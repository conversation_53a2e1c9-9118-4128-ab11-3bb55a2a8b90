# 指纹认证系统

一个完整的指纹认证解决方案，包含Android客户端和Spring Boot后端服务。

## 项目架构

```
智能缝纫系统指纹认证
├── Android客户端 (已有)
│   ├── 指纹绑定功能
│   ├── 指纹登录功能  
│   ├── 指纹解绑功能
│   └── 生物识别安全机制
└── Spring Boot后端 (新建)
    ├── 用户认证服务
    ├── 设备管理服务
    ├── Token加密服务
    └── 审计日志服务
```

## 技术栈

### 后端技术
- **Spring Boot 2.7.14** - 主框架
- **MyBatis-Plus 3.5.3.1** - ORM框架
- **MySQL 8.0** - 数据库
- **Druid 1.2.18** - 数据库连接池
- **JWT** - 令牌认证
- **Spring Security** - 安全框架
- **AES-GCM** - 数据加密

### 前端技术 (Android)
- **Android KeyStore** - 硬件安全模块
- **BiometricPrompt** - 生物识别API
- **AES加密** - 本地数据加密
- **OkHttp** - 网络请求
- **FastJSON** - JSON解析

## 核心功能

### 🔐 安全特性
- **硬件级安全**: 使用Android KeyStore保护密钥
- **指纹加密**: Token通过指纹验证后才能解密
- **双重验证**: 绑定时需要密码+指纹双重验证
- **设备绑定**: 每台设备独立绑定，互不影响
- **会话管理**: JWT访问令牌+刷新令牌机制

### 📱 用户体验
- **快速登录**: 指纹验证即可完成登录
- **设备管理**: 支持多设备绑定(最多5台)
- **异常处理**: 完善的错误提示和恢复机制
- **兼容性**: 支持Android 6.0+所有设备

## 数据库设计

### 核心表结构

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| `users` | 用户表 | user_id, password, status |
| `fingerprint_devices` | 指纹设备表 | user_id, device_id, encrypted_token |
| `fingerprint_auth_logs` | 认证日志表 | operation_type, result, client_ip |
| `refresh_tokens` | 刷新令牌表 | refresh_token, access_token, expires_at |

## API接口

### 核心接口
- `POST /api/fingerprint/bind` - 指纹绑定
- `POST /api/fingerprint/login` - 指纹登录  
- `POST /api/fingerprint/unbind` - 指纹解绑
- `POST /api/fingerprint/refresh` - 令牌刷新
- `GET /api/fingerprint/health` - 健康检查

详细API文档请参考 [API_DOCUMENTATION.md](backend/API_DOCUMENTATION.md)

## 部署指南

### 环境要求
- **JDK 8+**
- **MySQL 8.0+**
- **Maven 3.6+**

### 快速启动

1. **创建数据库**
```sql
CREATE DATABASE smart_sewing_fingerprint CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **导入表结构**
```bash
mysql -u root -p smart_sewing_fingerprint < database/init.sql
```

3. **修改配置**
编辑 `backend/src/main/resources/application.yml`:
```yaml
spring:
  datasource:
    url: ****************************************************
    username: your_username
    password: your_password
```

4. **启动服务**
```bash
cd backend
mvn spring-boot:run
```

5. **验证部署**
访问: http://localhost:8080/api/fingerprint/health

## 监控和运维

### 日志监控
- **应用日志**: `logs/application.log`
- **认证日志**: 数据库 `fingerprint_auth_logs` 表
- **SQL日志**: MyBatis-Plus控制台输出

### 性能监控
- **Druid监控**: http://localhost:8080/api/druid/
- **JVM监控**: 可集成Spring Boot Actuator
- **数据库监控**: 通过Druid实时监控SQL性能

### 安全运维
- **密钥轮换**: 定期更新JWT签名密钥
- **日志审计**: 监控异常登录行为
- **设备管理**: 支持远程解绑可疑设备

## 许可证

本项目采用 MIT 许可证

## 技术支持

如有问题，请提交Issue或联系开发团队：
- 📧 Email: <EMAIL>
- 🐛 Issues: GitHub Issues
- 📖 Wiki: 项目Wiki

