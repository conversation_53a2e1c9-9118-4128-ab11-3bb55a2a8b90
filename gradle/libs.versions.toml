[versions]
agp = "8.10.0"
unifiedPush = "3.0.10"
andserver = "2.1.12"
appDialog = "1.2.0"
biometric = "1.1.0"
commonsLang3 = "3.17.0"
eventbus = "3.3.1"
fastjson = "2.0.57"
glide = "4.16.0"
guavaRetrying = "2.0.0"
hanlp = "portable-1.8.4"
hivemqMqttClient = "1.3.7"
hutool = "5.8.39"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.6.1"
luban = "1.1.8"
material = "1.12.0"
kotlin = "2.2.0"
mlkitCommon = "2.3.0"
mlkitFaceDetection = "2.3.0"
mlkitBarcodeScanning = "2.3.0"
mlkitTextRecognition = "2.3.0"
coreKtx = "1.13.1"
mpandroidchart = "v3.1.0"
okhttp = "4.12.0"
processPhoenix = "3.0.0"
realm = "10.19.0"
rxjava = "3.1.10"
rxandroid = "3.0.2"
speech = "1.6.2"
usbSerialForAndroid = "3.9.0"
utilcodex = "1.31.1"
xcrashAndroidLib = "3.1.0"
yserialport = "2.2.8"
yutils = "2.2.5"


[libraries]
android-unifiedPush = { module = "com.github.UnifiedPush:android-connector", version.ref = "unifiedPush" }
andserver = { module = "com.yanzhenjie.andserver:api", version.ref = "andserver" }
andserver-processor = { module = "com.yanzhenjie.andserver:processor", version.ref = "andserver" }
app-dialog = { module = "com.github.jenly1314.AppUpdater:app-dialog", version.ref = "appDialog" }
biometric = { module = "androidx.biometric:biometric", version.ref = "biometric" }
commons-lang3 = { module = "org.apache.commons:commons-lang3", version.ref = "commonsLang3" }
eventbus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }
fastjson = { module = "com.alibaba:fastjson", version.ref = "fastjson" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
guava-retrying = { module = "com.github.rholder:guava-retrying", version.ref = "guavaRetrying" }
hanlp = { module = "com.hankcs:hanlp", version.ref = "hanlp" }
hivemq-mqtt-client = { module = "com.hivemq:hivemq-mqtt-client", version.ref = "hivemqMqttClient" }
hutool-all = { module = "cn.hutool:hutool-all", version.ref = "hutool" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
luban = { module = "top.zibin:Luban", version.ref = "luban" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
mlkit-common = { module = "com.github.jenly1314.MLKit:mlkit-common", version.ref = "mlkitCommon" }
mlkit-face-detection = { module = "com.github.jenly1314.MLKit:mlkit-face-detection", version.ref = "mlkitFaceDetection" }
mlkit-barcode-scanning = { module = "com.github.jenly1314.MLKit:mlkit-barcode-scanning", version.ref = "mlkitBarcodeScanning" }
mlkit-text-recognition = { module = "com.github.jenly1314.MLKit:mlkit-text-recognition", version.ref = "mlkitTextRecognition" }
core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
mpandroidchart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "mpandroidchart" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
process-phoenix = { module = "com.jakewharton:process-phoenix", version.ref = "processPhoenix" }

rxjava = { module = "io.reactivex.rxjava3:rxjava", version.ref = "rxjava" }
rxandroid = { module = "io.reactivex.rxjava3:rxandroid", version.ref = "rxandroid" }
#noinspection Aligned16KB
speech = { module = "net.gotev:speech", version.ref = "speech" }
utilcodex = { module = "com.blankj:utilcodex", version.ref = "utilcodex" }
#noinspection Aligned16KB
xcrash-android-lib = { module = "com.iqiyi.xcrash:xcrash-android-lib", version.ref = "xcrashAndroidLib" }
yserialport = { module = "com.kotlinx:yserialport", version.ref = "yserialport" }
yutils = { module = "com.kotlinx:yutils", version.ref = "yutils" }
usb-serial-for1-android = { module = "com.github.mik3y:usb-serial-for-android", version.ref = "usbSerialForAndroid" }
realm-gradle-plugin = { module = "io.realm:realm-gradle-plugin", version.ref = "realm" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }


