<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#40FFFFFF">
    
    <item android:id="@android:id/mask">
        <shape android:shape="oval">
            <solid android:color="@android:color/white" />
        </shape>
    </item>
    
    <item>
        <selector>
            <!-- 禁用状态 -->
            <item android:state_enabled="false">
                <shape android:shape="oval">
                    <gradient
                        android:startColor="#FFE0E0E0"
                        android:endColor="#FFBDBDBD"
                        android:angle="45" />
                    <size 
                        android:width="48dp" 
                        android:height="48dp" />
                </shape>
            </item>
            
            <!-- 正常状态 -->
            <item>
                <layer-list>
                    <!-- 阴影层 -->
                    <item android:top="3dp" android:left="3dp">
                        <shape android:shape="oval">
                            <solid android:color="#30000000" />
                            <size 
                                android:width="48dp" 
                                android:height="48dp" />
                        </shape>
                    </item>
                    <!-- 按钮主体 -->
                    <item android:bottom="3dp" android:right="3dp">
                        <shape android:shape="oval">
                            <gradient
                                android:startColor="#6200EE"
                                android:endColor="#3700B3"
                                android:angle="45" />
                            <size 
                                android:width="48dp" 
                                android:height="48dp" />
                        </shape>
                    </item>
                    <!-- 高光层 -->
                    <item android:bottom="3dp" android:right="3dp">
                        <shape android:shape="oval">
                            <gradient
                                android:startColor="#40FFFFFF"
                                android:endColor="#00FFFFFF"
                                android:angle="90" />
                            <size 
                                android:width="48dp" 
                                android:height="48dp" />
                        </shape>
                    </item>
                </layer-list>
            </item>
        </selector>
    </item>
    
</ripple> 