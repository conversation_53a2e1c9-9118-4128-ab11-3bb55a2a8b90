<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#E8F4FD" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="#576B95" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F9F9F9" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="#E5E5E5" />
        </shape>
    </item>
    
</selector>