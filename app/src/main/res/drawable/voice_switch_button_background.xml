<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#4CAF50" />
            <stroke
                android:width="2dp"
                android:color="#2E7D32" />
        </shape>
    </item>
    
    <!-- 选中状态（语音模式） -->
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="#FF5722" />
            <stroke
                android:width="2dp"
                android:color="#D84315" />
        </shape>
    </item>
    
    <!-- 默认状态（键盘模式） -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#2196F3" />
            <stroke
                android:width="2dp"
                android:color="#1976D2" />
        </shape>
    </item>
    
</selector> 