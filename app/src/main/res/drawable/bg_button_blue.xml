<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape>
            <solid android:color="#999999" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="#1976D2" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape>
            <solid android:color="#2196F3" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector> 