<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#4A90E2" />
            <corners android:radius="28dp" />
            <stroke
                android:width="2dp"
                android:color="#3A7BD5" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#576B95" />
            <corners android:radius="28dp" />
            <stroke
                android:width="1dp"
                android:color="#4A5A85" />
        </shape>
    </item>
    
</selector>