<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape>
            <solid android:color="#999999" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="#D32F2F" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape>
            <solid android:color="#F44336" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector> 