<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#FFEBEE" />
            <corners android:radius="8dp" />
            <stroke
                android:width="1dp"
                android:color="#F44336" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FAFAFA" />
            <corners android:radius="8dp" />
            <stroke
                android:width="1dp"
                android:color="#E0E0E0" />
        </shape>
    </item>
    
</selector>