<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="statusText"
            type="String" />

        <variable
            name="isAuthenticateEnabled"
            type="Boolean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="24dp">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="32dp"
            android:contentDescription="@string/fingerprint_icon_desc"
            android:src="@android:drawable/ic_dialog_info"
            tools:ignore="ImageContrastCheck" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/fingerprint_title"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:gravity="center"
            android:minHeight="60dp"
            android:text="@{statusText ?? @string/checking_fingerprint_support}"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnBind"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/button_primary"
                android:enabled="@{isAuthenticateEnabled ?? false}"
                android:onClick="doBind"
                android:text="@string/start_fingerprint_bind"
                tools:ignore="VisualLintButtonSize" />

            <Button
                android:id="@+id/btnLogin"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/button_primary"
                android:enabled="@{isAuthenticateEnabled ?? false}"
                android:onClick="doLogin"
                android:text="@string/start_fingerprint_login"
                tools:ignore="VisualLintButtonSize" />

            <Button
                android:id="@+id/btnUnbind"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/button_primary"
                android:enabled="@{isAuthenticateEnabled ?? false}"
                android:onClick="doUnbind"
                android:text="@string/start_fingerprint_unbind"
                tools:ignore="VisualLintButtonSize" />
        </LinearLayout>

        <Button
            android:id="@+id/btnClose"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="doClose"
            android:text="@string/close"
            android:textSize="16sp"
            tools:ignore="VisualLintButtonSize" />

    </LinearLayout>
</layout>