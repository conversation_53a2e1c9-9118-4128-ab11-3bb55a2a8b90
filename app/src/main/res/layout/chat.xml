<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.supreme.smart.sewing.voice.example.ChatViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F5F5F5"
        android:orientation="vertical"
        android:fitsSystemWindows="true">

        <!-- 标题栏 -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="#6200EE"
            android:elevation="4dp"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/voice_chat_title"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </androidx.appcompat.widget.Toolbar>

        <!-- 聊天消息列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvChatMessages"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipToPadding="false"
            android:padding="8dp"
            android:scrollbars="vertical" />

        <!-- 输入区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:elevation="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp"
            android:paddingBottom="16dp">

            <!-- 语音/键盘切换按钮 -->
            <ImageButton
                android:id="@+id/btnVoiceSwitch"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/circle_button_background"
                android:padding="8dp"
                android:scaleType="centerInside"
                android:src="@drawable/ic_voice"
                tools:ignore="ContentDescription,SpeakableTextPresentCheck,TouchTargetSizeCheck" />

            <!-- 输入框容器 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- 文字输入框 -->
                <EditText
                    android:id="@+id/etMessage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/chat_edit_text_background"
                    android:hint="@string/voice_input_message_hint"
                    android:maxHeight="120dp"
                    android:maxLines="5"
                    android:minHeight="40dp"
                    android:paddingStart="12dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="8dp"
                    android:scrollbars="vertical"
                    android:textSize="16sp"
                    android:visibility="visible"
                    tools:ignore="Autofill,TextFields,TouchTargetSizeCheck,VisualLintTextFieldSize" />

                <!-- 语音输入按钮（微信样式） -->
                <Button
                    android:id="@+id/btnVoiceHold"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@drawable/voice_hold_button_background"
                    android:text="@string/voice_hold_to_speak_btn"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 发送按钮 -->
            <ImageButton
                android:id="@+id/btnSend"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/chat_send_button_ripple"
                android:contentDescription="@string/voice_send_message_desc"
                android:elevation="2dp"
                android:enabled="false"
                android:padding="8dp"
                android:rotation="-45"
                android:scaleType="centerInside"
                android:src="@drawable/ic_send_arrow"
                android:stateListAnimator="@null"
                tools:ignore="TouchTargetSizeCheck" />

        </LinearLayout>

    </LinearLayout>

</layout>