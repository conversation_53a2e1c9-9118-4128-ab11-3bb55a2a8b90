<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black">

        <!-- 相机预览 -->
        <androidx.camera.view.PreviewView
            android:id="@+id/previewView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


        <!-- 闪光灯按钮 -->
        <ImageView
            android:id="@+id/ivFlashlight"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="top|end"
            android:layout_margin="20dp"
            android:background="@drawable/circle_button_background"
            android:contentDescription="@string/flashlight_desc"
            android:padding="12dp"
            android:src="@drawable/camera_scan_flashlight_selector"
            android:visibility="gone" />

        <!-- 右侧按钮区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical|start"
            android:orientation="vertical"
            android:background="@drawable/gradient_side_overlay"
            android:padding="16dp">

            <!-- 人脸注册按钮 -->
            <androidx.cardview.widget.CardView
                android:layout_width="120dp"
                android:layout_height="60dp"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="30dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="#FF4CAF50">

                <Button
                    android:id="@+id/btnRegisterFace"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent"
                    android:drawablePadding="4dp"
                    android:gravity="center"
                    android:onClick="onRegisterFaceClick"
                    android:text="@string/face_register"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:ignore="TextContrastCheck" />

            </androidx.cardview.widget.CardView>

            <!-- 关闭按钮 -->
            <androidx.cardview.widget.CardView
                android:layout_width="120dp"
                android:layout_height="60dp"
                app:cardCornerRadius="30dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@android:color/holo_orange_dark">

                <Button
                    android:id="@+id/btnClose"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent"
                    android:drawablePadding="4dp"
                    android:gravity="center"
                    android:onClick="onCloseClick"
                    android:text="@string/close"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:ignore="TextContrastCheck" />

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- 底部提示文本 -->
        <TextView
            android:id="@+id/tvHint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:textSize="14sp"
            android:text="@string/face_hint"
            android:textColor="#CCFFFFFF"
            android:gravity="center"
            android:background="@drawable/gradient_bottom_overlay"
            android:padding="16dp" />

    </FrameLayout>
</layout>