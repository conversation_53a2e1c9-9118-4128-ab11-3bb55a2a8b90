<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        
        <variable
            name="upgradeInfo"
            type="com.supreme.smart.sewing.update.UpgradeInfo" />
        <variable
            name="currentVersion"
            type="androidx.databinding.ObservableField&lt;String&gt;" />
        <variable
            name="downloadProgress"
            type="androidx.databinding.ObservableInt" />
        <variable
            name="downloadSpeed"
            type="androidx.databinding.ObservableField&lt;String&gt;" />
        <variable
            name="isDownloading"
            type="androidx.databinding.ObservableBoolean" />
        <variable
            name="showInstallButton"
            type="androidx.databinding.ObservableBoolean" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/update_background"
        android:fillViewport="true">
        
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">  <!-- 添加居中对齐 -->
        
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:maxWidth="480dp"
        android:minWidth="320dp"
        android:orientation="vertical"
        android:padding="16dp">



        <!-- 标题区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingBottom="12dp">

            <ImageView
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginBottom="8dp"
                android:src="@mipmap/ic_launcher"
                android:contentDescription="@string/app_name" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/update_available"
                android:textColor="@color/update_text_primary"
                android:textSize="20sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 版本信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardBackgroundColor="@color/update_card_background"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tvCurrentVersion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:text="@{@string/current_version(currentVersion)}"
                    android:textColor="@color/update_text_secondary"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tvNewVersion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:text="@{@string/new_version(upgradeInfo.versionName)}"
                    android:textColor="@color/update_primary"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvFileSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:text="@{@string/file_size(upgradeInfo.formattedFileSize)}"
                    android:textColor="@color/update_text_secondary"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 更新内容卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardBackgroundColor="@color/update_card_background"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/update_content"
                    android:textColor="@color/update_text_primary"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:scrollbars="none">
                    
                    <TextView
                        android:id="@+id/tvUpdateContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:lineSpacingExtra="2dp"
                        android:text="@{upgradeInfo.updateContent ?? @string/loading_update_content}"
                        android:textColor="@color/update_text_secondary"
                        android:textSize="13sp" />
                        
                </ScrollView>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 下载进度区域 -->
        <LinearLayout
            android:id="@+id/layoutProgress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:orientation="vertical"
            android:visibility="@{isDownloading ? View.VISIBLE : View.GONE}">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/update_card_background"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tvDownloadStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/downloading"
                        android:textColor="@color/update_text_primary"
                        android:textSize="15sp"
                        android:textStyle="bold" />

                    <ProgressBar
                        android:id="@+id/progressBar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginBottom="8dp"
                        android:max="100"
                        android:progress="@{downloadProgress}"
                        android:progressTint="@color/update_progress" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvProgress"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@{String.valueOf(downloadProgress) + `%`}"
                            android:textColor="@color/update_text_secondary"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/tvDownloadSpeed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{downloadSpeed ?? `0KB/s`}"
                            android:textColor="@color/update_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- 按钮区域 -->
        <LinearLayout
            android:id="@+id/layoutButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2"
            android:visibility="@{isDownloading ? View.GONE : View.VISIBLE}">

            <Button
                android:id="@+id/btnLater"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/update_later"
                android:textColor="@color/update_text_secondary"
                android:textSize="14sp"
                app:strokeColor="@color/update_divider" />

            <Button
                android:id="@+id/btnUpdate"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/update_primary"
                android:text="@string/update_now"
                android:textColor="@color/white"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 下载控制按钮 -->
        <LinearLayout
            android:id="@+id/layoutDownloadButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="@{isDownloading ? View.VISIBLE : View.GONE}"
            android:weightSum="2">

            <Button
                android:id="@+id/btnCancel"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/cancel_download"
                android:textColor="@color/update_error"
                android:textSize="14sp"
                app:strokeColor="@color/update_error" />

            <Button
                android:id="@+id/btnInstall"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/update_progress"
                android:text="@string/install_now"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="@{showInstallButton ? View.VISIBLE : View.GONE}" />

        </LinearLayout>

    </LinearLayout>
    </LinearLayout>
    </ScrollView>

</layout>