<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <!-- 可以在这里定义变量，后续可以添加状态绑定 -->
        <variable
            name="statusText"
            type="String" />

        <variable
            name="resultText"
            type="String" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#FAFAFA">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:gravity="center"
                android:text="@string/voice_to_text_title"
                android:textColor="#333333"
                android:textSize="24sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <!-- 状态显示 -->
            <TextView
                android:id="@+id/tv_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/bg_status_view"
                android:gravity="center"
                android:padding="20dp"
                android:text="@{statusText ?? @string/default_status_text}"
                android:textColor="#666666"
                android:textSize="16sp"
                android:visibility="gone" />

            <!-- 测试服务按钮 -->
            <Button
                android:id="@+id/btn_test_service"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_button_orange"
                android:elevation="2dp"
                android:text="@string/voice_to_text_test_whisper"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:visibility="gone" />

            <!-- 录音按钮 -->
            <Button
                android:id="@+id/btn_record"
                android:layout_width="match_parent"
                android:layout_height="70dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_button_green"
                android:elevation="2dp"
                android:enabled="false"
                android:text="@string/voice_to_text_start_record"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 停止录音按钮 -->
            <Button
                android:id="@+id/btn_stop"
                android:layout_width="match_parent"
                android:layout_height="70dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_button_red"
                android:elevation="2dp"
                android:enabled="false"
                android:text="@string/voice_to_text_stop_record"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 转录按钮 -->
            <Button
                android:id="@+id/btn_transcribe"
                android:layout_width="match_parent"
                android:layout_height="70dp"
                android:text="@string/voice_to_text_start_convert"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:background="@drawable/bg_button_blue"
                android:layout_marginBottom="16dp"
                android:elevation="2dp"
                android:enabled="false" />

            <!-- 强制中文转录按钮 -->
            <Button
                android:id="@+id/btn_chinese_transcribe"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:text="@string/voice_to_text_force_cn"
                android:textSize="16sp"
                android:textColor="@android:color/white"
                android:background="@drawable/bg_button_green"
                android:layout_marginBottom="16dp"
                android:elevation="2dp"
                android:enabled="false" />

            <!-- 测试API格式按钮 -->
            <Button
                android:id="@+id/btn_test_api"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:text="@string/voice_to_text_test_api"
                android:textSize="16sp"
                android:textColor="@android:color/white"
                android:background="@drawable/bg_button_purple"
                android:layout_marginBottom="16dp"
                android:elevation="2dp"
                android:enabled="false" />

            <!-- JSON格式转录按钮 -->
            <Button
                android:id="@+id/btn_json_transcribe"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/bg_button_grey"
                android:elevation="2dp"
                android:enabled="false"
                android:text="@string/voice_to_text_json_convert"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:visibility="gone" />

            <!-- 结果显示 -->
            <TextView
                android:id="@+id/tv_result"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/bg_result_view"
                android:gravity="top"
                android:minHeight="200dp"
                android:padding="20dp"
                android:scrollbars="vertical"
                android:text="@{resultText ?? @string/default_result_text}"
                android:textColor="#333333"
                android:textSize="16sp" />

        </LinearLayout>

    </ScrollView>

</layout> 