<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="32dp"
    android:background="@android:color/white"
    android:orientation="vertical"
    android:padding="24dp"
    android:elevation="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/dialog_voice_input_speak"
        android:textColor="#4CAF50"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 实时文本显示 -->
    <TextView
        android:id="@+id/tvRealTimeText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="60dp"
        android:background="#f5f5f5"
        android:padding="16dp"
        android:textSize="16sp"
        android:textColor="#333333"
        android:gravity="center"
        android:text="@string/dialog_voice_input_listening"
        android:layout_marginBottom="16dp" />

    <!-- 取消提示区域 -->
    <LinearLayout
        android:id="@+id/cancelHintArea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="#ffebee"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@android:drawable/ic_menu_delete"
            android:layout_marginEnd="8dp"
            app:tint="#f44336" />

        <TextView
            android:id="@+id/tvCancelHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/dialog_voice_input_cancel"
            android:textColor="#f44336"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 发送语音提示区域 -->
    <LinearLayout
        android:id="@+id/sendVoiceHintArea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="#e8f5e8"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_voice"
            android:layout_marginEnd="8dp"
            app:tint="#4CAF50" />

        <TextView
            android:id="@+id/tvSendVoiceHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/dialog_voice_input_send"
            android:textColor="#4CAF50"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 音频波形动画 -->
    <com.supreme.smart.sewing.voice.component.WaveformView
        android:id="@+id/waveformView"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_marginBottom="16dp" />

    <!-- 提示文字 -->
    <TextView
        android:id="@+id/tvBottomHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/dialog_voice_input_swipe_hint"
        android:textColor="#666666"
        android:textSize="14sp"
        android:layout_gravity="center" />

</LinearLayout>