<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- 消息气泡 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:background="@drawable/message_bubble"
        android:orientation="vertical"
        android:padding="12dp"
        android:layout_marginStart="60dp"
        android:maxWidth="280dp">

        <!-- 消息内容 -->
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/chat_text_sample"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:lineSpacingExtra="2dp" />

        <!-- 时间戳 -->
        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="4dp"
            android:text="@string/voice_item_time"
            android:textColor="#999999"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>