<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="30dp"
    android:background="@drawable/app_dialog_bg"
    android:orientation="vertical">
    <TextView
        android:id="@+id/tvDialogTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="10dp"
        android:lines="1"
        android:textSize="@dimen/app_dialog_title_text_size"
        android:textColor="@color/app_dialog_title_color"
        android:text="@string/app_dialog_title"/>
    <ImageView
        android:id="@+id/ivDialogContent"
        android:layout_width="match_parent"
        android:contentDescription="@null"
        android:layout_height="300dp"
        android:layout_marginLeft="6dp"
        android:layout_marginRight="6dp"
        android:layout_marginBottom="6dp"/>
    <include layout="@layout/app_dialog_line_h"/>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal">
        <Button
            android:id="@+id/btnDialogCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:layout_weight="1"
            android:text="@string/app_dialog_cancel"
            android:textSize="@dimen/app_dialog_button_text_size"
            android:textColor="@color/app_dialog_button_color_selector"
            android:background="?android:attr/selectableItemBackground"
            tools:ignore="VisualLintButtonSize" />
        <include
            android:id="@+id/line"
            layout="@layout/app_dialog_line_v"/>
        <Button
            android:id="@+id/btnDialogConfirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:layout_weight="1"
            android:text="@string/app_dialog_ok"
            android:textSize="@dimen/app_dialog_button_text_size"
            android:textColor="@color/app_dialog_button_color_selector"
            android:background="?android:attr/selectableItemBackground"
            tools:ignore="VisualLintButtonSize" />
    </LinearLayout>

</LinearLayout>