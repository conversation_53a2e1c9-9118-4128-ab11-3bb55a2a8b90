<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="sendData"
            type="String" />

        <variable
            name="receivedData"
            type="String" />

        <variable
            name="isConnected"
            type="boolean" />

        <variable
            name="isPolling"
            type="boolean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <EditText
            android:id="@+id/et_send_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autofillHints=""
            android:hint="@string/input_data_hint"
            android:inputType="text"
            android:visibility="gone"
            android:minHeight="48dp"
            android:padding="1dp"
            android:text="@{sendData ?? @string/default_send_data}"
            tools:ignore="VisualLintTextFieldSize" />

        <LinearLayout
            style="?android:attr/buttonBarStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_send"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:enabled="@{isConnected}"
                android:text="@string/send"
                tools:ignore="VisualLintButtonSize" />

            <Button
                android:id="@+id/btn_start_polling"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:enabled="@{isConnected &amp;&amp; !isPolling}"
                android:text="@string/start_polling"
                tools:ignore="VisualLintButtonSize" />

            <Button
                android:id="@+id/btn_stop_polling"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:enabled="@{isConnected &amp;&amp; isPolling}"
                android:text="@string/stop_polling"
                tools:ignore="VisualLintButtonSize" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/received_data_label"
                android:textStyle="bold" />

            <Button
                android:id="@+id/btn_clear_data"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/clear"
                android:textSize="12sp"
                android:minWidth="60dp"
                android:padding="8dp"
                tools:ignore="VisualLintButtonSize" />

        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_received_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/white"
                android:padding="8dp"
                android:text="@{receivedData}"
                android:textColor="@android:color/black" />
        </ScrollView>

    </LinearLayout>
</layout>