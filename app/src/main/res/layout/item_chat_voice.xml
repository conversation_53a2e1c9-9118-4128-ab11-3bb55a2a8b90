<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- 语音消息气泡 -->
    <LinearLayout
        android:id="@+id/voice_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:background="@drawable/voice_message_bubble"
        android:orientation="horizontal"
        android:padding="12dp"
        android:layout_marginStart="80dp"
        android:minWidth="120dp"
        android:gravity="center_vertical"
        android:clickable="true"
        android:focusable="true">

        <!-- 语音图标 -->
        <ImageView
            android:id="@+id/iv_voice_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_voice"
            android:layout_marginEnd="8dp"
            android:tint="@android:color/white" />

        <!-- 语音时长 -->
        <TextView
            android:id="@+id/tv_duration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/voice_item_seconds"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- 转换文字区域 -->
    <LinearLayout
        android:id="@+id/converted_text_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginTop="4dp"
        android:layout_marginStart="80dp"
        android:background="@drawable/converted_text_background"
        android:orientation="vertical"
        android:padding="8dp"
        android:visibility="gone">

        <!-- 转换状态提示 -->
        <TextView
            android:id="@+id/tv_convert_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/voice_item_converting"
            android:textColor="#666666"
            android:textSize="12sp"
            android:layout_marginBottom="4dp"
            android:visibility="gone" />

        <!-- 转换后的文字 -->
        <TextView
            android:id="@+id/tv_converted_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="#333333"
            android:textSize="14sp"
            android:lineSpacingExtra="2dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- 时间戳 -->
    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="12dp"
        android:text="@string/voice_item_time"
        android:textColor="#999999"
        android:textSize="12sp" />

</LinearLayout>