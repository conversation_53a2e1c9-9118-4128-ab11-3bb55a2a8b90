<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="selectedLanguage"
            type="String" />
        <variable
            name="isChinese"
            type="boolean" />
        <variable
            name="isEnglish"
            type="boolean" />
        <variable
            name="isVietnamese"
            type="boolean" />
        <variable
            name="isIndonesian"
            type="boolean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/white">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="24dp">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:text="@string/back"
            android:textSize="16sp"
            android:textColor="@color/black" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/language_settings"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:gravity="center" />

        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 语言选择区域 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/select_language"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:layout_marginBottom="16dp" />

    <RadioGroup
        android:id="@+id/radio_group_languages"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/rb_chinese"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/language_chinese"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:padding="12dp"
            android:layout_marginBottom="8dp"
            android:checked="@{isChinese}"
            android:background="@drawable/radio_button_background" />

        <RadioButton
            android:id="@+id/rb_english"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/language_english"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:padding="12dp"
            android:layout_marginBottom="8dp"
            android:checked="@{isEnglish}"
            android:background="@drawable/radio_button_background" />

        <RadioButton
            android:id="@+id/rb_vietnamese"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/language_vietnamese"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:padding="12dp"
            android:layout_marginBottom="8dp"
            android:checked="@{isVietnamese}"
            android:background="@drawable/radio_button_background" />

        <RadioButton
            android:id="@+id/rb_indonesian"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/language_indonesian"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:padding="12dp"
            android:layout_marginBottom="8dp"
            android:checked="@{isIndonesian}"
            android:background="@drawable/radio_button_background" />

    </RadioGroup>

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="32dp"
        android:gravity="center">

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="200dp"
            android:layout_height="48dp"
            android:text="@string/confirm"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:background="@drawable/button_primary"
            android:layout_marginEnd="16dp" />

    </LinearLayout>

    </LinearLayout>

</layout>