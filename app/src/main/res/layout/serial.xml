<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="sendData"
            type="String" />

        <variable
            name="receivedData"
            type="String" />

        <variable
            name="isConnected"
            type="boolean" />

        <variable
            name="isPolling"
            type="boolean" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">


        <EditText
            android:id="@+id/et_multiple_commands"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="8dp"
            android:autofillHints=""
            android:background="@android:drawable/edit_text"
            android:gravity="top"
            android:hint="输入多条命令，每行一条（十六进制）"
            android:inputType="textMultiLine"
            android:padding="8dp"
            android:scrollbars="vertical"
            android:text="@{sendData ?? @string/default_send_data_serial}"
            android:visibility="gone" />

        <LinearLayout
            style="?android:attr/buttonBarStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_send_multiple"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_weight="1"
                android:enabled="@{isConnected}"
                android:text="@string/send"
                android:textSize="16sp"
                tools:ignore="VisualLintButtonSize" />

            <Button
                android:id="@+id/btn_start_multiple_polling"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:enabled="@{isConnected &amp;&amp; !isPolling}"
                android:text="@string/start_polling"
                android:textSize="16sp"
                tools:ignore="VisualLintButtonSize" />

            <Button
                android:id="@+id/btn_stop_multiple_polling"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:enabled="@{isConnected &amp;&amp; isPolling}"
                android:text="@string/stop_polling"
                android:textSize="16sp"
                tools:ignore="VisualLintButtonSize" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/received_data_label"
                android:textStyle="bold" />

            <Button
                android:id="@+id/btn_clear_data"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="60dp"
                android:padding="8dp"
                android:text="@string/clear"
                android:textSize="16sp" />

        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_received_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/white"
                android:padding="8dp"
                android:text="@{receivedData}"
                android:textColor="@android:color/black" />
        </ScrollView>

    </LinearLayout>
</layout>