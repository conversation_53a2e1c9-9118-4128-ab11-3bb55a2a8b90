<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f8f8f8"
        android:fitsSystemWindows="true">

        <!-- 顶部工具栏 -->
        <LinearLayout
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/purple_500"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:id="@+id/btnBack"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:onClick="doBack"
                android:src="@android:drawable/ic_menu_revert"
                app:tint="@android:color/white"
                tools:ignore="ContentDescription,SpeakableTextPresentCheck,TouchTargetSizeCheck" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/voice_input_title"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 识别结果显示区域 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/bottomPanel"
            android:layout_below="@id/topBar"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/voice_recognition_result"
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/etResult"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/white"
                    android:elevation="2dp"
                    android:gravity="top|start"
                    android:hint="@string/voice_input_hint"
                    android:inputType="textMultiLine"
                    android:minHeight="120dp"
                    android:padding="16dp"
                    android:scrollbars="vertical"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    tools:ignore="Autofill,VisualLintTextFieldSize" />

            </LinearLayout>

        </ScrollView>

        <!-- 底部控制面板 -->
        <LinearLayout
            android:id="@+id/bottomPanel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@android:color/transparent"
            android:elevation="8dp"
            android:orientation="vertical">

            <!-- 状态提示文字 -->
            <TextView
                android:id="@+id/tvHint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="16dp"
                android:text="@string/voice_hold_to_speak"
                android:textColor="#666666"
                android:textSize="16sp"
                android:visibility="gone" />

            <!-- 录音按钮区域 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="120dp">

                <!-- 录音按钮 -->
                <ImageView
                    android:id="@+id/btnVoiceInput"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/voice_button_selector"
                    android:scaleType="center"
                    android:src="@android:drawable/ic_btn_speak_now"
                    app:tint="@android:color/white"
                    tools:ignore="ContentDescription" />

            </RelativeLayout>

        </LinearLayout>

    </RelativeLayout>
</layout>