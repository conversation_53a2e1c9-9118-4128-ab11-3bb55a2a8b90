<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Smart Sewing System</string>
    <string name="checking_fingerprint_support">Checking fingerprint support...</string>
    
    <!-- APP Update Related -->
    <string name="update_title">App Update</string>
    <string name="update_available">New Version Available</string>
    <string name="current_version">Current Version: %s</string>
    <string name="new_version">New Version: %s</string>
    <string name="update_content">Update Content</string>
    <string name="update_now">Update Now</string>
    <string name="update_later">Update Later</string>
    <string name="downloading">Downloading...</string>
    <string name="download_progress">Download Progress: %d%%</string>
    <string name="download_completed">Download Completed</string>
    <string name="download_failed">Download Failed</string>
    <string name="install_now">Install Now</string>
    <string name="checking_update">Checking for updates...</string>
    <string name="no_update">Already the latest version</string>
    <string name="network_error">Network connection failed</string>
    <string name="file_size">File Size: %s</string>
    <string name="download_speed">Download Speed: %s/s</string>
    <string name="cancel_download">Cancel Download</string>
    <string name="retry_download">Retry Download</string>
    <string name="update_install_prompt">Download completed, click to install</string>
    <string name="update_install_failed">Installation failed</string>
    <string name="update_permission_denied">Permission denied, unable to install</string>
    <string name="check_update">Check Update</string>
    <string name="loading_update_content">Loading update content...</string>
    
    <!-- Main Activity -->
    <string name="hint_input_text">Please enter text</string>
    <string name="voice_input">Voice Input</string>
    <string name="scan_qrcode">Scan QR Code</string>
    <string name="scan_barcode">Scan Barcode</string>
    <string name="ocr">OCR</string>
    <string name="face_recognition">Face Recognition</string>
    <string name="fingerprint_recognition">Fingerprint Recognition</string>
    <string name="standard_serial_port">Standard Serial Port</string>
    <string name="usb_serial_port">USB Serial Port</string>
    <string name="close">Close</string>
    
    <!-- Fingerprint Activity -->
    <string name="fingerprint_icon_desc">Fingerprint Icon</string>
    <string name="fingerprint_title">Fingerprint Recognition</string>
    <string name="start_fingerprint_auth">Start Fingerprint Authentication</string>
    
    <!-- Face Detection Activity -->
    <string name="flashlight_desc">Flashlight</string>
    <string name="face_register">Face\nRegistration</string>
    <string name="face_register_start">Registering face...</string>
    <string name="face_register_completed">Face registration completed</string>
    <string name="face_hint">Please keep your face within the frame, better results in good lighting</string>
    
    <!-- Serial Activity -->
    <string name="input_data_hint">Enter data to send (hexadecimal or text)</string>
    <string name="send">Send</string>
    <string name="start_polling">Start Polling</string>
    <string name="stop_polling">Stop Polling</string>
    <string name="clear">Clear</string>
    <string name="received_data_label">Received data:</string>
    <string name="receive_data_error">Data reception error: </string>
    <string name="input_valid_hex_data">Please enter valid hexadecimal data</string>
    
    <!-- Dialog strings (from existing resources) -->
    <string name="app_dialog_title">Notice</string>
    <string name="app_dialog_cancel">Cancel</string>
    <string name="app_dialog_ok">OK</string>
    
    <!-- Activity Toast and Status Messages -->
    <string name="fingerprint_auth_title">Fingerprint Authentication</string>
    <string name="fingerprint_auth_subtitle">Please place your finger on the fingerprint sensor</string>
    <string name="fingerprint_auth_description">Use your fingerprint for authentication</string>
    <string name="fingerprint_auth_cancel">Cancel</string>
    <string name="fingerprint_auth_success">Fingerprint authentication successful!</string>
    <string name="fingerprint_auth_failed">Fingerprint authentication failed, please try again</string>
    <string name="fingerprint_auth_error">Authentication error: %s</string>
    <string name="fingerprint_auth_prompt">Please perform fingerprint authentication...</string>
    <string name="fingerprint_support_available">Device supports fingerprint recognition, authentication available</string>
    <string name="fingerprint_no_hardware">Device does not support fingerprint recognition</string>
    <string name="fingerprint_hw_unavailable">Fingerprint recognition hardware currently unavailable</string>
    <string name="fingerprint_none_enrolled">No fingerprints enrolled, please enroll fingerprints in system settings first</string>
    <string name="fingerprint_security_update_required">Security update required</string>
    <string name="fingerprint_unknown_error">Unknown error</string>
    
    <!-- Serial Port Messages -->
    <string name="serial_init_success">Serial port initialization successful</string>
    <string name="serial_init_failed">Serial port initialization failed</string>
    <string name="send_success">Send successful</string>
    <string name="send_failed">Send failed</string>
    <string name="start_polling_msg">Start polling</string>
    <string name="stop_polling_msg">Stop polling</string>
    <string name="received_prefix">Received: </string>
    <string name="usb_device_not_found">USB device not found</string>
    <string name="usb_permission_granted">USB permission granted</string>
    <string name="usb_permission_denied">USB permission denied</string>
    <string name="usb_init_success">USB serial port initialization successful</string>
    <string name="usb_init_failed">USB serial port initialization failed</string>
    <string name="serial_error">Serial port error: %1$s</string>

    <!-- Log Messages -->
    <string name="log_sync_send_response">Sync send received response: %1$s</string>
    <string name="log_sync_send_failed">Sync send failed: %1$s</string>
    <string name="log_voice_results">=========== onResults = %1$s</string>
    <string name="log_voice_matches_size">=========== matches.size() = %1$d</string>
    <string name="log_voice_error">Voice recognition error: %1$d</string>
    <string name="log_audio_error">Audio error</string>
    <string name="log_client_error">Client error</string>
    <string name="log_insufficient_permissions">Insufficient permissions</string>
    <string name="log_network_error">Network error</string>
    <string name="log_no_match">No match found</string>
    <string name="log_recognizer_busy">Recognizer busy</string>
    <string name="log_server_error">Server error</string>
    <string name="log_speech_timeout">Speech input timeout</string>
    <string name="log_voice_ready">Ready to start voice recognition</string>
    <string name="log_voice_begin">Voice input detected</string>
    <string name="log_voice_end">Voice input ended</string>
    <string name="log_voice_start_failed">Voice recognition startup failed</string>
    <string name="log_fingerprint_auth_error">Authentication error: %1$s</string>
    <string name="log_fingerprint_auth_success">Fingerprint authentication successful</string>
    <string name="log_fingerprint_auth_failed">Fingerprint authentication failed</string>
    <string name="log_camera_size_updated">Camera size updated to: %1$dx%2$d</string>
    <string name="log_upload_failed">Upload failed: %1$s</string>
    <string name="log_response_content">Response content: %1$s</string>


    <!-- Language Settings -->
    <string name="language_settings">Language Settings</string>
    <string name="select_language">Select Language</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_vietnamese">Tiếng Việt</string>
    <string name="language_indonesian">Bahasa Indonesia</string>
    <string name="back">Back</string>
    <string name="confirm">Confirm</string>

    <!-- Voice Input Activity -->
    <string name="voice_input_title">Voice Input (Dialog Version)</string>
    <string name="voice_recognition_result">Recognition Result</string>
    <string name="voice_input_hint">Hold the button below to start voice input...</string>
    <string name="voice_hold_to_speak">Hold to speak</string>
    <string name="voice_convert_success">Voice to text conversion completed</string>
    <string name="voice_no_speech_detected">No speech detected</string>
    <string name="voice_input_cancelled">Voice input cancelled</string>
    <string name="voice_recognition_error">Voice recognition error: </string>

    <!-- Chat Activity -->
    <string name="voice_chat_title">Voice Chat Example</string>
    <string name="voice_input_message_hint">Enter message</string>
    <string name="voice_hold_to_speak_btn">Hold to speak</string>
    <string name="voice_send_message_desc">Send message</string>

    <string name="toast_permission_granted_success">Permission granted successfully</string>
    <string name="toast_missing_essential_permissions">Missing essential permissions:</string>
    <string name="toast_please_enable_manually_in_settings">Please enable manually in settings</string>
    <string name="toast_optional_permissions_denied">Optional permissions denied</string>

    <string name="dialog_power_management_settings_title">Power Management Permission Settings</string>
    <string name="dialog_power_management_settings_message_prefix">To ensure the application can receive push messages even when exited, the following permissions need to be set:\n\n</string>
    <string name="dialog_power_management_settings_message_suffix">\nThese settings can prevent the system from killing push services, ensuring timely message reception.</string>
    <string name="dialog_power_management_settings_positive_button">Set Now</string>
    <string name="dialog_power_management_settings_negative_button">Set Later</string>

    <string name="dialog_battery_optimization_settings_title">Battery Optimization Settings</string>
    <string name="dialog_battery_optimization_settings_message">To ensure the application can receive messages normally in the background, it needs to be added to the battery optimization whitelist.\n\nAfter clicking "Go to Settings":\n1. Find "Smart Sewing System" on the pop-up page\n2. Select "Don't optimize" or "Allow"\n3. Return to the application to continue setting</string>
    <string name="dialog_battery_optimization_positive_button">Go to Settings</string>
    <string name="dialog_battery_optimization_negative_button">Skip</string>

    <string name="dialog_manual_setup_guide_title">Manual Setup Guide</string>
    <string name="dialog_manual_setup_guide_message">Please follow these steps to manually set battery optimization:\n\nMethod 1:\n1. Open phone "Settings"\n2. Find "Battery" or "Power Management"\n3. Find "Battery Optimization" or "App Power Consumption Management"\n4. Find "Smart Sewing System" and set it to "Don't optimize"\n\nMethod 2:\n1. Open phone "Settings"\n2. Find "App Management" or "App Info"\n3. Find "Smart Sewing System"\n4. Find the "Battery" option in the app details\n5. Turn off "Battery Optimization" or set to "Unlimited"</string>
    <string name="dialog_manual_setup_guide_positive_button">Got it</string>
    <string name="dialog_manual_setup_guide_negative_button">Try again</string>

    <string name="dialog_autostart_settings_title">Autostart Permission Settings</string>
    <string name="dialog_autostart_settings_message">To ensure the push service can run even after the application exits, please enable the application's autostart permission.\n\nSettings location for different phone brands:\n• Xiaomi: Security Center > App Management > Autostart Management\n• Huawei: Phone Manager > App Launch Management\n• OPPO/OnePlus: Settings > App Management > Autostart\n• vivo: iManager > App Management > Autostart\n• Others: Settings > App Management > Autostart Management</string>
    <string name="dialog_autostart_positive_button">Go to Settings</string>
    <string name="dialog_autostart_negative_button">Skip</string>
    <string name="toast_set_autostart_hint">Please set "%1$s" to allow autostart</string>
    <string name="toast_manual_autostart_hint">Please manually enable autostart permission in settings</string>

    <string name="toast_set_battery_or_background_hint">Please find battery or background running settings in app info</string>
    <string name="toast_manual_background_run_hint">Please manually allow app to run in background in settings</string>
    <string name="toast_power_management_setup_completed">Power management permission setup completed!</string>
    <string name="toast_power_management_partially_granted">Some permissions still need to be set manually</string>

    <string name="dialog_permission_reminder_title">Permission Reminder</string>
    <string name="dialog_permission_reminder_message_prefix">The following permissions are not set, which may affect message reception:\n\n</string>
    <string name="dialog_permission_reminder_message_suffix">\n\nSet now?</string>
    <string name="dialog_permission_reminder_positive_button">Set Now</string>
    <string name="dialog_permission_reminder_negative_button">Remind Me Later</string>

    <string name="dialog_startup_management_settings_title">Startup Management Permission Settings</string>
    <string name="dialog_startup_management_settings_positive_button">Go to Settings</string>
    <string name="dialog_startup_management_settings_negative_button">Set Later</string>

    <string name="dialog_startup_management_confirm_title">Setup Confirmation</string>
    <string name="dialog_startup_management_confirm_message">Have you completed the startup management settings as instructed?\n\n✓ Turn off "Auto Management"\n✓ Enable "Allow Autostart"\n✓ Enable "Allow Associated Start"\n✓ Enable "Allow Background Activity"\n\nAfter selecting "Yes", the system will no longer remind you.</string>
    <string name="dialog_startup_management_confirm_positive_button">Yes, setup completed</string>
    <string name="dialog_startup_management_confirm_negative_button">Not yet, remind me later</string>
    <string name="toast_startup_management_completed">Startup management permission setup completed!</string>
    <string name="toast_startup_management_reset">Startup management permission status has been reset</string>











    <!-- Permission display names -->
    <string name="permission_name_record_audio">Microphone</string>
    <string name="permission_name_camera">Camera</string>
    <string name="permission_name_storage">Storage</string>
    <string name="permission_name_notification">Notification</string>
    <string name="permission_name_bluetooth">Bluetooth</string>
    <string name="permission_name_location">Location</string>

    <!-- Power management permission dialog -->
    <string name="dialog_power_management_title">Power Management Permission Settings</string>
    <string name="dialog_power_management_message">To ensure the app can receive push messages even when exited, the following permissions need to be set:\n\n%1$s\n\nThese settings prevent the system from killing the push service and ensure timely message reception.</string>
    <string name="dialog_power_management_positive">Set Now</string>
    <string name="dialog_power_management_negative">Set Later</string>

    <!-- Battery optimization dialog -->
    <string name="dialog_battery_optimization_title">Battery Optimization Settings</string>
    <string name="dialog_battery_optimization_message">To ensure the app can receive messages normally in the background, it needs to be added to the battery optimization whitelist.\n\nAfter clicking \"Go to Settings\":\n1. Find \"Smart Sewing System\" in the popup page\n2. Select \"Don\'t optimize\" or \"Allow\"\n3. Return to the app to continue setup</string>
    <string name="dialog_battery_optimization_positive">Go to Settings</string>
    <string name="dialog_battery_optimization_negative">Skip</string>

    <!-- Battery optimization manual guide -->
    <string name="dialog_battery_manual_title">Manual Setup Guide</string>
    <string name="dialog_battery_manual_message">Please follow these steps to manually set battery optimization:\n\nMethod 1:\n1. Open phone \"Settings\"\n2. Find \"Battery\" or \"Power Management\"\n3. Find \"Battery Optimization\" or \"App Power Management\"\n4. Find \"Smart Sewing System\" and set to \"Don\'t optimize\"\n\nMethod 2:\n1. Open phone \"Settings\"\n2. Find \"App Management\" or \"App Info\"\n3. Find \"Smart Sewing System\"\n4. Find \"Battery\" option in app details\n5. Turn off \"Battery Optimization\" or set to \"Unrestricted\"</string>
    <string name="dialog_battery_manual_positive">I understand</string>
    <string name="dialog_battery_manual_negative">Try again</string>

    <!-- Auto-start permission dialog -->
    <string name="dialog_autostart_title">Auto-start Permission Settings</string>
    <string name="dialog_autostart_message">To ensure the push service can still run after the app exits, please enable the app\'s auto-start permission.\n\nSettings location for different phone brands:\n• Xiaomi: Security Center > App Management > Auto-start Management\n• Huawei: Phone Manager > App Launch Management\n• OPPO/OnePlus: Settings > App Management > Auto-start\n• vivo: i Manager > App Management > Auto-start\n• Others: Settings > App Management > Auto-start Management</string>
    <string name="dialog_autostart_positive">Go to Settings</string>
    <string name="dialog_autostart_negative">Skip</string>

    <!-- Background run permission dialog -->
    <string name="dialog_background_run_title">Background Run Permission Settings</string>
    <string name="dialog_background_run_message">To ensure the push service can run continuously in the background, please allow the app to run in the background.\n\nSetup method:\n• Find \"Battery\" or \"Background Run\" option in app info page\n• Set to \"Unrestricted\" or \"Allow background run\"\n• Turn off \"Adaptive Battery\" restrictions for this app</string>
    <string name="dialog_background_run_positive">Go to Settings</string>
    <string name="dialog_background_run_negative">Complete</string>

    <!-- Permission reminder dialog -->
    <string name="dialog_permission_reminder_title">Permission Reminder</string>
    <string name="dialog_permission_reminder_message">The following permissions are not set and may affect message reception:\n\n%1$s\n\nSet them now?</string>
    <string name="dialog_permission_reminder_positive">Set Now</string>
    <string name="dialog_permission_reminder_negative">Remind Later</string>

    <!-- Startup management settings dialog -->
    <string name="dialog_startup_management_title">Startup Management Permission Settings</string>
    <string name="dialog_startup_management_positive">Go to Settings</string>
    <string name="dialog_startup_management_negative">Set Later</string>

    <!-- Setup completion confirmation dialog -->
    <string name="dialog_setup_confirmation_title">Setup Completion Confirmation</string>
    <string name="dialog_setup_confirmation_message">Have you completed the startup management settings as instructed?\n\n✓ Turn off automatic management\n✓ Allow auto-start\n✓ Allow associated startup\n✓ Allow background activity\n\nAfter selecting \"Yes\", the system will no longer remind you of this setting.</string>
    <string name="dialog_setup_confirmation_positive">Yes, setup complete</string>
    <string name="dialog_setup_confirmation_negative">Not yet, remind later</string>

    <!-- Toast hint messages -->
    <string name="toast_find_smart_sewing_system">Please find \"Smart Sewing System\" and set to \"Don\'t optimize\"</string>
    <string name="toast_find_battery_optimization">Please find battery optimization settings in app info</string>
    <string name="toast_allow_autostart">Please set \"Smart Sewing System\" to allow auto-start</string>
    <string name="toast_find_autostart_settings">Please find auto-start related settings in app info</string>
    <string name="toast_manual_autostart">Please manually enable app auto-start permission in settings</string>
    <string name="toast_find_battery_background">Please find battery or background run settings in app info</string>
    <string name="toast_manual_background_run">Please manually allow app background run in settings</string>
    <string name="toast_set_power_management">Please set appropriate permissions for the app in power management</string>
    <string name="toast_find_power_management">Please find power management related options in settings</string>
    <string name="toast_manual_system_settings">Please manually open system settings for configuration</string>
    <string name="toast_opening_battery_optimization">Opening battery optimization settings...</string>
    <string name="toast_device_not_support_direct">Device does not support direct battery optimization setting, please set manually</string>
    <string name="toast_battery_optimization_test_failed">Battery optimization permission test failed: %1$s</string>
    <string name="toast_no_battery_optimization_needed">Current Android version does not need battery optimization permission</string>
    <string name="toast_opening_battery_list">Opening battery optimization settings list</string>
    <string name="toast_device_not_support_battery">Device does not support battery optimization settings</string>
    <string name="toast_all_battery_methods_failed">All battery optimization setting methods failed</string>
    <string name="toast_set_all_power_permissions_hint">Please set all related permissions in app info</string>
    <string name="toast_manual_background_run_hint">Please manually allow app background run in settings</string>
    <string name="toast_set_all_power_permissions_hint_detailed">Please set \'Battery Optimization\', \'Background Run\', and \'Auto-start\' to allow/unrestricted in app info</string>
    <string name="toast_startup_management_reset">Startup management permission setting status has been reset</string>
    <string name="toast_background_dialog_reset">Background permission dialog setting status has been reset</string>

    <!-- Power management permission status -->
    <string name="power_permission_battery_whitelist">Battery optimization whitelist</string>
    <string name="power_permission_autostart">Boot auto-start</string>
    <string name="power_permission_background_run">Background run</string>

    <!-- Permission related Toast messages -->
    <string name="toast_permission_granted_success">Permission granted successfully</string>
    <string name="toast_missing_essential_permissions">Missing essential permissions: %1$s\nPlease enable manually in settings</string>
    <string name="toast_battery_optimization_success">Battery optimization setting successful!</string>
    <string name="toast_manual_battery_optimization">Please manually set battery optimization whitelist</string>
    <string name="toast_power_management_complete">Power management permissions setup complete!</string>
    <string name="toast_partial_permissions_manual">Some permissions still need manual setup</string>
    <string name="toast_power_management_complete_background">Power management permissions setup complete, app can run normally in background</string>
    <string name="toast_manual_remaining_permissions">Please manually complete remaining permission settings for best experience</string>
    <string name="toast_background_permission_already_set">Background permissions already set</string>
    <string name="toast_startup_management_complete">Startup management permissions setup complete!</string>

    <string name="power_status_storage_permission">Storage Permission: </string>
    <string name="power_status_notification_permission">Notification Permission: </string>
    <string name="power_status_battery_optimization_whitelist">Battery Optimization Whitelist: </string>
    <string name="power_status_joined">Joined</string>
    <string name="power_status_not_joined">Not Joined</string>
    <string name="power_status_autostart_permission">Autostart Permission: </string>
    <string name="power_status_granted">Granted</string>
    <string name="power_status_not_granted">Not Granted</string>
    <string name="power_status_background_run_permission">Background Run Permission: </string>
    <string name="power_status_allowed">Allowed</string>
    <string name="power_status_restricted">Restricted</string>
    <string name="power_status_doze_mode">Doze Mode Status: </string>
    <string name="power_status_entered">Entered</string>
    <string name="power_status_normal">Normal</string>
    <string name="power_status_doze_check_failed">Doze Mode Status: Check Failed</string>
    <string name="power_status_overall_status">Overall Status: </string>
    <string name="power_status_good">Good</string>
    <string name="power_status_needs_optimization">Needs Optimization</string>
    <string name="power_status_permissions_to_set">Permissions to Set: </string>

</resources>