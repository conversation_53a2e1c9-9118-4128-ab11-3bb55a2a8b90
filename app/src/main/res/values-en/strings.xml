<resources>
    <string name="app_name">SmartSewingSystem</string>
    <string name="checking_fingerprint_support">Checking fingerprint support...</string>
    
    <!-- APP Update Related -->
    <string name="update_title">App Update</string>
    <string name="update_available">New version found</string>
    <string name="current_version">Current version: %s</string>
    <string name="new_version">Update version: %s</string>
    <string name="update_content">Update content</string>
    <string name="update_now">Update now</string>
    <string name="update_later">Update later</string>
    <string name="downloading">Downloading…</string>
    <string name="download_progress">Download progress: %d%%</string>
    <string name="download_completed">Download completed</string>
    <string name="download_failed">Download failed</string>
    <string name="install_now">Install now</string>
    <string name="checking_update">Checking for updates...</string>
    <string name="no_update">Already the latest version</string>
    <string name="network_error">Network connection failed</string>
    <string name="file_size">File size: %s</string>
    <string name="download_speed">Download speed: %s/s</string>
    <string name="cancel_download">Cancel download</string>
    <string name="retry_download">Retry download</string>
    <string name="update_install_prompt">Download completed, click to install</string>
    <string name="update_install_failed">Installation failed</string>
    <string name="update_permission_denied">Permission denied, cannot install</string>
    <string name="check_update">Check for updates</string>
    <string name="text_command">Control command (hold to speak)</string>
    <string name="text_chat">Voice conversation</string>
    <string name="loading_update_content">Loading update content...</string>
    
    <!-- Main Activity -->
    <string name="hint_input_text">Please enter text</string>
    <string name="voice_input">Voice input</string>
    <string name="scan_qrcode">Scan QR code</string>
    <string name="scan_barcode">Scan code</string>
    <string name="ocr">OCR</string>
    <string name="face_recognition">Face recognition</string>
    <string name="fingerprint_recognition">Fingerprint recognition</string>
    <string name="standard_serial_port">Standard serial port</string>
    <string name="usb_serial_port">USB serial port</string>
    <string name="close">Close</string>
    
    <!-- Fingerprint Activity -->
    <string name="fingerprint_icon_desc">Fingerprint icon</string>
    <string name="fingerprint_title">Fingerprint Recognition</string>
    <string name="start_fingerprint_auth">Start fingerprint authentication</string>
    <string name="start_fingerprint_bind">Fingerprint binding</string>
    <string name="start_fingerprint_unbind">Fingerprint unbinding</string>
    <string name="start_fingerprint_login">Fingerprint login</string>

    <!-- Face Detection Activity -->
    <string name="flashlight_desc">Flashlight</string>
    <string name="face_register">Face\nRegistration</string>
    <string name="face_register_start">Registering face</string>
    <string name="face_register_completed">Face registration completed</string>
    <string name="face_hint">Please keep your face within the frame, better results in good lighting</string>

    <!-- Serial Activity -->
    <string name="input_data_hint">Enter data to send (hexadecimal)</string>
    <string name="send">Send</string>
    <string name="start_polling">Start polling</string>
    <string name="stop_polling">Stop polling</string>
    <string name="clear">Clear</string>
    <string name="received_data_label">Received data:</string>
    <string name="receive_data_error">Data reception error: </string>
    <string name="input_valid_hex_data">Please enter valid hexadecimal data</string>
    
    <!-- Modbus Activity -->
    <string name="default_send_data" translatable="false">点击发送按钮执行琦星数据读取</string>
    <string name="received_prefix">Received: </string>
    
    <!-- Dialog strings (from existing resources) -->
    <string name="app_dialog_title">Notice</string>
    <string name="app_dialog_cancel">Cancel</string>
    <string name="app_dialog_ok">OK</string>
    
    <!-- Activity Toast and Status Messages -->
    <string name="fingerprint_auth_title">Fingerprint Authentication</string>
    <string name="fingerprint_auth_subtitle">Please place your finger on the fingerprint sensor</string>
    <string name="fingerprint_auth_description">Use your fingerprint for authentication</string>
    <string name="fingerprint_auth_cancel">Cancel</string>
    <string name="fingerprint_auth_success">Fingerprint authentication successful!</string>
    <string name="fingerprint_auth_failed">Fingerprint authentication failed, please try again</string>
    <string name="fingerprint_auth_error">Authentication error: %s</string>
    <string name="fingerprint_auth_prompt">Please perform fingerprint authentication...</string>
    <string name="fingerprint_support_available">Device supports fingerprint recognition, authentication available</string>
    <string name="fingerprint_no_hardware">Device does not support fingerprint recognition</string>
    <string name="fingerprint_hw_unavailable">Fingerprint recognition hardware currently unavailable</string>
    <string name="fingerprint_none_enrolled">No fingerprints enrolled, please register fingerprints in system settings first</string>
    <string name="fingerprint_security_update_required">Security update required</string>
    <string name="fingerprint_unknown_error">Unknown error</string>
    
    <!-- Serial Port Messages -->
    <string name="serial_init_success">Serial port initialization successful</string>
    <string name="serial_init_failed">Serial port initialization failed</string>
    <string name="send_success">Send successful</string>
    <string name="send_failed">Send failed</string>
    <string name="start_polling_msg">Start polling</string>
    <string name="stop_polling_msg">Stop polling</string>
    <string name="usb_device_not_found">USB device not found</string>
    <string name="usb_permission_granted">USB permission granted</string>
    <string name="usb_permission_denied">USB permission denied</string>
    <string name="usb_init_success">USB serial port initialization successful</string>
    <string name="usb_init_failed">USB serial port initialization failed</string>
    <string name="serial_error">Serial port error: %1$s</string>

    <!-- Log Messages -->
    <string name="log_sync_send_response">Sync send received response: %1$s</string>
    <string name="log_sync_send_failed">Sync send failed: %1$s</string>
    <string name="log_voice_results">=========== onResults = %1$s</string>
    <string name="log_voice_matches_size">=========== matches.size() = %1$d</string>
    <string name="log_voice_error">Voice recognition error: %1$d</string>
    <string name="log_audio_error">Audio error</string>
    <string name="log_client_error">Client error</string>
    <string name="log_insufficient_permissions">Insufficient permissions</string>
    <string name="log_network_error">Network error</string>
    <string name="log_no_match">No match found</string>
    <string name="log_recognizer_busy">Recognizer busy</string>
    <string name="log_server_error">Server error</string>
    <string name="log_speech_timeout">No speech input timeout</string>
    <string name="log_voice_ready">Ready to start voice recognition</string>
    <string name="log_voice_begin">Voice start detected</string>
    <string name="log_voice_end">Voice end detected</string>
    <string name="log_voice_start_failed">Voice recognition startup failed</string>
    <string name="log_fingerprint_auth_error">Authentication error: %1$s</string>
    <string name="log_fingerprint_auth_success">Fingerprint authentication successful</string>
    <string name="log_fingerprint_auth_failed">Fingerprint authentication failed</string>
    <string name="log_camera_size_updated">Camera size updated to: %1$dx%2$d</string>
    <string name="log_upload_failed">Upload failed: %1$s</string>
    <string name="log_response_content">Response content: %1$s</string>

    <!-- Default Values -->
    <string name="default_send_data_serial" translatable="false">0203083600056794\n020300010001D5F9\n0203085200026789</string>

    <!-- Language Settings -->
    <string name="language_settings">Language Settings</string>
    <string name="select_language">Select Language</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_vietnamese">Tiếng Việt</string>
    <string name="language_indonesian">Bahasa Indonesia</string>
    <string name="back">Back</string>
    <string name="confirm">Confirm</string>

    <!-- Voice to Text Activity -->
    <string name="default_status_text">Checking permissions…</string>
    <string name="default_result_text">Transcription results will be displayed here…</string>

    <!-- Voice Input Activity -->
    <string name="voice_input_title">Voice Input (Dialog Version)</string>
    <string name="voice_recognition_result">Recognition Result</string>
    <string name="voice_input_hint">Hold the button below to start voice input…</string>
    <string name="voice_hold_to_speak">Hold to speak</string>
    <string name="voice_convert_success">Voice to text completed</string>
    <string name="voice_no_speech_detected">No speech detected</string>
    <string name="voice_input_cancelled">Voice input cancelled</string>
    <string name="voice_recognition_error">Voice recognition error: </string>

    <!-- Chat Activity -->
    <string name="voice_chat_title">Voice Chat Example</string>
    <string name="voice_input_message_hint">Enter message</string>
    <string name="voice_hold_to_speak_btn">Hold to Speak</string>
    <string name="voice_send_message_desc">Send message</string>

    <string name="toast_missing_essential_permissions">Missing essential permissions:</string>
    <string name="toast_please_enable_manually_in_settings">Please enable manually in settings</string>
    <string name="toast_optional_permissions_denied">Optional permissions denied</string>

    <string name="dialog_power_management_settings_title">Power Management Permission Settings</string>
    <string name="dialog_power_management_settings_message_prefix">To ensure the app can receive push messages even when exited, the following permissions need to be set:\n\n</string>
    <string name="dialog_power_management_settings_message_suffix">\n\nThese settings prevent the system from killing the push service and ensure timely message reception.</string>
    <string name="dialog_power_management_settings_positive_button">Set Now</string>
    <string name="dialog_power_management_settings_negative_button">Set Later</string>

    <string name="dialog_battery_optimization_settings_title">Battery Optimization Settings</string>
    <string name="dialog_battery_optimization_settings_message">To ensure the app can receive messages normally in the background, it needs to be added to the battery optimization whitelist.\n\nAfter clicking \"Go to Settings\":\n1. Find \"Smart Sewing System\" in the popup page\n2. Select \"Don\'t optimize\" or \"Allow\"\n3. Return to the app to continue setup</string>
    <string name="dialog_battery_optimization_positive_button">Go to Settings</string>
    <string name="dialog_battery_optimization_negative_button">Skip</string>

    <string name="dialog_manual_setup_guide_title">Manual Setup Guide</string>
    <string name="dialog_manual_setup_guide_message">Please follow these steps to manually set battery optimization:\n\nMethod 1:\n1. Open phone \"Settings\"\n2. Find \"Battery\" or \"Power Management\"\n3. Find \"Battery Optimization\" or \"App Power Management\"\n4. Find \"Smart Sewing System\" and set to \"Don\'t optimize\"\n\nMethod 2:\n1. Open phone \"Settings\"\n2. Find \"App Management\" or \"App Info\"\n3. Find \"Smart Sewing System\"\n4. Find \"Battery\" option in app details\n5. Turn off \"Battery Optimization\" or set to \"Unrestricted\"</string>
    <string name="dialog_manual_setup_guide_positive_button">I understand</string>
    <string name="dialog_manual_setup_guide_negative_button">Try again</string>

    <string name="dialog_autostart_settings_title">Auto-start Permission Settings</string>
    <string name="dialog_autostart_settings_message">To ensure the push service can still run after the app exits, please enable the app\'s auto-start permission.\n\nSettings location for different phone brands:\n• Xiaomi: Security Center > App Management > Auto-start Management\n• Huawei: Phone Manager > App Launch Management\n• OPPO/OnePlus: Settings > App Management > Auto-start\n• vivo: i Manager > App Management > Auto-start\n• Others: Settings > App Management > Auto-start Management</string>
    <string name="dialog_autostart_positive_button">Go to Settings</string>
    <string name="dialog_autostart_negative_button">Skip</string>
    <string name="toast_set_autostart_hint">Please set \"%1$s\" to allow auto-start</string>
    <string name="toast_manual_autostart_hint">Please manually enable app auto-start permission in settings</string>

    <string name="toast_set_battery_or_background_hint">Please find battery or background run settings in app info</string>
    <string name="toast_manual_background_run_hint">Please manually allow app background run in settings</string>
    <string name="toast_power_management_setup_completed">Power management permission setup completed!</string>
    <string name="toast_power_management_partially_granted">Some permissions still need manual setup</string>

    <string name="dialog_permission_reminder_title">Permission Reminder</string>
    <string name="dialog_permission_reminder_message_prefix">The following permissions are not set and may affect message reception:\n\n</string>
    <string name="dialog_permission_reminder_message_suffix">\n\nSet them now?</string>
    <string name="dialog_permission_reminder_positive_button">Set Now</string>
    <string name="dialog_permission_reminder_negative_button">Remind Later</string>

    <string name="dialog_startup_management_settings_title">Startup Management Permission Settings</string>
    <string name="dialog_startup_management_settings_positive_button">Go to Settings</string>
    <string name="dialog_startup_management_settings_negative_button">Set Later</string>

    <string name="dialog_startup_management_confirm_title">Setup Completion Confirmation</string>
    <string name="dialog_startup_management_confirm_message">Have you completed the startup management settings as instructed?\n\n✓ Turn off \"Automatic Management\"\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"\n\nAfter selecting \"Yes\", the system will no longer remind you of this setting.</string>
    <string name="dialog_startup_management_confirm_positive_button">Yes, setup complete</string>
    <string name="dialog_startup_management_confirm_negative_button">Not yet, remind later</string>
    <string name="toast_startup_management_completed">Startup management permission setup completed!</string>
    <string name="toast_startup_management_reset">Startup management permission setting status has been reset</string>

    <string name="toast_background_permission_setup_completed">Power management permission setup completed, app can run normally in background</string>
    <string name="toast_partial_power_permission_denied">Some power management permissions not set: %1$s</string>
    <string name="toast_background_permission_dialog_reset">Background permission dialog setting status has been reset</string>
    <string name="toast_set_all_power_permissions_hint">Please set \'Battery Optimization\' \'Background Run\' \'Auto-start\' to allowed/unrestricted in app info</string>

    <string name="power_status_storage_permission">Storage Permission: </string>
    <string name="power_status_notification_permission">Notification Permission: </string>
    <string name="power_status_battery_optimization_whitelist">Battery Optimization Whitelist: </string>
    <string name="power_status_joined">Joined</string>
    <string name="power_status_not_joined">Not Joined</string>
    <string name="power_status_autostart_permission">Auto-start Permission: </string>
    <string name="power_status_granted">Granted</string>
    <string name="power_status_not_granted">Not Granted</string>
    <string name="power_status_background_run_permission">Background Run Permission: </string>
    <string name="power_status_allowed">Allowed</string>
    <string name="power_status_restricted">Restricted</string>
    <string name="power_status_doze_mode">Doze Mode Status: </string>
    <string name="power_status_entered">Entered</string>
    <string name="power_status_normal">Normal</string>
    <string name="power_status_doze_check_failed">Doze Mode Status: Check Failed</string>
    <string name="power_status_overall_status">Overall Status: </string>
    <string name="power_status_good">Good</string>
    <string name="power_status_needs_optimization">Needs Optimization</string>
    <string name="power_status_permissions_to_set">Permissions to Set: </string>

</resources>
