<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Sistem Jahit Pintar</string>
    <string name="checking_fingerprint_support">Memeriksa dukungan sidik jari...</string>
    
    <!-- APP Update Related -->
    <string name="update_title">Pembaruan Aplikasi</string>
    <string name="update_available">Versi Baru Tersedia</string>
    <string name="current_version">Versi Saat Ini: %s</string>
    <string name="new_version">Versi Baru: %s</string>
    <string name="update_content">Konten Pembaruan</string>
    <string name="update_now">Perbarui Sekarang</string>
    <string name="update_later">Per<PERSON><PERSON></string>
    <string name="downloading">Mengunduh...</string>
    <string name="download_progress">Progres Unduhan: %d%%</string>
    <string name="download_completed"><PERSON><PERSON><PERSON>lesai</string>
    <string name="download_failed"><PERSON><PERSON><PERSON></string>
    <string name="install_now">Instal Sekarang</string>
    <string name="checking_update">Memeriksa pembaruan...</string>
    <string name="no_update">Sudah versi terbaru</string>
    <string name="network_error">Koneksi jaringan gagal</string>
    <string name="file_size">Ukuran File: %s</string>
    <string name="download_speed">Kecepatan Unduh: %s/s</string>
    <string name="cancel_download">Batalkan Unduhan</string>
    <string name="retry_download">Coba Lagi Unduhan</string>
    <string name="update_install_prompt">Unduhan selesai, klik untuk menginstal</string>
    <string name="update_install_failed">Instalasi gagal</string>
    <string name="update_permission_denied">Izin ditolak, tidak dapat menginstal</string>
    <string name="check_update">Periksa Pembaruan</string>
    <string name="loading_update_content">Memuat konten pembaruan...</string>
    
    <!-- Main Activity -->
    <string name="hint_input_text">Silakan masukkan teks</string>
    <string name="voice_input">Input Suara</string>
    <string name="scan_qrcode">Pindai Kode QR</string>
    <string name="scan_barcode">Pindai Barcode</string>
    <string name="ocr">OCR</string>
    <string name="face_recognition">Pengenalan Wajah</string>
    <string name="fingerprint_recognition">Pengenalan Sidik Jari</string>
    <string name="standard_serial_port">Port Serial Standar</string>
    <string name="usb_serial_port">Port Serial USB</string>
    <string name="close">Tutup</string>
    
    <!-- Fingerprint Activity -->
    <string name="fingerprint_icon_desc">Ikon Sidik Jari</string>
    <string name="fingerprint_title">Pengenalan Sidik Jari</string>
    <string name="start_fingerprint_auth">Mulai Autentikasi Sidik Jari</string>
    
    <!-- Face Detection Activity -->
    <string name="flashlight_desc">Lampu Kilat</string>
    <string name="face_register">Pendaftaran\nWajah</string>
    <string name="face_register_start">Sedang mendaftarkan wajah…</string>
    <string name="face_register_completed">Pendaftaran wajah selesai</string>
    <string name="face_hint">Silakan jaga wajah Anda dalam bingkai, efek lebih baik saat pencahayaan cukup</string>
    
    <!-- Serial Activity -->
    <string name="input_data_hint">Masukkan data untuk dikirim (heksadesimal atau teks)</string>
    <string name="send">Kirim</string>
    <string name="start_polling">Mulai Polling</string>
    <string name="stop_polling">Hentikan Polling</string>
    <string name="clear">Hapus</string>
    <string name="received_data_label">Data yang diterima:</string>
    <string name="receive_data_error">Kesalahan penerimaan data: </string>
    <string name="input_valid_hex_data">Silakan masukkan data heksadesimal yang valid</string>
    
    <!-- Dialog strings (from existing resources) -->
    <string name="app_dialog_title">Pemberitahuan</string>
    <string name="app_dialog_cancel">Batal</string>
    <string name="app_dialog_ok">OK</string>
    
    <!-- Activity Toast and Status Messages -->
    <string name="fingerprint_auth_title">Autentikasi Sidik Jari</string>
    <string name="fingerprint_auth_subtitle">Silakan letakkan jari Anda pada sensor sidik jari</string>
    <string name="fingerprint_auth_description">Gunakan sidik jari Anda untuk autentikasi</string>
    <string name="fingerprint_auth_cancel">Batal</string>
    <string name="fingerprint_auth_success">Autentikasi sidik jari berhasil!</string>
    <string name="fingerprint_auth_failed">Autentikasi sidik jari gagal, silakan coba lagi</string>
    <string name="fingerprint_auth_error">Kesalahan autentikasi: %s</string>
    <string name="fingerprint_auth_prompt">Silakan lakukan autentikasi sidik jari...</string>
    <string name="fingerprint_support_available">Perangkat mendukung pengenalan sidik jari, dapat melakukan autentikasi</string>
    <string name="fingerprint_no_hardware">Perangkat tidak mendukung pengenalan sidik jari</string>
    <string name="fingerprint_hw_unavailable">Perangkat keras pengenalan sidik jari saat ini tidak tersedia</string>
    <string name="fingerprint_none_enrolled">Belum mendaftarkan sidik jari, silakan daftarkan sidik jari di pengaturan sistem terlebih dahulu</string>
    <string name="fingerprint_security_update_required">Diperlukan pembaruan keamanan</string>
    <string name="fingerprint_unknown_error">Kesalahan tidak diketahui</string>
    
    <!-- Serial Port Messages -->
    <string name="serial_init_success">Inisialisasi port serial berhasil</string>
    <string name="serial_init_failed">Inisialisasi port serial gagal</string>
    <string name="send_success">Pengiriman berhasil</string>
    <string name="send_failed">Pengiriman gagal</string>
    <string name="start_polling_msg">Mulai polling</string>
    <string name="stop_polling_msg">Hentikan polling</string>
    <string name="received_prefix">Diterima: </string>
    <string name="usb_device_not_found">Perangkat USB tidak ditemukan</string>
    <string name="usb_permission_granted">Izin USB diberikan</string>
    <string name="usb_permission_denied">Izin USB ditolak</string>
    <string name="usb_init_success">Inisialisasi port serial USB berhasil</string>
    <string name="usb_init_failed">Inisialisasi port serial USB gagal</string>
    <string name="serial_error">Kesalahan port serial: %1$s</string>

    <!-- Log Messages -->
    <string name="log_sync_send_response">Kirim sinkron menerima respons: %1$s</string>
    <string name="log_sync_send_failed">Kirim sinkron gagal: %1$s</string>
    <string name="log_voice_results">=========== onResults = %1$s</string>
    <string name="log_voice_matches_size">=========== matches.size() = %1$d</string>
    <string name="log_voice_error">Kesalahan pengenalan suara: %1$d</string>
    <string name="log_audio_error">Kesalahan audio</string>
    <string name="log_client_error">Kesalahan klien</string>
    <string name="log_insufficient_permissions">Izin tidak mencukupi</string>
    <string name="log_network_error">Kesalahan jaringan</string>
    <string name="log_no_match">Tidak ada hasil yang cocok</string>
    <string name="log_recognizer_busy">Pengenal sedang sibuk</string>
    <string name="log_server_error">Kesalahan server</string>
    <string name="log_speech_timeout">Timeout input suara</string>
    <string name="log_voice_ready">Siap memulai pengenalan suara</string>
    <string name="log_voice_begin">Input suara terdeteksi</string>
    <string name="log_voice_end">Input suara berakhir</string>
    <string name="log_voice_start_failed">Startup pengenalan suara gagal</string>
    <string name="log_fingerprint_auth_error">Kesalahan autentikasi: %1$s</string>
    <string name="log_fingerprint_auth_success">Autentikasi sidik jari berhasil</string>
    <string name="log_fingerprint_auth_failed">Autentikasi sidik jari gagal</string>
    <string name="log_camera_size_updated">Ukuran kamera diperbarui menjadi: %1$dx%2$d</string>
    <string name="log_upload_failed">Upload gagal: %1$s</string>
    <string name="log_response_content">Konten respons: %1$s</string>


    <!-- Language Settings -->
    <string name="language_settings">Pengaturan Bahasa</string>
    <string name="select_language">Pilih Bahasa</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_vietnamese">Tiếng Việt</string>
    <string name="language_indonesian">Bahasa Indonesia</string>
    <string name="back">Kembali</string>
    <string name="confirm">Konfirmasi</string>

    <!-- Voice Input Activity -->
    <string name="voice_input_title">Input Suara (Versi Dialog)</string>
    <string name="voice_recognition_result">Hasil Pengenalan</string>
    <string name="voice_input_hint">Tahan tombol di bawah untuk memulai input suara...</string>
    <string name="voice_hold_to_speak">Tahan untuk berbicara</string>
    <string name="voice_convert_success">Konversi suara ke teks selesai</string>
    <string name="voice_no_speech_detected">Tidak ada suara yang terdeteksi</string>
    <string name="voice_input_cancelled">Input suara dibatalkan</string>
    <string name="voice_recognition_error">Kesalahan pengenalan suara: </string>

    <!-- Chat Activity -->
    <string name="voice_chat_title">Contoh Obrolan Suara</string>
    <string name="voice_input_message_hint">Masukkan pesan</string>
    <string name="voice_hold_to_speak_btn">Tahan untuk berbicara</string>
    <string name="voice_send_message_desc">Kirim pesan</string>

    <string name="toast_permission_granted_success">Izin berhasil diberikan</string>
    <string name="toast_missing_essential_permissions">Izin penting hilang:</string>
    <string name="toast_please_enable_manually_in_settings">Harap aktifkan secara manual di pengaturan</string>
    <string name="toast_optional_permissions_denied">Izin opsional ditolak</string>

    <string name="dialog_power_management_settings_title">Pengaturan Izin Manajemen Daya</string>
    <string name="dialog_power_management_settings_message_prefix">Untuk memastikan aplikasi dapat menerima pesan push bahkan saat keluar, izin berikut perlu diatur:\n\n</string>
    <string name="dialog_power_management_settings_message_suffix">\nPengaturan ini dapat mencegah sistem mematikan layanan push, memastikan penerimaan pesan tepat waktu.</string>
    <string name="dialog_power_management_settings_positive_button">Atur Sekarang</string>
    <string name="dialog_power_management_settings_negative_button">Atur Nanti</string>

    <string name="dialog_battery_optimization_settings_title">Pengaturan Optimasi Baterai</string>
    <string name="dialog_battery_optimization_settings_message">Untuk memastikan aplikasi dapat menerima pesan secara normal di latar belakang, perlu ditambahkan ke daftar putih optimasi baterai.\n\nSetelah mengklik "Pergi ke Pengaturan":\n1. Temukan "Smart Sewing System" di halaman pop-up\n2. Pilih "Jangan optimasi" atau "Izinkan"\n3. Kembali ke aplikasi untuk melanjutkan pengaturan</string>
    <string name="dialog_battery_optimization_positive_button">Pergi ke Pengaturan</string>
    <string name="dialog_battery_optimization_negative_button">Lewati</string>

    <string name="dialog_manual_setup_guide_title">Panduan Pengaturan Manual</string>
    <string name="dialog_manual_setup_guide_message">Harap ikuti langkah-langkah berikut untuk mengatur optimasi baterai secara manual:\n\nMetode Satu:\n1. Buka "Pengaturan" telepon\n2. Temukan "Baterai" atau "Manajemen Daya"\n3. Temukan "Optimasi Baterai" atau "Manajemen Konsumsi Daya Aplikasi"\n4. Temukan "Smart Sewing System" dan atur ke "Jangan optimasi"\n\nMetode Dua:\n1. Buka "Pengaturan" telepon\n2. Temukan "Manajemen Aplikasi" atau "Info Aplikasi"\n3. Temukan "Smart Sewing System"\n4. Temukan opsi "Baterai" di detail aplikasi\n5. Matikan "Optimasi Baterai" atau atur ke "Tidak terbatas"</string>
    <string name="dialog_manual_setup_guide_positive_button">Saya mengerti</string>
    <string name="dialog_manual_setup_guide_negative_button">Coba lagi</string>

    <string name="dialog_autostart_settings_title">Pengaturan Izin Autostart</string>
    <string name="dialog_autostart_settings_message">Untuk memastikan layanan push dapat berjalan bahkan setelah aplikasi keluar, harap aktifkan izin autostart aplikasi.\n\nLokasi pengaturan untuk berbagai merek ponsel:\n• Xiaomi: Pusat Keamanan > Manajemen Aplikasi > Manajemen Autostart\n• Huawei: Manajer Telepon > Manajemen Peluncuran Aplikasi\n• OPPO/OnePlus: Pengaturan > Manajemen Aplikasi > Autostart\n• vivo: iManager > Manajemen Aplikasi > Autostart\n• Lainnya: Pengaturan > Manajemen Aplikasi > Manajemen Autostart</string>
    <string name="dialog_autostart_positive_button">Pergi ke Pengaturan</string>
    <string name="dialog_autostart_negative_button">Lewati</string>
    <string name="toast_set_autostart_hint">Harap atur "%1$s" untuk mengizinkan autostart</string>
    <string name="toast_manual_autostart_hint">Harap aktifkan izin autostart secara manual di pengaturan</string>

    <string name="toast_set_battery_or_background_hint">Harap temukan pengaturan baterai atau latar belakang di info aplikasi</string>
    <string name="toast_manual_background_run_hint">Harap izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>
    <string name="toast_power_management_setup_completed">Pengaturan izin manajemen daya selesai!</string>
    <string name="toast_power_management_partially_granted">Beberapa izin masih perlu diatur secara manual</string>

    <string name="dialog_permission_reminder_title">Pengingat Izin</string>
    <string name="dialog_permission_reminder_message_prefix">Izin berikut tidak diatur, yang dapat memengaruhi penerimaan pesan:\n\n</string>
    <string name="dialog_permission_reminder_message_suffix">\n\nAtur sekarang?</string>
    <string name="dialog_permission_reminder_positive_button">Atur Sekarang</string>
    <string name="dialog_permission_reminder_negative_button">Ingatkan Nanti</string>

    <string name="dialog_startup_management_settings_title">Pengaturan Izin Manajemen Startup</string>
    <string name="dialog_startup_management_settings_positive_button">Pergi ke Pengaturan</string>
    <string name="dialog_startup_management_settings_negative_button">Atur Nanti</string>

    <string name="dialog_startup_management_confirm_title">Konfirmasi Pengaturan Selesai</string>
    <string name="dialog_startup_management_confirm_message">Apakah Anda sudah menyelesaikan pengaturan manajemen startup sesuai instruksi?\n\n✓ Matikan "Manajemen Otomatis"\n✓ Aktifkan "Izinkan Autostart"\n✓ Aktifkan "Izinkan Start Terkait"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"\n\nSetelah memilih "Ya", sistem tidak akan mengingatkan lagi.</string>
    <string name="dialog_startup_management_confirm_positive_button">Ya, pengaturan selesai</string>
    <string name="dialog_startup_management_confirm_negative_button">Belum, ingatkan nanti</string>
    <string name="toast_startup_management_completed">Pengaturan izin manajemen startup selesai!</string>
    <string name="toast_startup_management_reset">Status izin manajemen startup telah direset</string>

    <string name="toast_background_permission_setup_completed">Pengaturan izin manajemen daya selesai, aplikasi dapat berjalan normal di latar belakang</string>
    <string name="toast_partial_power_permission_denied">Beberapa izin manajemen daya belum diatur: %1$s</string>
    <string name="toast_background_permission_dialog_reset">Status dialog izin latar belakang telah direset</string>
    <string name="toast_set_all_power_permissions_hint">Harap atur 'Optimasi Baterai', 'Latar Belakang', 'Autostart' menjadi Izinkan/Tidak Terbatas di info aplikasi</string>

    <string name="power_status_storage_permission">Izin Penyimpanan: </string>
    <string name="power_status_notification_permission">Izin Notifikasi: </string>
    <string name="power_status_battery_optimization_whitelist">Daftar Putih Optimasi Baterai: </string>
    <string name="power_status_joined">Bergabung</string>
    <string name="power_status_not_joined">Belum Bergabung</string>
    <string name="power_status_autostart_permission">Izin Autostart: </string>
    <string name="power_status_granted">Diberikan</string>
    <string name="power_status_not_granted">Belum Diberikan</string>
    <string name="power_status_background_run_permission">Izin Latar Belakang: </string>
    <string name="power_status_allowed">Diizinkan</string>
    <string name="power_status_restricted">Dibatasi</string>
    <string name="power_status_doze_mode">Status Mode Doze: </string>
    <string name="power_status_entered">Masuk</string>
    <string name="power_status_normal">Normal</string>
    <string name="power_status_doze_check_failed">Status Mode Doze: Pemeriksaan gagal</string>
    <string name="power_status_overall_status">Status Keseluruhan: </string>
    <string name="power_status_good">Baik</string>
    <string name="power_status_needs_optimization">Perlu Optimasi</string>
    <string name="power_status_permissions_to_set">Izin yang Perlu Diatur: </string>

    <!-- PermissionHelper Toast Messages -->
    <string name="toast_power_management_permission_completed">Pengaturan izin manajemen daya selesai, aplikasi dapat berjalan normal di latar belakang</string>
    <string name="toast_manual_remaining_permissions">Harap selesaikan pengaturan izin yang tersisa secara manual untuk pengalaman optimal</string>
    <string name="toast_set_power_management_permissions">Harap atur izin yang sesuai untuk aplikasi di manajemen daya</string>
    <string name="toast_find_power_management_settings">Harap temukan opsi terkait manajemen daya di pengaturan</string>
    <string name="toast_manual_system_settings">Harap buka pengaturan sistem secara manual untuk konfigurasi</string>
    <string name="toast_opening_battery_optimization">Membuka pengaturan optimasi baterai...</string>
    <string name="toast_device_not_support_battery_optimization">Perangkat tidak mendukung pengaturan optimasi baterai langsung, harap atur secara manual</string>
    <string name="toast_battery_optimization_test_failed">Pemeriksaan izin optimasi baterai gagal</string>
    <string name="toast_android_no_battery_optimization">Versi Android saat ini tidak memerlukan izin optimasi baterai</string>
    <string name="toast_open_battery_optimization_list">Buka daftar pengaturan optimasi baterai</string>
    <string name="toast_device_not_support_battery_settings">Perangkat tidak mendukung pengaturan optimasi baterai</string>
    <string name="toast_all_battery_optimization_failed">Semua metode pengaturan optimasi baterai gagal</string>
    <string name="toast_set_all_permissions_in_app_info">Harap atur semua izin terkait di info aplikasi</string>
    <string name="toast_manual_allow_background_run">Harap izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>
    <string name="toast_set_battery_background_autostart_permissions">Harap atur 'Optimasi Baterai', 'Latar Belakang', 'Autostart' menjadi Izinkan/Tidak Terbatas di info aplikasi</string>
    <string name="toast_background_permission_already_set">Izin latar belakang sudah diatur, tidak perlu diulang</string>

    <!-- PermissionHelper Dialog Messages -->
    <string name="dialog_background_run_settings_title">Pengaturan Izin Latar Belakang</string>
    <string name="dialog_background_run_settings_message">Untuk memastikan push pesan, harap atur 'Optimasi Baterai', 'Latar Belakang', 'Autostart' menjadi Izinkan/Tidak Terbatas di halaman berikutnya.</string>
    <string name="dialog_background_run_settings_positive_button">Pergi ke Pengaturan</string>
    <string name="dialog_background_run_settings_negative_button">Atur Nanti</string>

    <string name="dialog_startup_management_settings_message_xiaomi">Untuk memastikan push pesan, harap lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Matikan "Manajemen Otomatis"\n✓ Aktifkan "Izinkan Autostart"\n✓ Aktifkan "Izinkan Start Terkait"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"</string>
    <string name="dialog_startup_management_settings_message_huawei">Untuk memastikan push pesan, harap lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Matikan "Manajemen Otomatis"\n✓ Aktifkan "Izinkan Autostart"\n✓ Aktifkan "Izinkan Start Terkait"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"</string>
    <string name="dialog_startup_management_settings_message_oppo">Untuk memastikan push pesan, harap lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan "Izinkan Autostart"\n✓ Aktifkan "Izinkan Start Terkait"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"</string>
    <string name="dialog_startup_management_settings_message_vivo">Untuk memastikan push pesan, harap lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan "Izinkan Autostart"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"\n✓ Aktifkan "Izinkan Konsumsi Baterai Tinggi di Latar Belakang"</string>
    <string name="dialog_startup_management_settings_message_samsung">Untuk memastikan push pesan, harap lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Matikan "Tidurkan Aplikasi"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"</string>
    <string name="dialog_startup_management_settings_message_oneplus">Untuk memastikan push pesan, harap lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan "Izinkan Autostart"\n✓ Aktifkan "Izinkan Start Terkait"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"</string>
    <string name="dialog_startup_management_settings_message_default">Untuk memastikan push pesan, harap lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan "Izinkan Autostart"\n✓ Aktifkan "Izinkan Aktivitas Latar Belakang"\n✓ Matikan pembatasan optimasi baterai terkait</string>
    <string name="toast_startup_management_completed">Pengaturan izin manajemen startup selesai!</string>
    <string name="toast_startup_management_reset">Status izin manajemen startup telah diatur ulang</string>

    <string name="dialog_startup_management_base_message">Untuk memastikan push pesan MQTT berfungsi dengan baik, ikuti langkah-langkah berikut untuk pengaturan:\n\n</string>
    <string name="dialog_startup_management_huawei_honor_message">Langkah-langkah pengaturan perangkat Huawei/Honor:\n1. Temukan \"Sistem Jahit Cerdas\" di halaman manajemen startup\n2. Matikan \"Manajemen otomatis\"\n3. Aktifkan secara manual:\n   • Izinkan mulai otomatis ✓\n   • Izinkan mulai terkait ✓\n   • Izinkan aktivitas latar belakang ✓\n\nJalur: Manajer Telepon → Manajemen peluncuran aplikasi</string>
    <string name="dialog_startup_management_xiaomi_redmi_message">Langkah-langkah pengaturan perangkat Xiaomi:\n1. Temukan \"Sistem Jahit Cerdas\" di halaman manajemen autostart\n2. Matikan \"Manajemen otomatis\"\n3. Aktifkan secara manual:\n   • Izinkan mulai otomatis ✓\n   • Izinkan mulai terkait ✓\n   • Izinkan aktivitas latar belakang ✓\n\nJalur: Pusat Keamanan → Manajemen aplikasi → Manajemen autostart</string>
    <string name="dialog_startup_management_oppo_message">Langkah-langkah pengaturan perangkat OPPO:\n1. Temukan \"Sistem Jahit Cerdas\" di halaman manajemen autostart\n2. Matikan \"Manajemen cerdas\"\n3. Aktifkan secara manual:\n   • Izinkan mulai otomatis ✓\n   • Izinkan mulai terkait ✓\n   • Izinkan berjalan di latar belakang ✓\n\nJalur: Pengaturan → Manajemen aplikasi → Autostart</string>
    <string name="dialog_startup_management_vivo_message">Langkah-langkah pengaturan perangkat vivo:\n1. Temukan \"Sistem Jahit Cerdas\" di halaman manajemen autostart\n2. Matikan \"Manajemen otomatis\"\n3. Aktifkan secara manual:\n   • Izinkan mulai otomatis ✓\n   • Izinkan mulai terkait ✓\n   • Izinkan konsumsi daya tinggi di latar belakang ✓\n\nJalur: i Manager → Manajemen aplikasi → Autostart</string>
    <string name="dialog_startup_management_oneplus_message">Langkah-langkah pengaturan perangkat OnePlus:\n1. Temukan \"Sistem Jahit Cerdas\" di halaman manajemen autostart\n2. Nyalakan \"Izinkan mulai otomatis\"\n3. Nyalakan \"Izinkan mulai terkait\"\n4. Nyalakan \"Izinkan aktivitas latar belakang\"\n\nJalur: Pengaturan → Manajemen aplikasi → Manajemen autostart</string>
    <string name="toast_background_permission_setup_completed">Pengaturan izin manajemen daya selesai, aplikasi dapat berjalan normal di latar belakang</string>
    <string name="toast_partial_power_permission_denied">Beberapa izin manajemen daya belum diatur: %1$s</string>
    <string name="toast_background_permission_dialog_reset">Status dialog izin latar belakang telah diatur ulang</string>
    <string name="toast_set_all_power_permissions_hint">Harap atur 'Optimasi Baterai' 'Latar Belakang' 'Autostart' ke diizinkan/tidak dibatasi di info aplikasi</string>

    <string name="power_status_storage_permission">Izin Penyimpanan: </string>
    <string name="power_status_notification_permission">Izin Notifikasi: </string>
    <string name="power_status_battery_optimization_whitelist">Daftar Putih Optimasi Baterai: </string>
    <string name="power_status_joined">Bergabung</string>
    <string name="power_status_not_joined">Tidak Bergabung</string>
    <string name="power_status_autostart_permission">Izin Autostart: </string>
    <string name="power_status_granted">Diberikan</string>
    <string name="power_status_not_granted">Tidak Diberikan</string>
    <string name="power_status_background_run_permission">Izin Jalankan Latar Belakang: </string>
    <string name="power_status_allowed">Diizinkan</string>
    <string name="power_status_restricted">Dibatasi</string>
    <string name="power_status_doze_mode">Status Mode Doze: </string>
    <string name="power_status_entered">Masuk</string>
    <string name="power_status_normal">Normal</string>
    <string name="power_status_doze_check_failed">Status Mode Doze: Pemeriksaan Gagal</string>
    <string name="power_status_overall_status">Status Keseluruhan: </string>
    <string name="power_status_good">Bagus</string>
    <string name="power_status_needs_optimization">Perlu Optimasi</string>
    <string name="power_status_permissions_to_set">Izin untuk Diatur: </string>

</resources>