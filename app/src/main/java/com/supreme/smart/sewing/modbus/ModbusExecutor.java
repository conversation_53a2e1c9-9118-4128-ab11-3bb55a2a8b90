package com.supreme.smart.sewing.modbus;

import android.content.Context;
import android.util.Log;

import com.blankj.utilcode.util.LogUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.BackpressureStrategy;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;


/**
 * Modbus 执行器
 * 使用单例ModbusManager实现多线程共享同一个YSerialPort文件描述符
 * 支持并发读取多个数据块而不会重复打开/关闭连接
 */
public enum ModbusExecutor {

    INSTANCE;

    private static final String TAG = "ModbusExecutor";

    private final ModbusManager modbusManager;

    // Modbus设备配置
    private byte slaveId = 2;              // 从站地址
    private String portPath = "/dev/ttyS2"; // 串口路径
    private int baudRate = 115200;           // 波特率
    private int readTimeout = 500;         // 读取超时时间(ms)
    private int retryCount = 3;            // 重试次数
    private long retryDelay = 50;          // 重试延迟(ms)
    private long readInterval = 50;        // 读取间隔(ms)

    private QixingDataResult lastResult;  // 缓存最后一次成功的结果
    private WeakReference<Context> contextRef;  // 使用弱引用持有Context

    private Disposable connectionMonitor;
    private boolean isAutoReconnect = true;
    private int reconnectAttempts = 0;
    private static final int MAX_RECONNECT_ATTEMPTS = 5;

    ModbusExecutor() {
        modbusManager = ModbusManager.getInstance();
    }

    /**
     * 设置Modbus配置
     */
    public void setConfig(byte slaveId, String portPath, int baudRate, int readTimeout,
                          int retryCount, long retryDelay, long readInterval) {
        this.slaveId = slaveId;
        this.portPath = portPath;
        this.baudRate = baudRate;
        this.readTimeout = readTimeout;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
        this.readInterval = readInterval;
    }

    /**
     * 设置是否自动重连
     */
    public void setAutoReconnect(boolean autoReconnect) {
        this.isAutoReconnect = autoReconnect;
    }

    /**
     * 启动连接监控
     */
    private void startConnectionMonitor() {
        if (connectionMonitor != null && !connectionMonitor.isDisposed()) {
            return;
        }

        connectionMonitor = Flowable.interval(1000, TimeUnit.MILLISECONDS)
                .observeOn(Schedulers.io())
                .subscribe(tick -> {
                    boolean isConnected = modbusManager.isConnected();
                    if (!isConnected && isAutoReconnect) {
                        LogUtils.wTag(TAG, "检测到连接断开，尝试重连...");
                        reconnect();
                    }
                });
    }

    /**
     * 停止连接监控
     */
    private void stopConnectionMonitor() {
        if (connectionMonitor != null && !connectionMonitor.isDisposed()) {
            connectionMonitor.dispose();
            connectionMonitor = null;
        }
    }

    /**
     * 重连机制
     */
    private void reconnect() {
        if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
            LogUtils.eTag(TAG, "重连次数超过最大限制，停止重连");
            stopConnectionMonitor();
            return;
        }

        Context context = contextRef != null ? contextRef.get() : null;
        if (context == null) {
            LogUtils.eTag(TAG, "Context为空，无法重连");
            stopConnectionMonitor();
            return;
        }

        reconnectAttempts++;
        LogUtils.iTag(TAG, "第" + reconnectAttempts + "次重连尝试");

        // 使用指数退避策略
        long delay = retryDelay * (1L << (reconnectAttempts - 1));
        Disposable ignored = Flowable.timer(delay, TimeUnit.MILLISECONDS)
                .observeOn(Schedulers.io())
                .subscribe(tick -> {
                    // 先关闭现有连接
                    modbusManager.close();

                    // 重新初始化连接
                    boolean success = modbusManager.init(context, portPath, baudRate);
                    if (success) {
                        LogUtils.iTag(TAG, "重连成功");
                        modbusManager.setDefaultTimeout(readTimeout);
                        reconnectAttempts = 0;
                    } else {
                        LogUtils.eTag(TAG, "重连失败");
                    }
                });
    }

    /**
     * 初始化Modbus连接
     */
    public boolean initModbus(Context context) {
        this.contextRef = new WeakReference<>(context.getApplicationContext()); // 使用ApplicationContext避免内存泄漏
        boolean success = modbusManager.init(this.contextRef.get(), portPath, baudRate);
        if (success) {
            LogUtils.iTag(TAG, "Modbus连接初始化成功");
            modbusManager.setDefaultTimeout(readTimeout);
            startConnectionMonitor();
            reconnectAttempts = 0;
        } else {
            LogUtils.eTag(TAG, "Modbus连接初始化失败");
        }
        return success;
    }

    private final AtomicReference<QixingDataResult> lastResultRef = new AtomicReference<>();


    /**
     * 异步并行读取琦星电控的四种数据（针距、计数器、状态、速度）
     *
     * @param registers 寄存器数组
     *                  数据读取说明：
     *                  1. 针距数据：从寄存器131开始读取1个寄存器
     *                  2. 计数器数据：从寄存器2102开始读取5个寄存器（包含针数、计件数等）
     *                  3. 状态数据：从寄存器2130开始读取2个寄存器（设备状态和错误码）
     *                  4. 速度数据：从寄存器1开始读取1个寄存器（最高转速）
     * @return Observable<QixingDataResult>
     */
    private Observable<QixingDataResult> readDataOfQixingAsync1(int[][] registers) {
        // 创建一个QixingDataResult对象用于存储结果
        QixingDataResult result = new QixingDataResult();

        LogUtils.dTag(TAG, "开始批量读取4个Modbus寄存器组");

        return modbusManager.batchReadHoldingRegisters(slaveId, registers)
                .map(responses -> {
                    int responseSize = responses.size();
                    LogUtils.dTag(TAG, "收到批量寄存器读取响应，共 " + responseSize + " 个响应");

                    // 处理针距数据响应
                    if (responseSize > 0) {
                        ModbusResponse response0 = responses.get(0);
                        if (response0.isSuccess) {
                            result.setNeedleGapData(response0.data);
                            //LogUtils.iTag(TAG, "读取针距成功 : " + CommonUtils.convertByteArrayToString(response0.data));
                        } else {
                            LogUtils.eTag(TAG, "读取针距失败: " + response0.errorMessage);
                            result.setNeedleGapError(response0.errorMessage);
                        }
                    } else {
                        result.setNeedleGapError("未收到针距数据响应");
                    }

                    // 处理计数器数据响应
                    if (responseSize > 1) {
                        ModbusResponse response1 = responses.get(1);
                        if (response1.isSuccess) {
                            result.setCounterData(response1.data);
                            // LogUtils.iTag(TAG, "读取计数器成功 : " + CommonUtils.convertByteArrayToString(response1.data));
                        } else {
                            LogUtils.eTag(TAG, "读取计数器失败: " + response1.errorMessage);
                            result.setCounterError(response1.errorMessage);
                        }
                    } else {
                        result.setCounterError("未收到计数器数据响应");
                    }

                    // 处理状态数据响应
                    if (responseSize > 2) {
                        ModbusResponse response2 = responses.get(2);
                        if (response2.isSuccess) {
                            result.setStatusData(response2.data);
                            // LogUtils.iTag(TAG, "读取状态成功 : " + CommonUtils.convertByteArrayToString(response2.data));
                        } else {
                            LogUtils.eTag(TAG, "读取状态失败: " + response2.errorMessage);
                            result.setStatusError(response2.errorMessage);
                        }
                    } else {
                        result.setStatusError("未收到状态数据响应");
                    }

                    // 处理速度数据响应
                    if (responseSize > 3) {
                        ModbusResponse response3 = responses.get(3);
                        if (response3.isSuccess) {
                            result.setSpeedData(response3.data);
                            //LogUtils.iTag(TAG, "读取速度成功 : " + CommonUtils.convertByteArrayToString(response3.data));
                        } else {
                            LogUtils.eTag(TAG, "读取速度失败: " + response3.errorMessage);
                            result.setSpeedError(response3.errorMessage);
                        }
                    } else {
                        result.setSpeedError("未收到速度数据响应");
                    }

                    // 更新最后一次成功的结果引用
                    lastResultRef.set(result);
                    return result;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }


    /**
     * 异步并行读取琦星电控的四种数据（针距、计数器、状态、速度）- 支持不同轮询间隔
     * 数据读取说明：
     * 1. 针距数据：从寄存器131开始读取1个寄存器 - 5秒间隔
     * 2. 计数器数据：从寄存器2102开始读取5个寄存器（包含针数、计件数等） - 1秒间隔
     * 3. 状态数据：从寄存器2130开始读取2个寄存器（设备状态和错误码） - 0.5秒间隔
     * 4. 速度数据：从寄存器1开始读取1个寄存器（最高转速） - 0.2秒间隔
     * <p>
     * 使用PollingConfig配置的智能数据读取方法
     *
     * @param currentTimeMs 当前时间戳
     * @param config        轮询配置
     * @return Observable<QixingDataResult>
     */
    private Observable<QixingDataResult> readDataOfQixingAsync2(long currentTimeMs, PollingConfig config) {
        // 创建一个QixingDataResult对象用于存储结果
        // 如果有历史数据，先复制历史数据作为基础
        QixingDataResult result = new QixingDataResult();
        if (lastResult != null) {
            // 复制历史数据作为基础，确保所有数据类型都有值
            result.copyFrom(lastResult);
            LogUtils.dTag(TAG, "复制历史数据作为基础");
        }

        // 根据当前时间间隔决定要读取哪些寄存器
        List<int[]> registerList = new ArrayList<>();
        List<PollingConfig.DataType> dataTypesToRead = new ArrayList<>();

        LogUtils.dTag(TAG, String.format(Locale.US, "智能轮询检查，时间: %dms, 配置: %s",
                currentTimeMs, config.toString()));

        // 遍历配置中的所有数据类型
        for (PollingConfig.DataType dataType : config.getConfiguredDataTypes()) {
            Long interval = config.getInterval(dataType);
            if (interval != null) {
                if (currentTimeMs % interval == 0) {
                    registerList.add(new int[]{dataType.getStartAddress(), dataType.getQuantity()});
                    dataTypesToRead.add(dataType);
                    LogUtils.dTag(TAG, "添加" + dataType.getDescription() + "读取任务，时间: " + currentTimeMs + "ms, 间隔: " + interval + "ms");
                } else {
                    LogUtils.dTag(TAG, "跳过" + dataType.getDescription() + "，时间: " + currentTimeMs + "ms, 间隔: " + interval + "ms, 余数: " + (currentTimeMs % interval));
                }
            }
        }

        // 如果没有需要读取的寄存器，返回历史数据
        if (registerList.isEmpty()) {
            LogUtils.dTag(TAG, "当前时间点无需读取任何数据，返回历史数据");
            result.setSentCommandCount(0);
            result.setReceivedCommandCount(0);
            return Observable.just(result);
        }

        // 转换为数组
        int[][] registers = registerList.toArray(new int[0][]);

        LogUtils.dTag(TAG, "智能轮询将读取 " + registers.length + " 个寄存器组");

        // 设置发送的命令数量
        result.setSentCommandCount(registers.length);

        return modbusManager.batchReadHoldingRegisters(slaveId, registers)
                .map(responses -> {
                    int responseSize = responses.size();
                    LogUtils.dTag(TAG, "收到智能批量寄存器读取响应，共 " + responseSize + " 个响应");

                    // 统计真正成功的响应数量
                    int successfulResponseCount = 0;
                    for (ModbusResponse response : responses) {
                        if (response.isSuccess) {
                            successfulResponseCount++;
                        }
                    }
                    
                    // 设置接收的成功命令数量
                    result.setReceivedCommandCount(successfulResponseCount);
                    LogUtils.dTag(TAG, "其中成功响应 " + successfulResponseCount + " 个");

                    // 根据响应数量和配置处理数据
                    int responseIndex = 0;

                    // 按照要读取的数据类型顺序处理响应
                    for (PollingConfig.DataType dataType : dataTypesToRead) {
                        if (responseIndex < responseSize) {
                            ModbusResponse response = responses.get(responseIndex++);
                            processDataTypeResponse(result, dataType, response);
                            LogUtils.dTag(TAG, "更新" + dataType.getDescription() + "数据");
                        }
                    }

                    // 更新最后一次成功的结果引用
                    lastResultRef.set(result);
                    return result;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 处理特定数据类型的响应
     *
     * @param result   结果对象
     * @param dataType 数据类型
     * @param response 响应对象
     */
    private void processDataTypeResponse(QixingDataResult result, PollingConfig.DataType dataType, ModbusResponse response) {
        switch (dataType) {
            case NEEDLE_GAP:
                if (response.isSuccess) {
                    result.setNeedleGapData(response.data);
                    LogUtils.dTag(TAG, "读取针距数据成功");
                } else {
                    LogUtils.eTag(TAG, "读取针距失败: " + response.errorMessage);
                    result.setNeedleGapError(response.errorMessage);
                }
                break;
            case COUNTER:
                if (response.isSuccess) {
                    result.setCounterData(response.data);
                    LogUtils.dTag(TAG, "读取计数器数据成功");
                } else {
                    LogUtils.eTag(TAG, "读取计数器失败: " + response.errorMessage);
                    result.setCounterError(response.errorMessage);
                }
                break;
            case STATUS:
                if (response.isSuccess) {
                    result.setStatusData(response.data);
                    LogUtils.dTag(TAG, "读取状态数据成功");
                } else {
                    LogUtils.eTag(TAG, "读取状态失败: " + response.errorMessage);
                    result.setStatusError(response.errorMessage);
                }
                break;
            case SPEED:
                if (response.isSuccess) {
                    result.setSpeedData(response.data);
                    LogUtils.dTag(TAG, "读取速度数据成功");
                } else {
                    LogUtils.eTag(TAG, "读取速度失败: " + response.errorMessage);
                    result.setSpeedError(response.errorMessage);
                }
                break;
        }
    }

    /**
     * 定时读取琦星电控数据
     *
     * @param registers 寄存器地址和数量
     *                  数据读取说明：
     *                  1. 针距数据：从寄存器131开始读取1个寄存器
     *                  2. 计数器数据：从寄存器2102开始读取5个寄存器（包含针数、计件数等）
     *                  3. 状态数据：从寄存器2130开始读取2个寄存器（设备状态和错误码）
     *                  4. 速度数据：从寄存器1开始读取1个寄存器（最高转速）
     * @return Flowable<QixingDataResult>
     */
    private Flowable<QixingDataResult> readDataOfQixingPeriodically1(int[][] registers) {
        return Flowable.interval(0, readInterval, TimeUnit.MILLISECONDS)
                .onBackpressureDrop()
                .observeOn(Schedulers.io())
                // 添加连接状态检查
                .filter(tick -> {
                    boolean isConnected = modbusManager.isConnected();
                    if (!isConnected) {
                        LogUtils.wTag(TAG, "Modbus连接已断开，返回最后一次成功的数据");
                        if (lastResult != null) {
                            return true;
                        }
                    }
                    return isConnected;
                })
                .flatMap(tick -> {
                    if (!modbusManager.isConnected() && lastResult != null) {
                        return Flowable.just(lastResult);
                    }
                    return readDataOfQixingAsync1(registers)
                            .toFlowable(BackpressureStrategy.DROP)
                            .timeout(readTimeout, TimeUnit.MILLISECONDS)
                            .retryWhen(errors ->
                                    errors.zipWith(Flowable.range(1, retryCount), (error, retryCount) -> {
                                                if (isRetryableError(error)) {
                                                    LogUtils.wTag(TAG, "可重试错误，第" + retryCount + "次重试: " + error.getMessage());
                                                    return retryCount;
                                                } else {
                                                    LogUtils.eTag(TAG, "不可重试错误: " + Log.getStackTraceString(error));
                                                    throw new RuntimeException(error);
                                                }
                                            })
                                            .flatMap(retryCount ->
                                                    Flowable.timer(retryDelay * (1L << (retryCount - 1)), TimeUnit.MILLISECONDS)
                                            )
                            )
                            .onErrorReturn(error -> {
                                LogUtils.eTag(TAG, "读取数据最终失败: " + Log.getStackTraceString(error));
                                return lastResult != null ? lastResult : createEmptyResult("最终失败: " + error.getMessage());
                            })
                            .doOnNext(result -> {
                                if (result.isAllSuccess()) {
                                    lastResult = result;
                                    LogUtils.dTag(TAG, "数据读取成功");
                                } else {
                                    LogUtils.wTag(TAG, "数据读取部分失败: " + getErrorMessage(result));
                                }
                            });
                })
                .distinctUntilChanged(this::isResultEqual) // 比较结果是否相同，加上这句后，结果相同不会触发onNext
                .throttleLast(1000, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .doOnCancel(() -> LogUtils.iTag(TAG, "定时读取已取消"))
                .doOnComplete(() -> LogUtils.iTag(TAG, "定时读取已完成"))
                .doOnError(error -> LogUtils.eTag(TAG, "定时读取发生错误: " + Log.getStackTraceString(error)));
    }

    /**
     * 比较两个结果是否相同
     */
    private boolean isResultEqual(QixingDataResult prev, QixingDataResult curr) {
        if (prev == null || curr == null) return false;

        // 对于智能轮询模式，我们只比较实际更新的数据
        // 如果某个数据在本次轮询中没有更新（为null），则使用上一次的值进行比较
        byte[] prevNeedleGap = prev.getNeedleGapData();
        byte[] currNeedleGap = curr.getNeedleGapData();
        if (currNeedleGap != null && !Arrays.equals(prevNeedleGap, currNeedleGap)) return false;

        byte[] prevCounter = prev.getCounterData();
        byte[] currCounter = curr.getCounterData();
        if (currCounter != null && !Arrays.equals(prevCounter, currCounter)) return false;

        byte[] prevStatus = prev.getStatusData();
        byte[] currStatus = curr.getStatusData();
        if (currStatus != null && !Arrays.equals(prevStatus, currStatus)) return false;

        byte[] prevSpeed = prev.getSpeedData();
        byte[] currSpeed = curr.getSpeedData();
        if (currSpeed != null && !Arrays.equals(prevSpeed, currSpeed)) return false;

        // 如果所有更新的数据都相同，则认为结果相同
        return true;
    }

    /**
     * 定时读取琦星电控数据 - 使用PollingConfig配置
     *
     * @param config  轮询配置
     * @param onNext  数据回调
     * @param onError 错误回调
     * @return Disposable 用于取消订阅
     */
    public Disposable readDataOfQixingPeriodically2(
            PollingConfig config,
            Consumer<QixingDataResult> onNext,
            Consumer<Throwable> onError) {

        // 计算基础轮询间隔（所有间隔的最大公约数）
        long baseInterval = config.calculateBaseInterval();

        LogUtils.iTag(TAG, String.format(Locale.US, "启动智能轮询，基础间隔: %dms, 配置: %s",
                baseInterval, config));

        return Flowable.interval(0, baseInterval, TimeUnit.MILLISECONDS)
                .onBackpressureDrop()
                .observeOn(Schedulers.io())
                .filter(tick -> {
                    boolean isConnected = modbusManager.isConnected();
                    if (!isConnected) {
                        LogUtils.wTag(TAG, "Modbus连接已断开，返回最后一次成功的数据");
                        if (lastResult != null) {
                            return true;
                        }
                    }
                    return isConnected;
                })
                .flatMap(tick -> {
                    long currentTimeMs = tick * baseInterval;

                    if (!modbusManager.isConnected() && lastResult != null) {
                        return Flowable.just(lastResult);
                    }

                    return readDataOfQixingAsync2(currentTimeMs, config)
                            .toFlowable(BackpressureStrategy.DROP)
                            .timeout(readTimeout, TimeUnit.MILLISECONDS)
                            .retryWhen(errors ->
                                    errors.zipWith(Flowable.range(1, retryCount), (error, retryCount) -> {
                                                if (isRetryableError(error)) {
                                                    LogUtils.wTag(TAG, "智能轮询可重试错误，第" + retryCount + "次重试: " + Log.getStackTraceString(error));
                                                    return retryCount;
                                                } else {
                                                    LogUtils.eTag(TAG, "智能轮询不可重试错误: " + Log.getStackTraceString(error));
                                                    throw new RuntimeException(error);
                                                }
                                            })
                                            .flatMap(retryCount ->
                                                    Flowable.timer(retryDelay * (1L << (retryCount - 1)), TimeUnit.MILLISECONDS)
                                            )
                            )
                            .onErrorReturn(error -> {
                                LogUtils.eTag(TAG, "智能轮询读取数据最终失败: " + Log.getStackTraceString(error));
                                return lastResult != null ? lastResult : createEmptyResult("最终失败: " + error.getMessage());
                            })
                            .doOnNext(result -> {
                                // 对于智能轮询，只要有成功的响应就更新lastResult
                                if (result.getReceivedCommandCount() > 0) {
                                    lastResult = result;
                                    LogUtils.dTag(TAG, "智能轮询数据读取成功，成功响应: " + result.getReceivedCommandCount() + "/" + result.getSentCommandCount());
                                } else {
                                    LogUtils.wTag(TAG, "智能轮询数据读取失败: " + getErrorMessage(result));
                                }
                            });
                })
                .distinctUntilChanged(this::isResultEqual)
                .throttleLast(1000, TimeUnit.MILLISECONDS)  // 增加节流时间到2秒
                .observeOn(AndroidSchedulers.mainThread())
                .doOnCancel(() -> LogUtils.iTag(TAG, "智能轮询定时读取已取消"))
                .doOnComplete(() -> LogUtils.iTag(TAG, "智能轮询定时读取已完成"))
                .doOnError(error -> LogUtils.eTag(TAG, "智能轮询定时读取发生错误: " + Log.getStackTraceString(error)))
                .subscribe(
                        onNext,
                        onError,
                        () -> LogUtils.iTag(TAG, "智能轮询定时读取流已完成")
                );
    }

    /**
     * 启动智能定时读取数据
     *
     * @param onNext  数据回调
     * @param onError 错误回调
     * @return Disposable 用于取消订阅
     */
    private Disposable startPeriodicReading1(
            Consumer<QixingDataResult> onNext,
            Consumer<Throwable> onError) {
        // 定义要读取的寄存器地址和数量
        int[][] registers = new int[][]{
                {131, 1},   // 针距数据：寄存器131，读取1个
                {2102, 5},  // 计数器数据：寄存器2102，读取5个
                {2130, 2},  // 状态数据：寄存器2130，读取2个
                {1, 1}      // 速度数据：寄存器1，读取1个
        };
        return readDataOfQixingPeriodically1(registers)
                .subscribe(
                        onNext,
                        onError,
                        () -> LogUtils.iTag(TAG, "定时读取流已完成")
                );
    }

    /**
     * 启动智能定时读取数据
     * 针距数据：5秒，计数器数据：1秒，状态数据：0.5秒，速度数据：0.2秒
     *
     * @param onNext  数据回调
     * @param onError 错误回调
     * @return Disposable 用于取消订阅
     */
    private Disposable startPeriodicReading2(
            Consumer<QixingDataResult> onNext,
            Consumer<Throwable> onError) {
        // 创建配置对象
        PollingConfig config = new PollingConfig.Builder()
                .setInterval(PollingConfig.DataType.NEEDLE_GAP, 5000)
                .setInterval(PollingConfig.DataType.COUNTER, 1000)
                .setInterval(PollingConfig.DataType.STATUS, 1000)
                .setInterval(PollingConfig.DataType.SPEED, 500)
                .build();

        return readDataOfQixingPeriodically2(config, onNext, onError);
    }

    /**
     * 启动智能定时读取数据
     * @param onNext 数据回调
     * @param onError 错误回调
     *
     * @param flag  true:根据配置，读取不同的数据，使用不同的轮询时间间隔;
     *             false:读取不同的数据，使用相同的轮询时间间隔;
     * @return Disposable 用于取消订阅
     */
    public Disposable startPeriodicReading(
            Consumer<QixingDataResult> onNext,
            Consumer<Throwable> onError,
            boolean flag) {
        if (flag)
            return startPeriodicReading2(onNext, onError);
        return startPeriodicReading1(onNext, onError);
    }


    /**
     * 执行单次琦星数据读取
     * 点击一次，执行一次完整的数据读取
     *
     * @param onSuccess 成功回调
     * @param onError   错误回调
     * @return Disposable
     */
    public Disposable sendSingleCommand(
            Consumer<QixingDataResult> onSuccess,
            Consumer<Throwable> onError) {

        // 定义要读取的寄存器地址和数量
        int[][] registers = new int[][]{
                {131, 1},   // 针距数据：寄存器131，读取1个
                {2102, 5},  // 计数器数据：寄存器2102，读取5个
                {2130, 2},  // 状态数据：寄存器2130，读取2个
                {1, 1}      // 速度数据：寄存器1，读取1个
        };

        return readDataOfQixingAsync1(registers)
                .subscribeOn(Schedulers.io())
                .timeout(readTimeout, TimeUnit.MILLISECONDS)
                .retryWhen(errors ->
                        errors.zipWith(Observable.range(1, retryCount), (error, retryCount) -> {
                                    if (isRetryableError(error)) {
                                        LogUtils.wTag(TAG, "单次读取可重试错误，第" + retryCount + "次重试: " + error.getMessage());
                                        return retryCount;
                                    } else {
                                        LogUtils.eTag(TAG, "单次读取不可重试错误: " + error.getMessage());
                                        throw new RuntimeException(error);
                                    }
                                })
                                .flatMap(retryCount ->
                                        Observable.timer(retryDelay * (1L << (retryCount - 1)), TimeUnit.MILLISECONDS)
                                )
                )
                .onErrorReturn(error -> {
                    LogUtils.eTag(TAG, "单次读取最终失败: " + error.getMessage());
                    return lastResult != null ? lastResult : createEmptyResult("最终失败: " + error.getMessage());
                })
                .doOnNext(result -> {
                    if (result.isAllSuccess()) {
                        lastResult = result;
                        LogUtils.dTag(TAG, "单次数据读取成功");
                    } else {
                        LogUtils.wTag(TAG, "单次数据读取部分失败: " + getErrorMessage(result));
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe(disposable -> LogUtils.iTag(TAG, "开始执行单次琦星数据读取"))
                .doOnError(error -> LogUtils.eTag(TAG, "单次读取异常: " + error.getMessage()))
                .subscribe(onSuccess, onError);
    }


    /**
     * 判断错误是否可重试
     */
    private boolean isRetryableError(Throwable error) {
        String message = error.getMessage();
        if (message == null)
            return true;

        // 超时错误可重试
        if (error instanceof TimeoutException)
            return true;

        // 连接错误可重试
        if (message.contains("连接") || message.contains("超时") || message.contains("网络"))
            return true;

        // 参数错误等不可重试
        return !message.contains("参数") && !message.contains("非法");

    }


    /**
     * 创建空的结果对象（用于错误情况）
     *
     * @param errorMessage 错误信息
     * @return 包含错误信息的空结果对象
     */
    private QixingDataResult createEmptyResult(String errorMessage) {
        QixingDataResult result = new QixingDataResult();

        // 设置所有数据类型的错误信息
        result.setNeedleGapError(errorMessage);
        result.setCounterError(errorMessage);
        result.setStatusError(errorMessage);
        result.setSpeedError(errorMessage);

        return result;
    }

    /**
     * 获取错误信息的辅助方法
     *
     * @param result QixingDataResult对象
     * @return 合并的错误信息
     */
    private String getErrorMessage(QixingDataResult result) {
        StringBuilder errorMsg = new StringBuilder();

        if (result.getNeedleGapError() != null) {
            errorMsg.append("针距错误: ").append(result.getNeedleGapError()).append("; ");
        }
        if (result.getCounterError() != null) {
            errorMsg.append("计数器错误: ").append(result.getCounterError()).append("; ");
        }
        if (result.getStatusError() != null) {
            errorMsg.append("状态错误: ").append(result.getStatusError()).append("; ");
        }
        if (result.getSpeedError() != null) {
            errorMsg.append("速度错误: ").append(result.getSpeedError()).append("; ");
        }

        return errorMsg.length() > 0 ? errorMsg.toString() : "未知错误";
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        stopConnectionMonitor();
        modbusManager.close();
        lastResult = null;
        reconnectAttempts = 0;
        contextRef = null;  // 清除Context引用
        LogUtils.iTag(TAG, "资源清理完成");
    }

}