package com.supreme.smart.sewing;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.speech.RecognizerIntent;
import android.speech.tts.TextToSpeech;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.barcode.BarcodeScanningActivity;
import com.supreme.smart.sewing.barcode.QRCodeScanningActivity;
import com.supreme.smart.sewing.databinding.MainBinding;
import com.supreme.smart.sewing.face.FaceDetectionActivity;
import com.supreme.smart.sewing.finger.FingerprintActivity;
import com.supreme.smart.sewing.lang.LanguageSettingsActivity;
import com.supreme.smart.sewing.modbus.ModbusActivity;
import com.supreme.smart.sewing.ocr.TextRecognitionActivity;
import com.supreme.smart.sewing.tokenizer.CommandProcessor;
import com.supreme.smart.sewing.update.UpgradeInfo;
import com.supreme.smart.sewing.update.UpgradeManager;
import com.supreme.smart.sewing.utils.LocaleHelper;
import com.supreme.smart.sewing.utils.PermissionHelper;
import com.supreme.smart.sewing.voice.component.VoiceUsageType;
import com.supreme.smart.sewing.voice.example.ChatActivity;
import com.supreme.smart.sewing.voice.example.VoiceInputActivity;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionAction;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionCallback;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionExecutor;

import net.gotev.speech.Speech;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicReference;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private MainBinding binding;

    private EditText hiddenEditText;
    private InputMethodManager imm;

    // 使用新的Activity Result API
    private ActivityResultLauncher<Intent> voiceInputLauncher;
    private PermissionHelper permissionHelper;

    // 语音识别执行器实例
    private VoiceRecognitionExecutor voiceExecutor;

    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase)));
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        binding = DataBindingUtil.setContentView(this, R.layout.main);

        hiddenEditText = binding.editText;
        imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

        // 初始化权限管理器和其他组件
        initializeComponents();

        // 请求权限
        requestPermissions();

        // 初始化智能输入处理器
        SmartInputHandler inputHandler = new SmartInputHandler();
        hiddenEditText.addTextChangedListener(inputHandler);

        // 设置隐藏EditText的触摸事件
        hiddenEditText.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                hiddenEditText.requestFocus();
                hiddenEditText.setSelection(hiddenEditText.getText().length());
            }
            return true;
        });

        initVoiceRecognition();
        setupTouchListener();

        initTextToSpeech();

    }

    /**
     * 初始化 TTS
     */
    private void initTextToSpeech() {
        Speech.init(this, getPackageName(), status -> {
            switch (status) {
                case TextToSpeech.SUCCESS:
                    LogUtils.iTag(TAG, "TextToSpeech engine successfully started");
                    break;

                case TextToSpeech.ERROR:
                    LogUtils.eTag(TAG, "Error while initializing TextToSpeech engine!");
                    break;

                default:
                    LogUtils.eTag(TAG, "Unknown TextToSpeech status: " + status);
                    break;
            }
        });
    }


    /**
     * 初始化组件
     */
    private void initializeComponents() {
        // 语音输入结果处理器
        voiceInputLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                this::handleVoiceInputResult
        );

        // 权限管理器
        permissionHelper = new PermissionHelper(this);

        // 一键设置通知渠道
        setupNotificationChannels();
    }

    /**
     * 设置通知渠道
     */
    private void setupNotificationChannels() {
        try {
            LogUtils.iTag(TAG, "开始设置通知渠道");

            // 使用 PermissionHelper 的一键设置功能
            permissionHelper.setupAllNotificationChannels();

            // 输出渠道状态用于调试
            String channelStatus = permissionHelper.getAllNotificationChannelsStatus();
            LogUtils.dTag(TAG, channelStatus);

            LogUtils.iTag(TAG, "通知渠道设置完成");

        } catch (Exception e) {
            LogUtils.eTag(TAG, "设置通知渠道失败", e);
        }
    }

    /**
     * 处理语音输入结果
     */
    private void handleVoiceInputResult(ActivityResult result) {
        if (result.getResultCode() == RESULT_OK && result.getData() != null) {
            // 处理语音识别结果
            ArrayList<String> results = result.getData().getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS);
            if (results != null && !results.isEmpty()) {
                String voiceText = results.get(0);
                LogUtils.dTag("VoiceInput", "语音识别结果: " + voiceText);

                // 将语音识别结果设置到隐藏的EditText中
                hiddenEditText.setText(voiceText);

                // 处理用户输入
                processUserInput(voiceText);

                Toast.makeText(this, "语音输入: " + voiceText, Toast.LENGTH_SHORT).show();
            }
        } else {
            LogUtils.dTag("VoiceInput", "语音输入取消或失败");
        }
    }

    /**
     * 请求权限
     */
    private void requestPermissions() {
        permissionHelper.requestPermissions((allEssentialGranted, deniedPermissions) -> {
            if (!allEssentialGranted) {
                // 如果录音权限被拒绝，禁用语音按钮
                if (deniedPermissions.contains("录音")) {
                    binding.btnVoiceInput.setEnabled(false);
                }
            }
        });
    }


    // 智能输入处理器
    private class SmartInputHandler implements TextWatcher {
        private final Handler delayHandler = new Handler(Looper.getMainLooper());
        private Runnable inputCompleteRunnable;
        private static final int INPUT_DELAY_MS = 1500; // 1.5秒延迟检测输入完成
        private final AtomicReference<String> lastText = new AtomicReference<>("");

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // 记录变化前的文本
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 实时文本变化处理
            //LogUtils.dTag("SmartInput", "文本变化: " + s.toString());

            // 检测是否为语音输入（批量文本插入）
            boolean isVoiceInputActive = false;
            if (!isVoiceInputActive && count > 1 && before == 0) {
                //LogUtils.dTag("SmartInput", "检测到可能的语音输入");
                onVoiceInputDetected(s.toString());
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            String currentText = s.toString();

            // 取消之前的延迟任务
            if (inputCompleteRunnable != null) {
                delayHandler.removeCallbacks(inputCompleteRunnable);
            }

            // 如果文本不为空，设置新的延迟任务
            if (StringUtils.isNotEmpty(currentText)) {
                inputCompleteRunnable = () -> {
                    if (!currentText.equals(lastText.get())) {
                        onInputComplete(currentText);
                        lastText.set(currentText);
                    }
                };
                delayHandler.postDelayed(inputCompleteRunnable, INPUT_DELAY_MS);
            }
        }

        // 语音输入检测回调
        private void onVoiceInputDetected(String text) {
            LogUtils.dTag("SmartInput", "语音输入检测: " + text);
            //Toast.makeText(MainActivity.this, "检测到语音输入", Toast.LENGTH_SHORT).show();
        }

        // 输入完成回调
        private void onInputComplete(String text) {
            LogUtils.dTag("SmartInput", "输入完成: " + text);
            //Toast.makeText(MainActivity.this, "输入完成: " + text, Toast.LENGTH_SHORT).show();

            // 这里可以添加具体的业务逻辑
            processUserInput(text);
        }
    }

    // 处理用户输入的业务逻辑
    private void processUserInput(String input) {
        LogUtils.dTag("UserInput", "处理用户输入: " + input);

        CommandProcessor commandProcessor = new CommandProcessor();
        commandProcessor.processInput(input);

        // 清空输入框
        hiddenEditText.setText("");
    }


    // 启动输入法的语音输入功能
    private void startInputMethodVoiceInput() {
        try {
            // 确保隐藏的EditText获得焦点
            hiddenEditText.requestFocus();

            // 显示软键盘
            imm.showSoftInput(hiddenEditText, InputMethodManager.SHOW_IMPLICIT);

            // 延迟一下再尝试启动语音输入
            hiddenEditText.postDelayed(() -> {
                try {
                    // 尝试通过Intent启动语音输入
                    Intent voiceIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
                    voiceIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
                    voiceIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault());
                    voiceIntent.putExtra(RecognizerIntent.EXTRA_PROMPT, "请说话...");

                    // 检查是否有应用可以处理语音识别
                    if (voiceIntent.resolveActivity(getPackageManager()) != null) {
                        // 使用新的Activity Result API
                        voiceInputLauncher.launch(voiceIntent);
                    } else {
                        // 如果没有语音识别应用，显示提示
                        Toast.makeText(this, "请在键盘上点击语音输入按钮", Toast.LENGTH_LONG).show();
                    }
                } catch (Exception e) {
                    LogUtils.eTag("VoiceInput", "启动语音输入失败: " + Log.getStackTraceString(e));
                    Toast.makeText(this, "请在键盘上点击语音输入按钮", Toast.LENGTH_LONG).show();
                }
            }, 300);

        } catch (Exception e) {
            LogUtils.eTag("VoiceInput", "启动输入法语音输入失败: " + Log.getStackTraceString(e));
            Toast.makeText(this, "启动语音输入失败", Toast.LENGTH_SHORT).show();
        }
    }

    public void doQRCodeClick(View view) {
        startActivity(new Intent(this, QRCodeScanningActivity.class));
    }

    public void doBarCodeClick(View view) {
        startActivity(new Intent(this, BarcodeScanningActivity.class));
    }

    public void doTextOcrClick(View view) {
        startActivity(new Intent(this, TextRecognitionActivity.class));
    }


    public void doFaceClick(View view) {
        startActivity(new Intent(this, FaceDetectionActivity.class));
    }

    public void doFingerprintClick(View view) {
        startActivity(new Intent(this, FingerprintActivity.class));
    }


    public void doSerialPort(View view) {
        //startActivity(new Intent(this, SerialActivity.class));
        startActivity(new Intent(this, ModbusActivity.class));
    }

    public void doUsbSerialPort(View view) {
    }

    public void doLanguageSettings(View view) {
        startActivity(new Intent(this, LanguageSettingsActivity.class));
    }

    public void doCheckUpdate(View view) {
        // 首先获取UpdateManager实例
        UpgradeManager upgradeManager = UpgradeManager.getInstance(this);

        // 创建或获取UpdateInfo对象
        UpgradeInfo upgradeInfo = upgradeManager.createSampleUpdateInfo(); // 或者从其他地方获取

        // 使用UpdateManager的方法启动，它会自动传递updateInfo
        upgradeManager.showUpdateDialog(upgradeInfo);
    }

    public void doVoiceInput(View view) {
        startActivity(new Intent(this, VoiceInputActivity.class));
    }

    @SuppressLint("ClickableViewAccessibility")
    public void doChatClick(View view) {
        startActivity(new Intent(this, ChatActivity.class));
    }

    /**
     * 测试电池优化权限按钮点击事件
     */
    public void doTestBatteryOptimization(View view) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        intent.setData(Uri.parse("package:" + this.getPackageName()));
        this.startActivity(intent);
//
//        if (permissionHelper != null) {
//            // 使用调试方法测试电池优化权限
//            permissionHelper.debugTestBatteryOptimization();
//
//            // 同时显示当前状态
//            boolean isOptimized = permissionHelper.isIgnoringBatteryOptimizations();
//            String statusMessage = "当前电池优化状态: " + (isOptimized ? "已加入白名单" : "未加入白名单");
//            Toast.makeText(this, statusMessage, Toast.LENGTH_LONG).show();
//            LogUtils.iTag(TAG, statusMessage);
//
//            // 显示详细状态信息
//            String detailStatus = permissionHelper.getPowerManagementPermissionStatus();
//            LogUtils.iTag(TAG, "详细权限状态:\n" + detailStatus);
//
//        } else {
//            LogUtils.eTag(TAG, "PermissionHelper 未初始化");
//            Toast.makeText(this, "权限管理器未初始化", Toast.LENGTH_SHORT).show();
//        }
    }

    private void initVoiceRecognition() {
        // 创建语音识别执行器实例
        voiceExecutor = new VoiceRecognitionExecutor();

        // 初始化语音识别执行器，指定为语音输入场景
        voiceExecutor.initialize(this, new VoiceRecognitionCallback() {
            private final Context context = MainActivity.this;

            @Override
            public void onRealTimeTextUpdate(String text) {
                LogUtils.dTag(TAG, "实时文本更新: " + text);
            }

            @Override
            public void onRecognitionComplete(String finalText, VoiceRecognitionAction action) {
                LogUtils.dTag(TAG, "识别完成 - 文本: " + finalText + ", 操作: " + action);

                switch (action) {
                    case SEND_TEXT:
                        if (!TextUtils.isEmpty(finalText)) {
                            Speech.getInstance().say(finalText);
                            processUserInput(finalText);
                        } else {
                            runOnUiThread(() -> Toast.makeText(context,
                                    getString(R.string.voice_no_speech_detected), Toast.LENGTH_SHORT).show());
                        }
                        break;

                    case CANCEL:
                        runOnUiThread(() -> Toast.makeText(context,
                                getString(R.string.voice_input_cancelled), Toast.LENGTH_SHORT).show());
                        break;

                    default:
                        break;
                }
            }

            @Override
            public void onAudioDataCollected(byte[] audioData) {

            }

            @Override
            public void onActionChanged(VoiceRecognitionAction action) {
                LogUtils.dTag(TAG, "操作状态变更: " + action);
            }

            @Override
            public void onError(String error) {
                LogUtils.eTag(TAG, "语音识别错误: " + error);
                runOnUiThread(() -> Toast.makeText(context,
                        getString(R.string.voice_recognition_error) + error, Toast.LENGTH_SHORT).show());
            }

            // 新增：语音转文字回调方法（MainActivity中不使用，提供空实现）
            @Override
            public void onVoiceToTextStart() {
            }

            @Override
            public void onVoiceToTextProgress(String progress) {
            }

            @Override
            public void onVoiceToTextComplete(String text) {
            }

            @Override
            public void onVoiceToTextError(String error) {
            }
        }, VoiceUsageType.VOICE_INPUT);

    }

    @SuppressLint("ClickableViewAccessibility")
    private void setupTouchListener() {
        binding.btnCommand.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    if (voiceExecutor != null) {
                        voiceExecutor.handleTouchDown(event);
                    }
                    return true;

                case MotionEvent.ACTION_MOVE:
                    if (voiceExecutor != null) {
                        voiceExecutor.handleTouchMove(event);
                    }
                    return true;

                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    if (voiceExecutor != null) {
                        voiceExecutor.handleTouchUp();
                    }
                    return true;

                default:
                    return false;
            }
        });

    }

    @Override
    protected void onResume() {
        super.onResume();
        // 当从其他页面返回时，重新初始化语音识别器以确保功能正常
        if (voiceExecutor != null) {
            LogUtils.dTag(TAG, "onResume - 重新初始化语音识别器");
            voiceExecutor.reinitializeVoiceRecognition();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (voiceExecutor != null) {
            voiceExecutor.cleanup();
            voiceExecutor = null;
        }

        Speech.getInstance().shutdown();
    }
    

}