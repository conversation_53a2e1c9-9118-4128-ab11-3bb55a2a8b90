
package com.supreme.smart.sewing.barcode;

import android.content.Intent;
import android.graphics.Point;
import android.widget.ImageView;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;

import com.google.mlkit.vision.barcode.common.Barcode;
import com.king.camera.scan.AnalyzeResult;
import com.king.camera.scan.CameraScan;
import com.king.camera.scan.util.PointUtils;
import com.king.mlkit.vision.barcode.QRCodeCameraScanActivity;
import com.supreme.smart.sewing.R;

import java.util.ArrayList;
import java.util.List;

/** 
 * 扫描二维码示例 
 * <AUTHOR> href="mailto:<EMAIL>">Jenly</a> 
 */ 
public class QRCodeScanningActivity extends QRCodeCameraScanActivity {

    private ImageView ivResult;

    @Override
    public void initUI() {
        super.initUI();

        ivResult = findViewById(R.id.ivResult);

        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                if (viewfinderView.isShowPoints()) {
                    // 如果是结果点显示时，用户点击了返回键，则认为是取消选择当前结果，重新开始扫码
                    ivResult.setImageResource(0);
                    viewfinderView.showScanner();
                    getCameraScan().setAnalyzeImage(true);
                } else {
                    finish();
                }
            }
        });
    }

    @Override
    public void initCameraScan(@NonNull CameraScan<List<Barcode>> cameraScan) {
        super.initCameraScan(cameraScan);
        cameraScan.setPlayBeep(true)
                .setVibrate(true)
                .bindFlashlightView(ivFlashlight);
    }

    @Override
    public int getLayoutId() {
        return R.layout.qrcode_scan;
    }

    @Override
    public void onScanResultCallback(AnalyzeResult<List<Barcode>> result) {
        // 停止分析
        getCameraScan().setAnalyzeImage(false);
        final List<Barcode> results = result.getResult();

        // 取预览当前帧图片并显示，为结果点提供参照
        ivResult.setImageBitmap(previewView.getBitmap());
        ArrayList<Point> points = new ArrayList<>();
        int width = result.getImageWidth();
        int height = result.getImageHeight();
        
        for (Barcode barcode : results) {
            if (barcode.getBoundingBox() != null) {
                // 将实际的结果中心点坐标转换成界面预览的坐标
                Point point = PointUtils.transform(
                        barcode.getBoundingBox().centerX(),
                        barcode.getBoundingBox().centerY(),
                        width,
                        height,
                        viewfinderView.getWidth(),
                        viewfinderView.getHeight()
                );
                points.add(point);
            }
        }
        
        // 设置Item点击监听
        viewfinderView.setOnItemClickListener(index -> {
            // 显示点击Item将所在位置扫码识别的结果返回
            Intent intent = new Intent();
            intent.putExtra(CameraScan.SCAN_RESULT, results.get(index).getDisplayValue());
            setResult(RESULT_OK, intent);
            finish();

            /* 
                显示结果后，如果需要继续扫码，则可以继续分析图像 
             */
//            ivResult.setImageResource(0);
//            viewfinderView.showScanner();
//            cameraScan.setAnalyzeImage(true);
        });
        
        // 显示结果点信息
        viewfinderView.showResultPoints(points);

        if (results.size() == 1) { // 只有一个结果直接返回
            Intent intent = new Intent();
            intent.putExtra(CameraScan.SCAN_RESULT, results.get(0).getDisplayValue());
            setResult(RESULT_OK, intent);
            finish();
        }
    }
}