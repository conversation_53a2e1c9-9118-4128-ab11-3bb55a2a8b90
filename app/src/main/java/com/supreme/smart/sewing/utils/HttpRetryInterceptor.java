package com.supreme.smart.sewing.utils;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class HttpRetryInterceptor implements Interceptor {

    private final int maxRetry; // 最大重试次数
    private final long baseDelayMs; // 基础延迟时间（毫秒）
    private final boolean useExponentialBackoff; // 是否使用指数退避
    private static final int DEFAULT_RETRY_COUNT = 3;
    private static final long DEFAULT_BASE_DELAY = 1000; // 1秒

    public HttpRetryInterceptor() {
        this(DEFAULT_RETRY_COUNT);
    }

    public HttpRetryInterceptor(int maxRetry) {
        this(maxRetry, DEFAULT_BASE_DELAY, true);
    }

    public HttpRetryInterceptor(int maxRetry, long baseDelayMs, boolean useExponentialBackoff) {
        this.maxRetry = Math.max(0, maxRetry);
        this.baseDelayMs = Math.max(100, baseDelayMs); // 最小100ms
        this.useExponentialBackoff = useExponentialBackoff;
    }

    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = null;
        IOException lastException = null;
        int retryNum = 0;

        while (retryNum <= maxRetry) {
            try {
                // 关闭之前的响应
                if (response != null) {
                    response.close();
                }

                response = chain.proceed(request);

                // 检查响应是否成功或者是否应该重试
                if (response.isSuccessful() || !shouldRetry(response, retryNum)) {
                    return response;
                }

                LogUtils.w("请求失败，状态码: " + response.code() + ", 第" + (retryNum + 1) + "次重试");

            } catch (IOException e) {
                lastException = e;
                LogUtils.w("请求异常: " + e.getMessage() + ", 第" + (retryNum + 1) + "次重试");

                // 检查是否应该重试此类异常
                if (!shouldRetryException(e) || retryNum >= maxRetry) {
                    throw e;
                }
            }

            retryNum++;

            // 如果还有重试机会，则等待后重试
            if (retryNum <= maxRetry) {
                long delayMs = calculateDelay(retryNum);
                LogUtils.i("等待 " + delayMs + "ms 后进行第" + retryNum + "次重试");
                
                try {
                    TimeUnit.MILLISECONDS.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("重试被中断", ie);
                }
            }
        }

        // 如果所有重试都失败了
        if (response != null && !response.isSuccessful()) {
            return response;
        }
        
        if (lastException != null) {
            throw lastException;
        }
        
        throw new IOException("请求失败，已达到最大重试次数: " + maxRetry);
    }

    /**
     * 判断是否应该重试响应
     */
    private boolean shouldRetry(Response response, int currentRetryCount) {
        if (currentRetryCount >= maxRetry) {
            return false;
        }

        int code = response.code();
        // 重试服务器错误和部分客户端错误
        return code >= 500 || // 5xx 服务器错误
               code == 408 || // 请求超时
               code == 429;   // 请求过多（限流）
    }

    /**
     * 判断是否应该重试异常
     */
    private boolean shouldRetryException(IOException exception) {
        // 重试网络相关异常
        return exception instanceof SocketTimeoutException ||
               exception instanceof ConnectException ||
               exception instanceof UnknownHostException ||
               (exception.getMessage() != null && 
                (exception.getMessage().contains("timeout") ||
                 exception.getMessage().contains("connection reset") ||
                 exception.getMessage().contains("broken pipe")));
    }

    /**
     * 计算延迟时间
     */
    private long calculateDelay(int retryNum) {
        if (useExponentialBackoff) {
            // 指数退避: baseDelay * 2^(retryNum-1)
            return baseDelayMs * (1L << (retryNum - 1));
        } else {
            // 固定延迟
            return baseDelayMs;
        }
    }
}