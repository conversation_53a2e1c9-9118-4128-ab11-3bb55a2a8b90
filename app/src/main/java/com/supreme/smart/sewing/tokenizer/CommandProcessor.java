package com.supreme.smart.sewing.tokenizer;

import com.blankj.utilcode.util.LogUtils;

import java.util.List;

/**
 * hanlp 分词器
 */
public class CommandProcessor {
    private static final String TAG = CommandProcessor.class.getSimpleName();
    private final CommandMatcher matcher = new CommandMatcher();
    private final CommandTextProcessor textProcessor = new CommandTextProcessor();

    public void processInput(String input) {
        // 1. 清洗文本
        String process = textProcessor.process(input);
        // 2. 分词处理
        List<String> words = textProcessor.segment(process);
        LogUtils.dTag(TAG, "分词结果: " + words);
        // 3. 命令匹配
        String command = matcher.matchCommand(words);

        if (command != null) {
            executeCommand(command, process);
        } else {
            handleUnknownCommand(process);
        }
    }


    private void executeCommand(String command, String input) {
        // 执行命令对应的操作
        LogUtils.iTag(TAG, "收到: " + input + " , 执行命令: " + command );
//        switch (command) {
//            case "OPEN_APP":
//                // 执行打开应用的操作
//                LogUtils.iTag(TAG, "执行打开应用的操作");
//                break;
//            case "CLOSE_APP":
//                // 执行关闭应用的操作
//                LogUtils.iTag(TAG, "执行关闭应用的操作");
//                break;
//            // 添加更多命令处理...
//        }
    }

    private void handleUnknownCommand(String input) {
        // 处理无法识别的命令
        LogUtils.iTag(TAG, "无法识别的命令: " + input);
    }
}

