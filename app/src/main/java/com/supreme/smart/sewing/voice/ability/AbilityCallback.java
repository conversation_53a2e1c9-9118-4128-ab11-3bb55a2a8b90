package com.supreme.smart.sewing.voice.ability;

/**
 * 能力回调接口
 */
public interface AbilityCallback {

    /**
     * 开始
     */
    void onAbilityBegin();

    /**
     * 能力结果输出
     * @param result 结果
     */
    void onAbilityResult(String result);

    /**
     * 结束
     * @param code 错误码
     * @param error 错误信息
     */
    void onAbilityError(int code, Throwable error);

    /**
     * 能力结束
     */
    void onAbilityEnd();

    /**
     * 音量更新回调
     * @param volume 当前音量级别
     */
    default void onVolumeUpdate(int volume) {
        // 默认空实现，子类可选择实现
    }
} 
