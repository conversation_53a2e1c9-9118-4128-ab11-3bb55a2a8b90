package com.supreme.smart.sewing.voice.voice;

/**
 * 语音识别回调接口
 * 定义语音识别和语音转文字的所有回调方法
 */
public interface VoiceRecognitionCallback {
    
    /**
     * 实时文本更新回调
     * @param text 实时识别的文本
     */
    void onRealTimeTextUpdate(String text);
    
    /**
     * 语音识别完成回调
     * @param finalText 最终识别的文本
     * @param action 用户执行的操作（发送文字、发送语音、取消等）
     */
    void onRecognitionComplete(String finalText, VoiceRecognitionAction action);
    
    /**
     * 音频数据收集回调（仅聊天场景）
     * @param audioData 收集到的音频数据
     */
    void onAudioDataCollected(byte[] audioData);
    
    /**
     * 操作状态变化回调
     * @param action 新的操作状态
     */
    void onActionChanged(VoiceRecognitionAction action);
    
    /**
     * 错误回调
     * @param error 错误信息
     */
    void onError(String error);
    
    // ==================== 语音转文字回调 ====================
    
    /**
     * 语音转文字开始回调
     */
    void onVoiceToTextStart();
    
    /**
     * 语音转文字进度回调
     * @param progress 转换进度文本（实时识别结果）
     */
    void onVoiceToTextProgress(String progress);
    
    /**
     * 语音转文字完成回调
     * @param text 转换完成的文字
     */
    void onVoiceToTextComplete(String text);
    
    /**
     * 语音转文字失败回调
     * @param error 错误信息
     */
    void onVoiceToTextError(String error);
}
