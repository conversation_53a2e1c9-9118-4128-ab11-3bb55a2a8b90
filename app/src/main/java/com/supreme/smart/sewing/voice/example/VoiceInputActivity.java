package com.supreme.smart.sewing.voice.example;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.databinding.VoiceInputBinding;
import com.supreme.smart.sewing.voice.component.VoiceUsageType;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionAction;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionCallback;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionExecutor;

/**
 * 语音输入Activity
 * 支持两种操作：直接松开转换文字、左滑取消
 */
public class VoiceInputActivity extends AppCompatActivity {

    private static final String TAG = "VoiceInputActivity";

    // ViewBinding
    private VoiceInputBinding binding;

    // 语音识别执行器实例
    private VoiceRecognitionExecutor voiceExecutor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 启用边缘到边缘(EdgeToEdge)显示模式，让内容延伸到系统栏(状态栏和导航栏)下面
        EdgeToEdge.enable(this);


        // 初始化ViewBinding
        binding = DataBindingUtil.setContentView(this, R.layout.voice_input);

        initVoiceRecognition();
        setupTouchListener();
    }


    private void initVoiceRecognition() {
        // 创建语音识别执行器实例
        voiceExecutor = new VoiceRecognitionExecutor();

        // 初始化语音识别执行器，指定为语音输入场景
        voiceExecutor.initialize(this, new VoiceRecognitionCallback() {
            private final Context context = VoiceInputActivity.this;

            @Override
            public void onRealTimeTextUpdate(String text) {
                LogUtils.dTag(TAG, "实时文本更新: " + text);
            }

            @Override
            public void onRecognitionComplete(String finalText, VoiceRecognitionAction action) {
                LogUtils.dTag(TAG, "识别完成 - 文本: " + finalText + ", 操作: " + action);

                switch (action) {
                    case SEND_TEXT:
                        if (!TextUtils.isEmpty(finalText)) {
                            runOnUiThread(() -> binding.etResult.setText(finalText));
                        } else {
                            runOnUiThread(() -> Toast.makeText(context,
                                    getString(R.string.voice_no_speech_detected), Toast.LENGTH_SHORT).show());
                        }
                        break;

                    case CANCEL:
                        runOnUiThread(() -> Toast.makeText(context,
                                getString(R.string.voice_input_cancelled), Toast.LENGTH_SHORT).show());
                        break;

                    default:
                        break;
                }
            }

            @Override
            public void onAudioDataCollected(byte[] audioData) {

            }


            @Override
            public void onActionChanged(VoiceRecognitionAction action) {
                LogUtils.dTag(TAG, "操作状态变更: " + action);
            }

            @Override
            public void onError(String error) {
                LogUtils.eTag(TAG, "语音识别错误: " + error);
                runOnUiThread(() -> Toast.makeText(context,
                        getString(R.string.voice_recognition_error) + error, Toast.LENGTH_SHORT).show());
            }

            @Override
            public void onVoiceToTextStart() {
            }

            @Override
            public void onVoiceToTextProgress(String progress) {
            }

            @Override
            public void onVoiceToTextComplete(String text) {
            }

            @Override
            public void onVoiceToTextError(String error) {
            }
        }, VoiceUsageType.VOICE_INPUT);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void setupTouchListener() {
        binding.btnVoiceInput.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    if (voiceExecutor != null) {
                        voiceExecutor.handleTouchDown(event);
                    }
                    return true;

                case MotionEvent.ACTION_MOVE:
                    if (voiceExecutor != null) {
                        voiceExecutor.handleTouchMove(event);
                    }
                    return true;

                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    if (voiceExecutor != null) {
                        voiceExecutor.handleTouchUp();
                    }
                    return true;

                default:
                    return false;
            }
        });

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理语音识别执行器资源
        if (voiceExecutor != null) {
            voiceExecutor.cleanup();
            voiceExecutor = null;
        }

        // 清理ViewBinding
        binding = null;

        LogUtils.dTag(TAG, "Activity销毁，资源已清理");
    }

    public void doBack(View view) {
        finish();
    }
}
