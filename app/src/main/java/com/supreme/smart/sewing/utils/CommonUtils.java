package com.supreme.smart.sewing.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.DataOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CommonUtils {

    public enum DatePart {
        YEAR, QUARTER, MONTH, WEEK, DAY, HOUR, MINUTE, SECOND, MILLISECOND
    }

    public static float[] copyArray(float[] src) {
        if (src != null)
            return Arrays.copyOf(src, src.length);
        return null;
    }

    public static <T> List<T> copyList(List<T> src) {
        if (src != null)
            return new ArrayList<>(src);
        return null;
    }


    /**
     * @param value        byte数值
     * @param fillWithZero 转换后如果不足两位，以0补足
     * @return String
     */
    public static String byteToHex(byte value, boolean fillWithZero) {
        final String retValue;
        if (fillWithZero) {
            retValue = String.format(Locale.US, "%02X", value);
        } else {
            retValue = String.format(Locale.US, "%01X", value);
        }

        return retValue;
    }

    /**
     * @param value        short数值
     * @param fillWithZero 转换后如果不足四位，以0补足
     * @return String
     */
    public static String shortToHex(short value, boolean fillWithZero) {
        final String retValue;
        if (fillWithZero) {
            retValue = String.format(Locale.US, "%04X", value);
        } else {
            retValue = String.format(Locale.US, "%01X", value);
        }
        return retValue;
    }

    /**
     * @param value        int数值
     * @param fillWithZero 转换后如果不足八位，以0补足
     * @return String
     */
    public static String intToHex(int value, boolean fillWithZero) {
        final String retValue;
        if (fillWithZero) {
            retValue = String.format(Locale.US, "%08X", value);
        } else {
            retValue = String.format(Locale.US, "%01X", value);
        }
        return retValue;
    }

    /**
     * @param value        long数值
     * @param fillWithZero 转换后如果不足16位，以0补足
     * @return String
     */
    public static String longToHex(long value, boolean fillWithZero) {
        final String retValue;
        if (fillWithZero) {
            retValue = String.format(Locale.US, "%016X", value);
        } else {
            retValue = String.format(Locale.US, "%01X", value);
        }
        return retValue;
    }

    /**
     * 取得BCC码
     *
     * @param values byte数组
     * @return BCC码
     */
    public static byte getBCCCode(byte[] values) {
        byte retValue;
        int total = 0;
        for (byte val : values) {
            total += val;
        }
        retValue = (byte) (~total + 1);
        return retValue;
    }

    public static short[] byteArrayToShortArray(byte[] values) {
        short[] retValue = null;
        if (values != null && values.length % 2 == 0) {
            retValue = new short[values.length / 2];
            int index = 0;
            for (int i = 0; i < values.length; i += 2) {
                byte[] temp = Arrays.copyOfRange(values, i, i + 2);
                String sb = byteToHex(temp[0], true) +
                        byteToHex(temp[1], true);
                retValue[index] = (short) Integer.parseInt(sb, 16);
                index++;
            }
        }
        return retValue;
    }

    public static boolean shortArrayIsZero(short[] values) {
        boolean retValue = true;
        if (values != null) {
            for (short value : values) {
                if (value != (short) 0 && value != (short) -1) {
                    retValue = false;
                    break;
                }
            }
        }
        return retValue;
    }

    /**
     * 将short转换成byte数组
     *
     * @param value short数值
     * @return byte数组
     */
    public static byte[] shortToByteArray(short value) {
        byte[] retValue = new byte[2];
        retValue[0] = (byte) ((value >> 8) & 0xFF);
        retValue[1] = (byte) (value & 0xFF);
        return retValue;
    }

    public static byte[] shortArrayToByteArray(short[] values) {
        byte[] result = new byte[2 * values.length];
        int index = 0;
        for (short value : values) {
            byte[] bytes = shortToByteArray(value);
            result[index] = bytes[0];
            result[index + 1] = bytes[1];
            index += 2;
        }
        return result;
    }

    /**
     * 将int转换成byte数组
     *
     * @param value int数值
     * @return byte数组
     */
    public static byte[] intToByteArray(int value) {
        byte[] retValue = new byte[4];
        retValue[0] = (byte) ((value >> 24) & 0xFF);
        retValue[1] = (byte) ((value >> 16) & 0xFF);
        retValue[2] = (byte) ((value >> 8) & 0xFF);
        retValue[3] = (byte) (value & 0xFF);
        return retValue;
    }

    /**
     * 将long转换成byte数组
     *
     * @param value long数值
     * @return byte[]
     */
    public static byte[] longToByteArray(long value) {
        byte[] retValue = new byte[8];
        retValue[0] = (byte) ((value >> 56) & 0xFF);
        retValue[1] = (byte) ((value >> 48) & 0xFF);
        retValue[2] = (byte) ((value >> 40) & 0xFF);
        retValue[3] = (byte) ((value >> 32) & 0xFF);
        retValue[4] = (byte) ((value >> 24) & 0xFF);
        retValue[5] = (byte) ((value >> 16) & 0xFF);
        retValue[6] = (byte) ((value >> 8) & 0xFF);
        retValue[7] = (byte) (value & 0xFF);
        return retValue;
    }

    /**
     * 将byte数组转换成long
     *
     * @param values byte数组
     * @return long数值
     */
    public static long byteArrayToLong(byte[] values) {
        long retValue = 0;
        if (values != null && values.length == 8) {
            retValue = ByteBuffer.wrap(values).getLong();
        }
        return retValue;
    }


    public static int shortArrayToInt(short[] values) {
        int retValue = 0;
        if (values != null && values.length == 2) {
            final byte[] v1 = shortToByteArray(values[0]);
            final byte[] v2 = shortToByteArray(values[1]);
            final byte[] v3 = new byte[]{v1[0], v1[1], v2[0], v2[1]};
            retValue = ByteBuffer.wrap(v3).getInt();
        }
        return retValue;
    }

    public static short[] intToShortArray(int value) {
        final byte[] bytes = intToByteArray(value);
        final byte[] bytes1 = new byte[]{bytes[0], bytes[1]};
        final byte[] bytes2 = new byte[]{bytes[2], bytes[3]};
        final short v1 = byteArrayToShort(bytes1);
        final short v2 = byteArrayToShort(bytes2);
        return new short[]{v1, v2};
    }

    public static long shortArrayToLong(short[] values) {
        long retValue = 0;
        if (values != null && values.length == 4) {
            final byte[] v1 = shortToByteArray(values[0]);
            final byte[] v2 = shortToByteArray(values[1]);
            final byte[] v3 = shortToByteArray(values[2]);
            final byte[] v4 = shortToByteArray(values[3]);
            final byte[] v5 = new byte[]{v1[0], v1[1], v2[0], v2[1], v3[0], v3[1], v4[0], v4[1]};
            retValue = ByteBuffer.wrap(v5).getLong();
        }
        return retValue;
    }

    public static long intArrayToLong(int[] values) {
        long retValue = 0;
        if (values != null && values.length == 2) {
            byte[] v1 = intToByteArray(values[0]);
            byte[] v2 = intToByteArray(values[1]);
            final byte[] v5 = new byte[]{v1[0], v1[1], v1[2], v1[3], v1[0], v2[1], v2[2], v2[3]};
            retValue = ByteBuffer.wrap(v5).getLong();
        }
        return retValue;
    }

    /**
     * 将byte数组转换成无符号16位整数(大端序)
     *
     * @param bytes 字节数组
     * @return 无符号16位整数
     */
    public static int byteArrayToUnsigned16(byte[] bytes, boolean isBigEndian) {
        // 确保有足够的字节
        if (bytes.length < 2) {
            throw new IllegalArgumentException("数组长度不足");
        }

        // 大端序（Big Endian）- 高位字节在前
        if (isBigEndian)
            return (bytes[0] & 0xFF) << 8 | (bytes[1] & 0xFF);

        return (bytes[1] & 0xFF) << 8 | (bytes[0] & 0xFF);

    }

    /**
     * 将byte数组转换成无符号32位整数
     *
     * @param bytes 字节数组
     * @return 无符号16位整数
     */
    public static long byteArrayToUnsigned32(byte[] bytes, boolean isBigEndian) {
        // 确保有足够的字节
        if (bytes.length < 4) {
            throw new IllegalArgumentException("数组长度不足");
        }

        // 大端序（Big Endian）- 高位字节在前
        if (isBigEndian)
            return ((long) (bytes[0] & 0xFF) << 24) |
                    ((long) (bytes[1] & 0xFF) << 16) |
                    ((long) (bytes[2] & 0xFF) << 8) |
                    (long) (bytes[3] & 0xFF);


        return ((long) (bytes[3] & 0xFF) << 24) |
                ((long) (bytes[2] & 0xFF) << 16) |
                ((long) (bytes[1] & 0xFF) << 8) |
                (long) (bytes[0] & 0xFF);
    }


    /**
     * 将字节数组转换为布尔(byte)数组
     */
    public static boolean[] bytesToBooleans(byte[] bytes, int quantity) {
        boolean[] booleans = new boolean[quantity];
        for (int i = 0; i < quantity; i++) {
            int byteIndex = i / 8;
            int bitIndex = i % 8;
            if (byteIndex < bytes.length) {
                booleans[i] = (bytes[byteIndex] & (1 << bitIndex)) != 0;
            }
        }
        return booleans;
    }

    /**
     * 取得整数的高位数
     *
     * @param value 传入的整数
     * @return 高位数
     */
    public static byte hiOfInteger(int value) {
        return (byte) ((value >> 8) & 0xff);
    }

    /**
     * 取得整数的低位数
     *
     * @param value 传入的整数
     * @return 低位数
     */
    public static byte loOfInteger(int value) {
        return (byte) (value & 0xff);
    }

    /**
     * 时间格式转换到字符串 - 使用Java 8时间API，线程安全
     *
     * @param date      需要转换的时间格式
     * @param formatStr 需要格式的目标字符串
     * @return String 返回转换后的时间字符串
     */
    public static String dateToString(Date date, String formatStr) {
        if (date == null) {
            return null;
        }
        if (StringUtils.isEmpty(formatStr)) {
            formatStr = "yyyy-MM-dd HH:mm:ss";
        }

        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatStr);
        return localDateTime.format(formatter);
    }

    /**
     * 字符串转换到时间格式 - 使用Java 8时间API，线程安全
     *
     * @param dateStr   需要转换的字符串
     * @param formatStr 需要格式的目标字符串
     * @return Date 返回转换后的时间
     */
    public static Date stringToDate(String dateStr, String formatStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        if (StringUtils.isEmpty(formatStr)) {
            formatStr = "yyyy-MM-dd HH:mm:ss";
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatStr);
            LocalDateTime localDateTime = LocalDateTime.parse(dateStr, formatter);
            return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            return null;
        }
    }

    public static int getDatePart(Date date, DatePart datePart) {
        if (date == null) {
            return 0;
        }

        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        switch (datePart) {
            case YEAR:
                return localDateTime.getYear();
            case MONTH:
                return localDateTime.getMonthValue();
            case WEEK:
                return localDateTime.getDayOfWeek().getValue();
            case DAY:
                return localDateTime.getDayOfMonth();
            case HOUR:
                return localDateTime.getHour();
            case MINUTE:
                return localDateTime.getMinute();
            case QUARTER:
                int month = localDateTime.getMonthValue();
                return (month - 1) / 3 + 1;
            case SECOND:
                return localDateTime.getSecond();
            default:
                return localDateTime.getNano() / 1_000_000; // 转换为毫秒
        }
    }

    /**
     * 比较两个日期的时间差 - 使用Java 8时间API，更精确和高效
     *
     * @param datePart  比较部位
     * @param dateBegin 开始时间
     * @param dateEnd   结束时间
     * @return 结束时间 - 开始时间
     */
    public static long dateDiff(DatePart datePart, Date dateBegin, Date dateEnd) {
        if (dateEnd == null || dateBegin == null) {
            return 0;
        }

        LocalDateTime beginDateTime = dateBegin.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endDateTime = dateEnd.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        switch (datePart) {
            case YEAR:
                return ChronoUnit.YEARS.between(beginDateTime, endDateTime);
            case QUARTER:
                long months = ChronoUnit.MONTHS.between(beginDateTime, endDateTime);
                return months / 3;
            case MONTH:
                return ChronoUnit.MONTHS.between(beginDateTime, endDateTime);
            case WEEK:
                return ChronoUnit.WEEKS.between(beginDateTime, endDateTime);
            case DAY:
                return ChronoUnit.DAYS.between(beginDateTime, endDateTime);
            case HOUR:
                return ChronoUnit.HOURS.between(beginDateTime, endDateTime);
            case MINUTE:
                return ChronoUnit.MINUTES.between(beginDateTime, endDateTime);
            case SECOND:
                return ChronoUnit.SECONDS.between(beginDateTime, endDateTime);
            default:
                return ChronoUnit.MILLIS.between(beginDateTime, endDateTime);
        }
    }

    public static String getWeekDay(Date date) {
        if (date == null) {
            return "";
        }

        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        DayOfWeek dayOfWeek = localDateTime.getDayOfWeek();

        String[] weekDays = {"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"};
        return weekDays[dayOfWeek.getValue() - 1];
    }


    public static int getWeekOfYear(Date date) {
        if (date == null) {
            return 0;
        }

        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        return localDateTime.get(weekFields.weekOfYear());
    }

    public static String getYearWeekDayShort() {
        return convertDateToYearWeekDayShort(getCurrentDate());
    }

    public static String getYearWeekDayLong() {
        return convertDateToYearWeekDayLong(getCurrentDate());
    }

    public static String convertDateToYearWeekDayShort(Date date) {
        if (date == null) {
            return null;
        }

        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        WeekFields weekFields = WeekFields.of(Locale.getDefault());

        int year = localDateTime.getYear() % 1000;
        int weekOfYear = localDateTime.get(weekFields.weekOfYear());
        int dayOfWeek = localDateTime.getDayOfWeek().getValue() % 7; // 转换为0-6，周日为0

        return String.format(Locale.US, "%02d%d%d", year, weekOfYear, dayOfWeek);
    }

    public static String convertDateToYearWeekDayLong(Date date) {
        if (date == null) {
            return null;
        }

        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        WeekFields weekFields = WeekFields.of(Locale.getDefault());

        int year = localDateTime.getYear();
        int weekOfYear = localDateTime.get(weekFields.weekOfYear());
        int dayOfWeek = localDateTime.getDayOfWeek().getValue() % 7; // 转换为0-6，周日为0

        return String.valueOf(year) + weekOfYear + dayOfWeek;
    }

    public static Date convertYearWeekDayShortToDate(String yearWeekDay) {
        if (StringUtils.isEmpty(yearWeekDay) || yearWeekDay.length() != 5) {
            return getCurrentDate();
        }

        try {
            int year = Integer.parseInt("20" + yearWeekDay.substring(0, 2));
            int weekOfYear = Integer.parseInt(yearWeekDay.substring(2, 4));
            int dayOfWeek = Integer.parseInt(yearWeekDay.substring(4, 5));

            // 使用Java 8时间API
            WeekFields weekFields = WeekFields.of(Locale.getDefault());
            LocalDate localDate = LocalDate.of(year, 1, 1)
                    .with(weekFields.weekOfYear(), weekOfYear)
                    .with(weekFields.dayOfWeek(), dayOfWeek == 0 ? 7 : dayOfWeek); // 处理周日

            return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            return getCurrentDate();
        }
    }

    public static Date convertYearWeekDayLongToDate(String yearWeekDay) {
        if (StringUtils.isEmpty(yearWeekDay) || yearWeekDay.length() != 7) {
            return getCurrentDate();
        }

        try {
            int year = Integer.parseInt(yearWeekDay.substring(0, 4));
            int weekOfYear = Integer.parseInt(yearWeekDay.substring(4, 6));
            int dayOfWeek = Integer.parseInt(yearWeekDay.substring(6, 7));

            // 使用Java 8时间API
            WeekFields weekFields = WeekFields.of(Locale.getDefault());
            LocalDate localDate = LocalDate.of(year, 1, 1)
                    .with(weekFields.weekOfYear(), weekOfYear)
                    .with(weekFields.dayOfWeek(), dayOfWeek == 0 ? 7 : dayOfWeek); // 处理周日

            return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            return getCurrentDate();
        }
    }

    public static Date getCurrentTime() {
        return Date.from(Instant.now());
    }

    public static Date getCurrentDate() {
        return Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static String getCurrentDateString() {
        return DateFormatUtils.format(getCurrentTime(), "yyyy-MM-dd");
    }

    public static String getCurrentTimeString() {
        return DateFormatUtils.format(getCurrentTime(), "yyyy-MM-dd HH:mm:ss.SSS");
    }

    public static byte[] getCurrentTimeByte() {
        byte[] retValue = new byte[7];
        Date currentTime = getCurrentTime();
        int year = getDatePart(currentTime, DatePart.YEAR);
        int month = getDatePart(currentTime, DatePart.MONTH);
        int day = getDatePart(currentTime, DatePart.DAY);
        int hour = getDatePart(currentTime, DatePart.HOUR);
        int minute = getDatePart(currentTime, DatePart.MINUTE);
        int second = getDatePart(currentTime, DatePart.SECOND);
        retValue[0] = hiOfInteger(year);
        retValue[1] = loOfInteger(year);
        retValue[2] = (byte) month;
        retValue[3] = (byte) day;
        retValue[4] = (byte) hour;
        retValue[5] = (byte) minute;
        retValue[6] = (byte) second;
        return retValue;
    }

    public static Date getDateOfDatetime(Date datetime) {
        if (datetime == null) {
            return null;
        }

        LocalDateTime localDateTime = datetime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDate localDate = localDateTime.toLocalDate();
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date getTimeOfDatetime(Date datetime) {
        if (datetime == null) {
            return null;
        }

        LocalDateTime localDateTime = datetime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalTime localTime = localDateTime.toLocalTime();
        // 使用1970-01-01作为基准日期
        LocalDateTime baseDateTime = LocalDate.of(1970, 1, 1).atTime(localTime);
        return Date.from(baseDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getLastDateOfMonth(int year, int month) {
        LocalDate lastDay = LocalDate.of(year, month, 1).with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime lastDateTime = lastDay.atTime(23, 59, 59);
        return Date.from(lastDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getFirstDateOfMonth(int year, int month) {
        LocalDate firstDay = LocalDate.of(year, month, 1);
        LocalDateTime firstDateTime = firstDay.atStartOfDay();
        return Date.from(firstDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getLastDateOfMonth(String yearMonth, String split) {
        Date retValue = null;
        if (StringUtils.isNotEmpty(yearMonth)) {
            if (split == null) split = "-";
            String[] splited = yearMonth.split(split);
            if (splited.length >= 2) {
                int year = Integer.parseInt(splited[0]);
                int month = Integer.parseInt(splited[1]);
                retValue = getLastDateOfMonth(year, month);
            }
        }
        return retValue;
    }

    public static Date getFirstDateOfMonth(String yearMonth, String split) {
        Date retValue = null;
        if (StringUtils.isNotEmpty(yearMonth)) {
            if (split == null) split = "-";
            String[] splited = yearMonth.split(split);
            if (splited.length >= 2) {
                int year = Integer.parseInt(splited[0]);
                int month = Integer.parseInt(splited[1]);
                retValue = getFirstDateOfMonth(year, month);
            }

        }
        return retValue;
    }

    public static int getLastDayOfMonth(int year, int month) {
        return LocalDate.of(year, month, 1).lengthOfMonth();
    }

    public static int getFirstDayOfMonth(int year, int month) {
        return 1; // 每月第一天总是1
    }

    public static Date getFirstDateOfYear(int year) {
        LocalDateTime firstDateTime = LocalDate.of(year, 1, 1).atStartOfDay();
        return Date.from(firstDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getLastDateOfYear(int year) {
        LocalDateTime lastDateTime = LocalDate.of(year, 12, 31).atTime(23, 59, 59);
        return Date.from(lastDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * byte数组16进制显示
     *
     * @param data byte数组
     * @return String 16进制字符串
     */
    public static String getBytesString(byte[] data) {
        StringBuilder sb = new StringBuilder();
        for (byte val : data) {
            sb.append(byteToHex(val, true)).append(" ");
        }
        return sb.toString().trim();
    }

    public static String getBytesString(byte[] data, String splitBy) {
        if (StringUtils.isEmpty(splitBy)) {
            splitBy = ",";
        }
        StringBuilder sb = new StringBuilder();
        for (byte val : data) {
            sb.append(byteToHex(val, true)).append(splitBy);
        }
        String s = sb.toString();
        if (StringUtils.isNotEmpty(s)) {
            if (s.endsWith(splitBy)) {
                s = s.substring(0, s.length() - splitBy.length());
            }
        }
        return s;
    }

    public static String getFloatArrayToString(float[] data, String splitBy) {
        if (StringUtils.isEmpty(splitBy)) {
            splitBy = ",";
        }
        StringBuilder sb = new StringBuilder();
        for (float val : data) {
            sb.append(Math.round(val)).append(splitBy);
        }
        String s = sb.toString();
        if (StringUtils.isNotEmpty(s)) {
            if (s.endsWith(splitBy)) {
                s = s.substring(0, s.length() - splitBy.length());
            }
        }
        return s;
    }

    /**
     * byte数组16进制显示
     *
     * @param data byte数组
     * @return String 16进制字符串
     */
    public static String convertByteArrayToString(byte[] data) {
        return convertByteArrayToString(data, " ");
    }

    public static String convertByteArrayToString(byte[] data, String split) {
        StringBuilder sBuffer = new StringBuilder();
        if (StringUtils.isEmpty(split)) {
            split = " ";
        }
        for (int i = 0; i < data.length; i++) {
            byte val = data[i];
            sBuffer.append(byteToHex(val, true));
            if (i != data.length - 1) {
                sBuffer.append(split);
            }
        }

        return sBuffer.toString();
    }

    public static String convertShortArrayToString(short[] data) {
        return convertShortArrayToString(data, " ");
    }

    public static String convertShortArrayToString(short[] data, String split) {
        String retValue = null;
        StringBuilder sBuffer = new StringBuilder();
        if (data != null && data.length > 0) {
            if (StringUtils.isEmpty(split)) {
                split = " ";
            }

            for (short val : data) {
                String shortToHex = String.valueOf(val);
                if (StringUtils.isNotEmpty(shortToHex)) {
                    while (shortToHex.startsWith("0")) {
                        shortToHex = shortToHex.substring(1);
                    }
                    if (StringUtils.isNotEmpty(shortToHex)) {
                        sBuffer.append(shortToHex).append(split);
                    }
                }
            }
            retValue = sBuffer.toString();
            if (retValue.endsWith(split)) {
                retValue = retValue.substring(0, retValue.length() - 1);
            }
        }
        return retValue;
    }

    /**
     * 将byte类型数组转换成BCD码
     *
     * @param values byte数组
     * @return BCD码
     */
    public static String byteArrayToBCD(byte[] values) {
        final StringBuilder sb = new StringBuilder();
        for (byte value : values) {
            sb.append(byteToHex(value, true));
        }
        return sb.toString();
    }

    /**
     * 将byte类型数组转换成short
     *
     * @param values byte数组
     * @return short
     */
    public static short byteArrayToShort(byte[] values) {
        short retValue = 0;
        if (values != null && values.length == 2) {
            retValue = ByteBuffer.wrap(values).getShort();
        }
        return retValue;
    }

    /**
     * 将byte类型数组转换成int
     *
     * @param values byte数组
     * @return int
     */
    public static int byteArrayToInt(byte[] values) {
        int retValue = 0;
        if (values != null && values.length == 4) {
            retValue = ByteBuffer.wrap(values).getInt();
        }
        return retValue;
    }


    /**
     * 将十进制整数转换成二进制数
     *
     * @param intValue 十进制整数
     * @param len      需要的数据长度，不匹配长度则补足或截取
     * @return 二进制数
     */
    public static String intToBinary(int intValue, int len) {
        String retValue = null;
        String str = Integer.toBinaryString(intValue);
        if (str.length() < len) {
            StringBuilder sBuilder = new StringBuilder(str);
            while (sBuilder.length() < len) {
                sBuilder.insert(0, "0");
            }
            retValue = sBuilder.toString();
        } else if (str.length() > len) {
            retValue = str.substring(str.length() - len);
        } else {
            retValue = str;
        }
        return retValue;
    }

    /**
     * 将byte类型数组转换成十六进制字符串
     *
     * @param bytes byte数组
     * @return 十六进制字符串
     */
    public static String byteArrayToHex(byte[] bytes) {
        StringBuilder sBuffer = new StringBuilder();
        for (byte val : bytes) {
            sBuffer.append(byteToHex(val, true));
        }
        return sBuffer.toString();
    }

    public static byte[] hexToByteArray(String hexString) {
        byte[] retValue = null;
        if (StringUtils.isNotEmpty(hexString)) {
            for (int i = 0; i < hexString.length(); i += 2) {
                final byte val = (byte) Integer.parseInt(hexString.substring(i, i + 2), 16);
                retValue = ArrayUtils.add(retValue, val);
            }
        }
        return retValue;
    }


    public static boolean isReachableRemote(String remoteIpAddress) {
        boolean retValue = false;
        try {
            InetAddress inetAddress = InetAddress.getByName(remoteIpAddress);
            // 测试是否可以达到该地址
            retValue = inetAddress.isReachable(3000);
        } catch (Exception ignored) {
        }
        return retValue;
    }

    public static String convertSecondToHMS(long secondValue, String lang) {
        long minuteValue = 0;
        long hourValue = 0;
        if (secondValue > 60) {
            minuteValue = secondValue / 60;
            secondValue = secondValue % 60;
            if (minuteValue > 60) {
                hourValue = minuteValue / 60;
                minuteValue = minuteValue % 60;
            }
        }
        String retValue = "";
        if ("zh-cn".equals(lang)) {
            if (secondValue >= 0) {
                retValue = secondValue + "秒";
            }
            if (minuteValue > 0) {
                retValue = minuteValue + "分" + retValue;
            }
            if (hourValue > 0) {
                retValue = hourValue + "小时" + retValue;
            }
        } else {
            if (secondValue >= 0) {
                retValue = secondValue + "\"";
            }
            if (minuteValue > 0) {
                retValue = minuteValue + "'" + retValue;
            }
            if (hourValue > 0) {
                retValue = hourValue + " " + retValue;
            }
        }
        return retValue;
    }

    /**
     * 取得当前语言环境
     *
     * @return 语言环境
     */
    public static String getDefaultLanguage() {
        Locale locale = Locale.getDefault();
        String language = locale.getLanguage();
        if ("zh".equals(language)) {
            String country = "cn";
            return language + "-" + country;
        } else {
            return language;
        }
    }

    /**
     * 取得整数最高位数字
     *
     * @param number 整数
     * @return 最高位数字
     */
    public static int getHighestDigit(int number) {
        number = Math.abs(number);
        if (number < 10) {
            return number;
        }
        return getHighestDigit((number - (number % 10)) / 10);
    }


    /**
     * 根据曲线总长度和点距，计算出新的点距
     * 说明：如果总长度能被点距整除，这不需要重新计算
     *
     * @param totalValue 曲线总长度
     * @param splitValue 点距
     * @return 计算出来的点距列表
     */
    public static List<Float> getMeasureDistanceValueList(float totalValue, float splitValue) {
        final List<Float> retValue = new ArrayList<>();
        if (Float.isInfinite(totalValue) || Float.isNaN(totalValue) || totalValue <= 0) {
            return retValue;
        }
        if (Float.isInfinite(splitValue) || Float.isNaN(splitValue) || splitValue <= 0) {
            return retValue;
        }

        if (totalValue > splitValue) {
            final float modValue = totalValue % splitValue;
            final float divValue = (totalValue - modValue) / splitValue;
            if (modValue / splitValue >= 0.333f) {
                for (float i = 0; i <= divValue; i++) {
                    retValue.add(i * splitValue);
                }
                retValue.add(totalValue);
            } else {
                float diffValue = modValue / divValue;
                if (Float.isInfinite(diffValue) || Float.isNaN(diffValue)) {
                    diffValue = 0f;
                }
                final float splitNewValue = diffValue + splitValue;
                for (float i = 0; i <= divValue; i++) {
                    retValue.add(i * splitNewValue);
                }
            }
        } else {
            retValue.add(0f);
            retValue.add(totalValue);
        }
        return retValue;
    }


    public static String getDigitChsValue(int number) {
        if (number >= 0 && number <= 9) {
            switch (number) {
                case 0:
                    return "零";
                case 1:
                    return "一";
                case 2:
                    return "二";
                case 3:
                    return "三";
                case 4:
                    return "四";
                case 5:
                    return "五";
                case 6:
                    return "六";
                case 7:
                    return "七";
                case 8:
                    return "八";
                case 9:
                    return "九";
            }
        }
        return "";
    }


    public static void exeSuCmd(String cmd) {
        Process process;
        DataOutputStream dos = null;
        try {
            process = Runtime.getRuntime().exec("su");
            dos = new DataOutputStream(process.getOutputStream());
            dos.writeBytes(cmd);
            dos.flush();
        } catch (Exception ignored) {
        } finally {
            if (dos != null) {
                try {
                    dos.close();
                } catch (Exception ignored) {
                }
            }
        }
    }

    /**
     * 实现远程关机
     */
    public static void remoteShutDown(String... shutdownIps) {
        try {
            for (String shutdownIp : shutdownIps) {
                Process process0 = Runtime.getRuntime().exec(" net use \\\\" + shutdownIp + "\\ipc$\"\" /user:\"guest\"");
                int waitFor0 = process0.waitFor();//等待命令执行完
                if (waitFor0 == 0) {
                    Process process1 = Runtime.getRuntime().exec("shutdown -s -t 0 -m \\\\" + shutdownIp + " -c \"test\" -f");
                    int waitFor1 = process1.waitFor();//等待命令执行完
                    if (waitFor1 != 0) {
                        Runtime.getRuntime().exec("shutdown -s -t 0 -m \\\\" + shutdownIp + " -c \"test\" -f");
                    }
                } else {
                    process0 = Runtime.getRuntime().exec(" net use \\\\" + shutdownIp + "\\ipc$\"\" /user:\"guest\"");
                    waitFor0 = process0.waitFor();//等待命令执行完
                    if (waitFor0 == 0) {
                        Process process1 = Runtime.getRuntime().exec("shutdown -s -t 0 -m \\\\" + shutdownIp + " -c \"test\" -f");
                        int waitFor1 = process1.waitFor();//等待命令执行完
                        if (waitFor1 != 0) {
                            Runtime.getRuntime().exec("shutdown -s -t 0 -m \\\\" + shutdownIp + " -c \"test\" -f");
                        }
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }



    /**
     * 调用 google guava 的重试框架，创建重试器
     *
     * @return 重试器
     */
    public static Retryer<Boolean> getRetryer() {
        return getRetryer(500, 3);
    }

    /**
     * 调用 google guava 的重试框架，创建重试器
     *
     * @param waitTime 重试等待时间
     * @param tryTimes 重试次数
     * @return 重试器
     */
    public static Retryer<Boolean> getRetryer(int waitTime, int tryTimes) {
        // 定义重试器
        return RetryerBuilder.<Boolean>newBuilder()
                .retryIfRuntimeException() // 发生运行时异常重试
                .retryIfExceptionOfType(IOException.class) // 发生IO异常重试
                .withStopStrategy(StopStrategies.stopAfterAttempt(tryTimes)) // 允许执行tryTimes次（首次执行 + 最多重试tryTimes-1次）
                .withWaitStrategy(WaitStrategies.fixedWait(waitTime, TimeUnit.MILLISECONDS)) // 等待
                .retryIfResult(result -> result == null || Objects.equals(result, false))//结果为是空或为false重试
                .build();

    }


    public static String getStackTraceString(Throwable tr) {
        if (tr == null) {
            return "";
        }

        // This is to reduce the amount of log spew that apps do in the non-error
        // condition of the network being unavailable.
        Throwable t = tr;
        while (t != null) {
            if (t instanceof UnknownHostException) {
                return "";
            }
            t = t.getCause();
        }

        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        tr.printStackTrace(pw);
        pw.flush();
        return sw.toString();
    }

    /**
     * json数据按照key排序
     *
     */
    public static Map<String, Object> sortJsonByKey(String jsonStr) {
        JSONObject jsonObj = JSON.parseObject(jsonStr);
        return sortJsonByKey(jsonObj);
    }

    /**
     * json数据按照key排序
     */
    public static Map<String, Object> sortJsonByKey(JSONObject jsonObj) {
        if (jsonObj != null && !jsonObj.isEmpty()) {
            return jsonObj.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
        }
        return null;
    }


    public static void main(String[] args) {
        System.out.println("------------------");
        System.out.println(getCurrentDate());
        System.out.println(getCurrentTime());
    }
}
