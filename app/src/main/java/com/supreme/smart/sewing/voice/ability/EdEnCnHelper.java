package com.supreme.smart.sewing.voice.ability;

import android.annotation.SuppressLint;
import android.util.Log;

import com.iflytek.aikit.core.AiAudio;
import com.iflytek.aikit.core.AiHandle;
import com.iflytek.aikit.core.AiHelper;
import com.iflytek.aikit.core.AiListener;
import com.iflytek.aikit.core.AiRequest;
import com.iflytek.aikit.core.AiResponse;
import com.iflytek.aikit.core.AiStatus;
import com.iflytek.aikit.core.DataStatus;
import com.supreme.smart.sewing.voice.audio.AudioRecorder;
import com.supreme.smart.sewing.voice.audio.RecorderCallback;

import java.io.File;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Desc: 语音识别 - 中英文
 * <AUTHOR>
 * @Date 2023/04/07-15:29
 * Copyright 2023 iFLYTEK Inc. All Rights Reserved.
 */
public class EdEnCnHelper implements AiListener, RecorderCallback {

    private static final String TAG = "EdEnCnHelper";

    private final AbilityCallback callBack;
    private final AudioRecorder recorder;
    private RecorderCallback recordCallback;

    private final byte[] lockArray = new byte[0];
    private AiHandle aiHandle;
    private final AtomicBoolean audioBegin = new AtomicBoolean(false);

    public EdEnCnHelper(AbilityCallback callBack) {
        this.callBack = callBack;
        // 能力回调
        AiHelper.getInst().registerListener(AbilityConstant.ED_ENCN_ID, this);

        recorder = AudioRecorder.getInstance();
        recorder.init();
        recorder.setRecorderCallback(this);
    }

    public void setRecorderCallback(RecorderCallback callback) {
        this.recordCallback = callback;
    }

    /**
     * ed资源存放目录
     */
    private String workDir() {
        return "/sdcard/iflytekAikit/ed/encn";
    }

    public void switchAsr(boolean enable) {
        if (enable) {
            startEsr();
        } else {
            stopAsr();
        }
    }

    /**
     * 开始语音识别
     */
    @SuppressLint("MissingPermission")
    public void startEsr() {
        if (initParams()) return;
        recorder.startRecording();

        ThreadExt.mainThread(callBack::onAbilityBegin);
    }


    private boolean initParams() {
        //能力逆初始化， 部分能力，比如语种切换的时候 需要逆初始化
        AiHelper.getInst().engineUnInit(AbilityConstant.ED_ENCN_ID);
        int ret = -1;
        ret = AiHelper.getInst().engineInit(AbilityConstant.ED_ENCN_ID);

        if (ret != AbilityConstant.ABILITY_SUCCESS_CODE) {
            Log.w(TAG, "open ivw error code ===> " + ret);
            final int finalRet = ret;
            ThreadExt.mainThread(() ->
                    callBack.onAbilityError(finalRet, new Throwable("引擎初始化失败：" + finalRet)));
            return true;
        }


        AiRequest.Builder paramBuilder = AiRequest.builder();
        paramBuilder.param("lmLoad", true);
        paramBuilder.param("vadLoad", true);
        paramBuilder.param("puncLoad", true);
        paramBuilder.param("numLoad", true);
        paramBuilder.param("postprocOn", true);
        paramBuilder.param("lmOn", true);
        paramBuilder.param("vadOn", true);
        paramBuilder.param("vadLinkOn", false);
        paramBuilder.param("vadNeed", true);
        paramBuilder.param("vadThreshold", 0.1332);
        paramBuilder.param("vadEnergyThreshold", 9);

        aiHandle = AiHelper.getInst()
                .start(AbilityConstant.ED_ENCN_ID, paramBuilder.build(), null);

        if (aiHandle == null || aiHandle.getCode() != AbilityConstant.ABILITY_SUCCESS_CODE) {
            final int code = aiHandle != null ? aiHandle.getCode()
                    : AbilityConstant.ABILITY_CUSTOM_UNKNOWN_CODE;
            Log.w(TAG, "open esr start error code ===> " + code);

            ThreadExt.mainThread(() ->
                    callBack.onAbilityError(code, new Throwable("open esr start error")));
            return true;
        }
        return false;
    }

    /**
     * 停止语音识别
     */
    private void stopAsr() {
        recorder.stopRecording();
        endAiHandle();
    }

    /**
     * 写入音频文件
     */
    public void writeStream(InputStream stream) {
        if (initParams()) return;
        try {
            byte[] buffer = new byte[320];
            AiStatus status = AiStatus.BEGIN;
            while (stream.read(buffer) != -1) {
                writeData(buffer, status);
                status = AiStatus.CONTINUE;
            }
            //补个尾帧，表示结束
            writeData(new byte[0], AiStatus.END);
            stream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 写入音频数据
     *
     * @param audio  音频数据
     * @param status 送入的数据的状态，告诉引擎送入的首帧数据、中间数据、还是尾帧
     */
    private void writeData(byte[] audio, AiStatus status) {
        if (aiHandle == null) {
            return;
        }
        synchronized (lockArray) {
            AiRequest.Builder dataBuilder = AiRequest.builder();
            AiAudio.Holder holder = AiAudio.get("PCM").data(audio);
            holder.status(status);
            dataBuilder.payload(holder.valid());

            int ret = AiHelper.getInst().write(dataBuilder.build(), aiHandle);
            if (ret != AbilityConstant.ABILITY_SUCCESS_CODE) {
                endAiHandle();
                Log.w(TAG, "writeData is error => " + ret);
            } else {
                ret = AiHelper.getInst().read(AbilityConstant.ED_ENCN_ID, aiHandle);
                if (ret != AbilityConstant.ABILITY_SUCCESS_CODE) {
                    Log.w(TAG, "read error code => " + ret);
                    endAiHandle();
                } else {
                    Log.w(TAG, "read success code => " + ret);
                }
            }
        }
    }

    /**
     * audioRecorder 回调
     */
    @Override
    public void onStartRecord() {
        Log.i(TAG, "onStartRecord===>");
        if (recordCallback != null) {
            recordCallback.onStartRecord();
        }
        audioBegin.set(true);
    }

    @Override
    public void onPauseRecord() {
        Log.i(TAG, "onPauseRecord===>");
        if (recordCallback != null) {
            recordCallback.onPauseRecord();
        }
    }

    @Override
    public void onResumeRecord() {
        Log.i(TAG, "onResumeRecord===>");
        if (recordCallback != null) {
            recordCallback.onResumeRecord();
        }
        audioBegin.set(true);
    }

    @Override
    public void onRecordProgress(byte[] data, int sampleSize, int volume) {
        Log.i(TAG, "onRecordProgress===>" + sampleSize + "=" + volume);
        if (recordCallback != null) {
            recordCallback.onRecordProgress(data, sampleSize, volume);
        }

        AiStatus status = AiStatus.CONTINUE;
        if (audioBegin.get()) {
            status = AiStatus.BEGIN;
            audioBegin.set(false);
        }
        writeData(data, status);
    }

    @Override
    public void onStopRecord(File output) {
        Log.i(TAG, "onStopRecord===>" + (output != null ? output.getAbsolutePath() : "null"));
        if (recordCallback != null) {
            recordCallback.onStopRecord(output);
        }
    }

    /**
     * 手动结束会话
     */
    private void endAiHandle() {
        if (aiHandle == null) return;

        final int ret = AiHelper.getInst().end(aiHandle);
        ThreadExt.mainThread(() -> {
            if (ret == AbilityConstant.ABILITY_SUCCESS_CODE) {
                callBack.onAbilityEnd();
            } else {
                callBack.onAbilityError(ret, new Throwable("aiHandle end error"));
            }
        });
        aiHandle = null;
    }

    /**
     * 安全地将字节数组转换为字符串
     *
     * @param data 字节数组
     * @return 转换后的字符串
     */
    private String convertBytesToString(byte[] data) {
        if (data == null) {
            return "";
        }

        try {
            // 首先尝试使用 GBK 编码
            return new String(data, "GBK");
        } catch (UnsupportedEncodingException e) {
            Log.w(TAG, "GBK encoding not supported, falling back to UTF-8", e);
            try {
                // 如果 GBK 不支持，尝试 UTF-8
                return new String(data, StandardCharsets.UTF_8);
            } catch (Exception e2) {
                Log.w(TAG, "UTF-8 encoding failed, using default charset", e2);
                // 最后使用默认字符集
                return new String(data);
            }
        }
    }

    /**
     * 能力输出回调结果
     *
     * @param handleID     会话ID
     * @param responseData 能力执行结果
     * @param usrContext   用户自定义标识
     */
    @Override
    public void onResult(int handleID, List<AiResponse> responseData, Object usrContext) {
        if (responseData == null || responseData.isEmpty()) return;

        Log.i(TAG, "onResult:handleID:" + handleID + " : " + responseData.size() + " usrContext: " + usrContext);

        for (final AiResponse item : responseData) {
            final String tempKey = item.getKey();
            final String tempValue = convertBytesToString(item.getValue());

            if (tempKey.contains("plain") || tempKey.contains("pgs")) {
                ThreadExt.mainThread(() -> callBack.onAbilityResult(tempKey + ": \n" + tempValue));
            }

            if (tempKey.contains("vad")) {
                ThreadExt.mainThread(() -> callBack.onAbilityResult(tempKey + ": \n" + tempValue));
            }
        }

        if (responseData.get(0).getStatus() == DataStatus.END.getValue()) {
            stopAsr();
        }
    }

    /**
     * 能力输出事件回调
     *
     * @param handleID   会话ID
     * @param event      0=未知;1=开始;2=结束;3=超时;4=进度
     * @param eventData  事件消息数据
     * @param usrContext 用户自定义标识
     *                   <p>
     *                   >>>> 注意啦：这里语音识别、ivw唤醒不会执行该事件回调 <<<<
     */
    @Override
    public void onEvent(int handleID, int event, List<AiResponse> eventData, Object usrContext) {
        // 这里语音识别、ivw唤醒不会执行该事件回调
    }

    /**
     * 能力输出失败回调
     *
     * @param handleID   会话ID
     * @param err        错误码
     * @param msg        错误描述
     * @param usrContext 用户自定义标识
     */
    @Override
    public void onError(int handleID, final int err, final String msg, Object usrContext) {
        final String tips = "onError==>,ERROR::" + msg + ",err code:" + err;

        ThreadExt.mainThread(() -> callBack.onAbilityError(err, new Throwable(tips)));

        Log.e(TAG, tips);
    }

    /**
     * 所有能力在退出的时候需要手动去释放会话
     * AiHelper.getInst().end(aiHandle)
     */
    public void destroy() {
        stopAsr();
    }
}