package com.supreme.smart.sewing.serial;

import android.content.Context;
import android.os.Bundle;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.databinding.SerialBinding;
import com.supreme.smart.sewing.utils.CommonUtils;
import com.supreme.smart.sewing.utils.LocaleHelper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;

public class SerialActivity extends AppCompatActivity {
    private static final String TAG = "SerialActivity";
    private final String portPath = "/dev/ttyS2";
    private final int baudRate = 115200;

    private final CompositeDisposable compositeDisposable = new CompositeDisposable();
    private SerialPortManager serialPortManager;
    private Disposable pollingDisposable;
    private Disposable multiplePollingDisposable;
    private SerialBinding binding;
    private final AtomicInteger pollingCounter = new AtomicInteger(0); // 轮询计数器


    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase)));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);

        // 使用DataBinding初始化布局
        binding = DataBindingUtil.setContentView(this, R.layout.serial);

        // 初始化数据绑定变量
        binding.setSendData("0203083600056794\n020300010001D5F9\n0203085200026789");
        binding.setReceivedData("");
        binding.setIsConnected(false);
        binding.setIsPolling(false);

        // 初始化串口管理器
        serialPortManager = SerialPortManager.getInstance();

        // 初始化串口

        boolean isInitSuccess = serialPortManager.initSerialPort(this, portPath, baudRate);
        if (isInitSuccess) {
            binding.setIsConnected(true);
            Toast.makeText(this, getString(R.string.serial_init_success), Toast.LENGTH_SHORT).show();

//            // 订阅串口数据
//            Disposable dataDisposable = serialPortManager.observeSerialData()
//                    .subscribe(bytes -> {
//                        // 将接收到的数据转换为十六进制字符串并显示
//                        String hexString = bytesToHexString(bytes);
//                        String currentData = binding.getReceivedData() != null ? binding.getReceivedData() : "";
//                        String timestamp = getCurrentTimestamp();
//                        String newData;
//                        if (currentData.length() > 1000) {
//                            // 当数据过长时，只保留最新的数据
//                            newData = "响应:" + timestamp + " " + hexString + "\n";
//                            LogUtils.eTag(TAG, "数据过长，已清除旧数据");
//                        } else {
//                            newData = currentData + "响应:" + timestamp + " " + hexString + "\n";
//                        }
//                        binding.setReceivedData(newData);
//
//                        // 也可以转换为字符串显示
//                        // String strData = new String(bytes);
//                        // binding.setReceivedData(currentData + "字符串: " + strData + "\n");
//                    }, throwable -> LogUtils.eTag(TAG, getString(R.string.receive_data_error) + throwable.getMessage()));
//
//            compositeDisposable.add(dataDisposable);
        } else {
            Toast.makeText(this, getString(R.string.serial_init_failed), Toast.LENGTH_SHORT).show();
        }

        // 清除数据按钮点击事件
        binding.btnClearData.setOnClickListener(v -> {
            clearReceivedData();
        });

        // 发送多条命令按钮点击事件
        binding.btnSendMultiple.setOnClickListener(v -> {
            sendMultipleCommands();
        });

        // 开始多条命令轮询按钮点击事件
        binding.btnStartMultiplePolling.setOnClickListener(v -> {
            startMultipleCommandsPolling();
        });

        // 停止多条命令轮询按钮点击事件
        binding.btnStopMultiplePolling.setOnClickListener(v -> {
            stopMultipleCommandsPolling();
        });
    }

    /**
     * 清除接收数据显示
     */
    private void clearReceivedData() {
        binding.setReceivedData("");
        Toast.makeText(this, "已清除接收数据", Toast.LENGTH_SHORT).show();
    }

    /**
     * 发送多条命令
     */
    private void sendMultipleCommands() {
        String commandsText = binding.getSendData().trim();
        if (commandsText.isEmpty()) {
            Toast.makeText(this, "请输入要发送的命令", Toast.LENGTH_SHORT).show();
            return;
        }

        // 按行分割命令
        String[] commandLines = commandsText.split("\\n");
        List<String> hexCommands = new ArrayList<>();

        for (String line : commandLines) {
            String command = line.trim();
            if (!command.isEmpty()) {
                // 验证是否为有效的十六进制字符串
                if (command.matches("^[0-9A-Fa-f]+$")) {
                    hexCommands.add(command);
                } else {
                    Toast.makeText(this, "命令格式错误: " + command + "\n请输入有效的十六进制字符串", Toast.LENGTH_LONG).show();
                    return;
                }
            }
        }

        if (hexCommands.isEmpty()) {
            Toast.makeText(this, "没有有效的命令", Toast.LENGTH_SHORT).show();
            return;
        }

        List<byte[]> commands = hexCommands.stream()
                .map(CommonUtils::hexToByteArray)
                .collect(Collectors.toList());

        // 发送多条命令
        Disposable sendDisposable = serialPortManager.sendCommands(commands, 1000).subscribe(responses -> {
                    // 显示发送和响应数据（一一对应）
                    StringBuilder result = new StringBuilder();
                    String timestamp = getCurrentTimestamp();

                    String currentData = binding.getReceivedData() != null ? binding.getReceivedData() : "";
                    result.append("=== 批量发送命令 ").append(timestamp).append(" ===\n");

                    // 发送命令和响应一一对应显示
                    int maxCount = Math.max(commands.size(), responses.size());
                    for (int i = 0; i < maxCount; i++) {
                        // 显示发送的命令
                        if (i < commands.size()) {
                            result.append("命令").append(i + 1).append(":").append(timestamp).append(" ").append(CommonUtils.convertByteArrayToString(commands.get(i))).append("\n");
                        }

                        // 显示对应的响应
                        if (i < responses.size()) {
                            result.append("响应").append(i + 1).append(":").append(timestamp).append(" ").append(CommonUtils.convertByteArrayToString(responses.get(i))).append("\n");
                        } else {
                            result.append("响应").append(i + 1).append(":").append(timestamp).append(" 无响应\n");
                        }

                        // 在每对命令-响应之间添加分隔
                        if (i < maxCount - 1) {
                            result.append("\n");
                        }
                    }

                    result.append("\n");

                    binding.setReceivedData(currentData + result);

                }, throwable -> {
                    LogUtils.eTag(TAG, "发送多条命令失败: " + throwable.getMessage());
                    Toast.makeText(SerialActivity.this, "发送多条命令失败: " + throwable.getMessage(), Toast.LENGTH_LONG).show();
                }
        );

        compositeDisposable.add(sendDisposable);
    }

    /**
     * 开始多条命令轮询
     */
    private void startMultipleCommandsPolling() {
        String commandsText = binding.getSendData().trim();
        if (commandsText.isEmpty()) {
            Toast.makeText(this, "请输入要轮询的命令", Toast.LENGTH_SHORT).show();
            return;
        }

        // 按行分割命令
        String[] commandLines = commandsText.split("\\n");
        List<String> hexCommands = new ArrayList<>();

        for (String line : commandLines) {
            String command = line.trim();
            if (!command.isEmpty()) {
                // 验证是否为有效的十六进制字符串
                if (command.matches("^[0-9A-Fa-f]+$")) {
                    hexCommands.add(command);
                } else {
                    Toast.makeText(this, "命令格式错误: " + command + "\n请输入有效的十六进制字符串", Toast.LENGTH_LONG).show();
                    return;
                }
            }
        }

        if (hexCommands.isEmpty()) {
            Toast.makeText(this, "没有有效的命令", Toast.LENGTH_SHORT).show();
            return;
        }

        List<byte[]> commands = hexCommands.stream()
                .map(CommonUtils::hexToByteArray)
                .collect(Collectors.toList());

        // 重置轮询计数器
        pollingCounter.set(0);

        // 使用SerialPortManager的轮询方法并订阅响应数据
        multiplePollingDisposable = serialPortManager.startPolling(commands, 0, 500, 50, 500)
                .subscribe(responses -> {
                    // 增加轮询计数
                    pollingCounter.incrementAndGet();

                    // 显示轮询发送和响应数据（一一对应）
                    StringBuilder result = new StringBuilder();
                    String timestamp = getCurrentTimestamp();

                    result.append("=== 轮询第").append(pollingCounter).append("次 ").append(timestamp).append(" ===").append("\n");

                    // 发送命令和响应一一对应显示
                    int maxCount = Math.max(commands.size(), responses.size());
                    for (int i = 0; i < maxCount; i++) {
                        // 显示发送的命令
                        if (i < commands.size()) {
                            result.append("命令").append(i + 1).append(":").append(timestamp).append(" ").append(CommonUtils.convertByteArrayToString(commands.get(i))).append("\n");
                        }

                        // 显示对应的响应
                        if (i < responses.size()) {
                            result.append("响应").append(i + 1).append(":").append(timestamp).append(" ").append(CommonUtils.convertByteArrayToString(responses.get(i))).append("\n");
                        } else {
                            result.append("响应").append(i + 1).append(":").append(timestamp).append(" 无响应\n");
                        }

                        // 在每对命令-响应之间添加分隔
                        if (i < maxCount - 1) {
                            result.append("\n");
                        }
                    }
                    result.append("\n");

                    // 每20次轮询后清除显示数据
                    String updatedData;
                    if (pollingCounter.get() % 20 == 0) {
                        updatedData = "=== 已清除前" + pollingCounter.get() + "次轮询数据 ===\n\n" + result;
                        LogUtils.dTag(TAG, "已清除轮询显示数据，当前轮询次数: " + pollingCounter.get());
                    } else {
                        updatedData = (binding.getReceivedData() != null ? binding.getReceivedData() : "") + result;
                    }
                    binding.setReceivedData(updatedData);
                }, throwable -> {
                    LogUtils.eTag(TAG, "轮询异常: " + throwable.getMessage());
                    String errorData = binding.getReceivedData() != null ? binding.getReceivedData() : "";
                    binding.setReceivedData(errorData + "轮询异常: " + throwable.getMessage() + "\n\n");
                    Toast.makeText(SerialActivity.this, "轮询异常: " + throwable.getMessage(), Toast.LENGTH_LONG).show();
                });

        compositeDisposable.add(multiplePollingDisposable);
        binding.setIsPolling(true);
    }

    /**
     * 停止多条命令轮询
     */
    private void stopMultipleCommandsPolling() {
        if (multiplePollingDisposable != null) {
            serialPortManager.stopPolling(multiplePollingDisposable);
            multiplePollingDisposable = null;
            binding.setIsPolling(false);
            pollingCounter.set(0);
            ; // 重置轮询计数器
        }
    }

    /**
     * 获取当前时间戳
     */
    private String getCurrentTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss.SSS", Locale.US);
        return sdf.format(new Date());
    }

    @Override
    protected void onDestroy() {
        // 停止轮询
        if (pollingDisposable != null) {
            serialPortManager.stopPolling(pollingDisposable);
            pollingDisposable = null;
        }

        // 停止多条命令轮询
        if (multiplePollingDisposable != null) {
            serialPortManager.stopPolling(multiplePollingDisposable);
            multiplePollingDisposable = null;
        }

        // 释放所有订阅
        if (compositeDisposable != null
                && !compositeDisposable.isDisposed()) {
            compositeDisposable.dispose();
        }

        // 关闭串口
        if (serialPortManager != null) {
            serialPortManager.closeSerialPort();
        }

        super.onDestroy();
    }
}