package com.supreme.smart.sewing.push;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.blankj.utilcode.util.LogUtils;

/**
 * 推送管理器 - 管理MQTT推送服务
 */
public class PushManager {
    
    private static final String TAG = "PushManager";
    private static volatile PushManager instance;
    private final Context context;
    
    private PushManager(Context context) {
        this.context = context.getApplicationContext();
    }
    
    public static synchronized PushManager getInstance(Context context) {
        if (instance == null) {
            synchronized (PushManager.class) {
                if (instance == null) {
                    instance = new PushManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动推送服务
     */
    @SuppressLint("ObsoleteSdkInt")
    public void startPushService() {
        try {
            Intent serviceIntent = new Intent(context, MqttPushService.class);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            
            LogUtils.dTag(TAG, "MQTT推送服务已启动");
            
        } catch (Exception e) {
            LogUtils.eTag(TAG, "启动MQTT推送服务失败", e);
        }
    }
    
    /**
     * 停止推送服务
     */
    public void stopPushService() {
        try {
            Intent serviceIntent = new Intent(context, MqttPushService.class);
            context.stopService(serviceIntent);
            
            LogUtils.dTag(TAG, "MQTT推送服务已停止");
            
        } catch (Exception e) {
            LogUtils.eTag(TAG, "停止MQTT推送服务失败", e);
        }
    }
} 