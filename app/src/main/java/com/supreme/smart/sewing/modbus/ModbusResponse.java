package com.supreme.smart.sewing.modbus;

/**
 * Modbus响应数据类
 * 用于封装Modbus通信的响应结果
 */
public class ModbusResponse {
    public final boolean isSuccess;
    public final byte[] data;
    public final String errorMessage;
    public final byte exceptionCode;
    
    /**
     * 构造函数
     * 
     * @param isSuccess 是否成功
     * @param data 响应数据
     * @param errorMessage 错误信息
     * @param exceptionCode 异常码
     */
    public ModbusResponse(boolean isSuccess, byte[] data, String errorMessage, byte exceptionCode) {
        this.isSuccess = isSuccess;
        this.data = data;
        this.errorMessage = errorMessage;
        this.exceptionCode = exceptionCode;
    }
    
    /**
     * 创建成功响应
     * 
     * @param data 响应数据
     * @return 成功的ModbusResponse对象
     */
    public static ModbusResponse success(byte[] data) {
        return new ModbusResponse(true, data, null, (byte) 0);
    }
    
    /**
     * 创建错误响应
     * 
     * @param errorMessage 错误信息
     * @return 错误的ModbusResponse对象
     */
    public static ModbusResponse error(String errorMessage) {
        return new ModbusResponse(false, null, errorMessage, (byte) 0);
    }
    
    /**
     * 创建异常响应
     * 
     * @param exceptionCode 异常码
     * @param errorMessage 错误信息
     * @return 异常的ModbusResponse对象
     */
    public static ModbusResponse exception(byte exceptionCode, String errorMessage) {
        return new ModbusResponse(false, null, errorMessage, exceptionCode);
    }
}