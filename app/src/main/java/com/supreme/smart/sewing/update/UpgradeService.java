package com.supreme.smart.sewing.update;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.utils.HttpUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

/**
 * 更新下载服务
 * Update Download Service
 */
public class UpgradeService extends Service {
    
    private static final String TAG = "UpdateService";
    private static final String CHANNEL_ID = "update_download_channel";
    private static final int NOTIFICATION_ID = 1001;
    
    private NotificationManager notificationManager;
    private NotificationCompat.Builder notificationBuilder;
    private ExecutorService executorService;
    private Handler mainHandler;
    private UpdateServiceBinder binder = new UpdateServiceBinder();
    
    // 下载状态
    public enum DownloadStatus {
        IDLE, DOWNLOADING, COMPLETED, FAILED, CANCELLED
    }
    
    private DownloadStatus downloadStatus = DownloadStatus.IDLE;
    private UpgradeInfo currentUpgradeInfo;
    private File downloadFile;
    private Call downloadCall;
    
    // 下载进度回调接口
    public interface DownloadCallback {
        void onProgress(int progress, long downloadedBytes, long totalBytes, String speed);
        void onCompleted(File file);
        void onFailed(String error);
        void onCancelled();
    }
    
    private DownloadCallback downloadCallback;
    
    public class UpdateServiceBinder extends Binder {
        public UpgradeService getService() {
            return UpgradeService.this;
        }
    }
    
    @Override
    public void onCreate() {
        super.onCreate();

        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        executorService = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
        
        createNotificationChannel();
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_STICKY;
    }
    
    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "应用更新下载",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("显示应用更新下载进度");
            channel.setSound(null, null);
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    /**
     * 开始下载
     * @param upgradeInfo 更新信息
     * @param callback 下载回调
     */
    public void startDownload(UpgradeInfo upgradeInfo, DownloadCallback callback) {
        if (downloadStatus == DownloadStatus.DOWNLOADING) {
            LogUtils.wTag(TAG, "已有下载任务在进行中");
            return;
        }
        
        this.currentUpgradeInfo = upgradeInfo;
        this.downloadCallback = callback;
        this.downloadStatus = DownloadStatus.DOWNLOADING;
        
        // 创建下载文件
        File downloadDir = new File(getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "update");
        if (!downloadDir.exists()) {
            downloadDir.mkdirs();
        }
        
        String fileName = "app_update_" + upgradeInfo.getVersionName() + ".apk";
        downloadFile = new File(downloadDir, fileName);
        
        // 如果文件已存在，删除旧文件
        if (downloadFile.exists()) {
            downloadFile.delete();
        }
        
        // 显示下载通知
        showDownloadNotification();
        
        // 开始下载
        executorService.execute(() -> downloadApk(upgradeInfo.getDownloadUrl()));
    }
    
    /**
     * 下载APK文件
     * @param downloadUrl 下载URL
     */
    private void downloadApk(String downloadUrl) {
        LogUtils.dTag(TAG, "开始下载APK: " + downloadUrl);
        
        downloadCall = HttpUtils.getInstance().downloadFile(downloadUrl, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (call.isCanceled()) {
                    handleDownloadCancelled();
                } else {
                    handleDownloadFailed("下载失败: " + e.getMessage());
                }
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    handleDownloadFailed("服务器响应错误: " + response.code());
                    return;
                }
                
                try {
                    saveFile(response);
                } catch (Exception e) {
                    handleDownloadFailed("保存文件失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 保存下载的文件
     * @param response HTTP响应
     */
    private void saveFile(Response response) throws IOException {
        InputStream inputStream = null;
        FileOutputStream outputStream = null;
        
        try {
            inputStream = response.body().byteStream();
            outputStream = new FileOutputStream(downloadFile);
            
            long totalBytes = response.body().contentLength();
            long downloadedBytes = 0;
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            long startTime = System.currentTimeMillis();
            long lastUpdateTime = startTime;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                if (downloadStatus == DownloadStatus.CANCELLED) {
                    return;
                }
                
                outputStream.write(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;
                
                // 更新进度（每500ms更新一次）
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastUpdateTime >= 500) {
                    updateProgress(downloadedBytes, totalBytes, currentTime - startTime);
                    lastUpdateTime = currentTime;
                }
            }
            
            // 下载完成
            if (downloadStatus == DownloadStatus.DOWNLOADING) {
                handleDownloadCompleted();
            }
            
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
            if (outputStream != null) {
                outputStream.close();
            }
        }
    }
    
    /**
     * 更新下载进度
     */
    private void updateProgress(long downloadedBytes, long totalBytes, long elapsedTime) {
        int progress = (int) ((downloadedBytes * 100) / totalBytes);
        String speed = calculateSpeed(downloadedBytes, elapsedTime);
        
        mainHandler.post(() -> {
            if (downloadCallback != null) {
                downloadCallback.onProgress(progress, downloadedBytes, totalBytes, speed);
            }
            updateNotificationProgress(progress);
        });
    }
    
    /**
     * 计算下载速度
     */
    private String calculateSpeed(long downloadedBytes, long elapsedTime) {
        if (elapsedTime == 0) return "0KB/s";
        
        long speed = downloadedBytes * 1000 / elapsedTime; // bytes per second
        
        if (speed < 1024) {
            return speed + "B/s";
        } else if (speed < 1024 * 1024) {
            return String.format("%.1fKB/s", speed / 1024.0);
        } else {
            return String.format("%.1fMB/s", speed / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 处理下载完成
     */
    private void handleDownloadCompleted() {
        downloadStatus = DownloadStatus.COMPLETED;
        
        // 验证文件MD5（如果提供了MD5值）
        if (currentUpgradeInfo.getMd5() != null && !currentUpgradeInfo.getMd5().isEmpty()) {
            if (!verifyFileMD5()) {
                handleDownloadFailed("文件校验失败");
                return;
            }
        }
        
        mainHandler.post(() -> {
            if (downloadCallback != null) {
                downloadCallback.onCompleted(downloadFile);
            }
            showDownloadCompletedNotification();
        });
    }
    
    /**
     * 处理下载失败
     */
    private void handleDownloadFailed(String error) {
        downloadStatus = DownloadStatus.FAILED;
        LogUtils.eTag(TAG, "下载失败: " + error);
        
        mainHandler.post(() -> {
            if (downloadCallback != null) {
                downloadCallback.onFailed(error);
            }
            showDownloadFailedNotification(error);
        });
    }
    
    /**
     * 处理下载取消
     */
    private void handleDownloadCancelled() {
        downloadStatus = DownloadStatus.CANCELLED;
        
        mainHandler.post(() -> {
            if (downloadCallback != null) {
                downloadCallback.onCancelled();
            }
            hideNotification();
        });
    }
    
    /**
     * 验证文件MD5
     */
    private boolean verifyFileMD5() {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 这里简化处理，实际应该读取文件计算MD5
            return true;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "MD5验证失败", e);
            return false;
        }
    }
    
    /**
     * 取消下载
     */
    public void cancelDownload() {
        if (downloadStatus == DownloadStatus.DOWNLOADING) {
            downloadStatus = DownloadStatus.CANCELLED;
            if (downloadCall != null) {
                downloadCall.cancel();
            }
            if (downloadFile != null && downloadFile.exists()) {
                downloadFile.delete();
            }
        }
    }
    
    /**
     * 获取下载状态
     */
    public DownloadStatus getDownloadStatus() {
        return downloadStatus;
    }
    
    /**
     * 显示下载通知
     */
    private void showDownloadNotification() {
        Intent intent = new Intent(this, UpgradeActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        notificationBuilder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle("正在下载更新")
                .setContentText("下载进度: 0%")
                .setProgress(100, 0, false)
                .setOngoing(true)
                .setContentIntent(pendingIntent);
        
        startForeground(NOTIFICATION_ID, notificationBuilder.build());
    }
    
    /**
     * 更新通知进度
     */
    private void updateNotificationProgress(int progress) {
        if (notificationBuilder != null) {
            notificationBuilder.setContentText("下载进度: " + progress + "%")
                    .setProgress(100, progress, false);
            notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build());
        }
    }
    
    /**
     * 显示下载完成通知
     */
    private void showDownloadCompletedNotification() {
        if (notificationBuilder != null) {
            notificationBuilder.setContentText("下载完成，点击安装")
                    .setProgress(0, 0, false)
                    .setOngoing(false)
                    .setAutoCancel(true);
            notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build());
        }
        stopForeground(false);
    }
    
    /**
     * 显示下载失败通知
     */
    private void showDownloadFailedNotification(String error) {
        if (notificationBuilder != null) {
            notificationBuilder.setContentText("下载失败: " + error)
                    .setProgress(0, 0, false)
                    .setOngoing(false)
                    .setAutoCancel(true);
            notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build());
        }
        stopForeground(false);
    }
    
    /**
     * 隐藏通知
     */
    private void hideNotification() {
        notificationManager.cancel(NOTIFICATION_ID);
        stopForeground(true);
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        LogUtils.dTag(TAG, "UpdateService onDestroy");
        
        cancelDownload();
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        
        hideNotification();
    }
}