package com.supreme.smart.sewing.modbus;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.business.MachineDataCollectionHelper;
import com.supreme.smart.sewing.databinding.ModbusBinding;
import com.supreme.smart.sewing.utils.CommonUtils;
import com.supreme.smart.sewing.utils.LocaleHelper;

import java.util.concurrent.TimeoutException;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

public class ModbusActivity extends AppCompatActivity {
    private static final String TAG = "ModbusActivity";
    private final byte slaveId = 2;               // 从站地址
    private final String portPath = "/dev/ttyS2"; // 串口路径
    private final int baudRate = 115200;          // 波特率
    private final int readTimeout = 500;          // 读取超时时间(ms)
    private final int retryCount = 1;             // 重试次数
    private final long retryDelay = 500;         // 重试延迟(ms)
    private final long readInterval = 500;       // 读取间隔(ms)


    private final CompositeDisposable compositeDisposable = new CompositeDisposable();
    private Disposable periodicReadingDisposable;
    private ModbusBinding binding;


    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase)));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 使用DataBinding初始化布局
        binding = DataBindingUtil.setContentView(this, R.layout.modbus);

        // 初始化数据绑定变量
        binding.setSendData(null);
        binding.setReceivedData("");
        binding.setIsConnected(false);
        binding.setIsPolling(false);

        // 初始化Modbus连接
        initializeModbusConnection();

        // 发送数据按钮点击事件
        binding.btnSend.setOnClickListener(v -> {
            sendSingleCommand();
        });

        // 开始轮询按钮点击事件
        binding.btnStartPolling.setOnClickListener(v -> {
            if (!binding.getIsPolling()) {
                startPolling();
            }
        });

        // 停止轮询按钮点击事件
        binding.btnStopPolling.setOnClickListener(v -> {
            if (binding.getIsPolling()) {
                stopPolling();
            }
        });

        // 清除数据按钮点击事件
        binding.btnClearData.setOnClickListener(v -> {
            clearReceivedData();
        });
    }


    /**
     * 初始化Modbus连接
     */
    private void initializeModbusConnection() {
        boolean initSuccess = ModbusExecutor.INSTANCE.initModbus(this);
        if (initSuccess) {
            binding.setIsConnected(true);
            ModbusExecutor.INSTANCE.setConfig(
                    slaveId, portPath, baudRate, readTimeout, retryCount,
                    retryDelay, readInterval);
            ModbusExecutor.INSTANCE.setAutoReconnect(true);
            Toast.makeText(this, "Modbus连接初始化成功", Toast.LENGTH_SHORT).show();
            LogUtils.iTag(TAG, "Modbus连接初始化成功");
        } else {
            binding.setIsConnected(false);
            Toast.makeText(this, "Modbus连接初始化失败", Toast.LENGTH_SHORT).show();
            LogUtils.eTag(TAG, "Modbus连接初始化失败");
        }
    }

    /**
     * 开始轮询
     */
    @SuppressLint("CheckResult")
    private void startPolling() {
        if (!binding.getIsConnected()) {
            Toast.makeText(this, "请先初始化Modbus连接", Toast.LENGTH_SHORT).show();
            return;
        }

        if (periodicReadingDisposable != null && !periodicReadingDisposable.isDisposed()) {
            LogUtils.wTag(TAG, "轮询已在进行中");
            return;
        }

        periodicReadingDisposable = ModbusExecutor.INSTANCE.startPeriodicReading(
                // 成功接收数据的回调
                qixingDataResult -> {
                    // 在后台线程更新数据库
                    Disposable dbDisposable = Single.fromCallable(() -> {
                                updateDatabase(qixingDataResult);
                                return true;
                            })
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(
                                    success -> {
                                        // 数据库更新成功后，在UI线程更新界面
                                        updateUI(qixingDataResult);
                                    },
                                    error -> {
                                        LogUtils.eTag(TAG, "数据库更新失败: " + Log.getStackTraceString(error));
                                    }
                            );
                    compositeDisposable.add(dbDisposable);

                }, throwable -> { // 错误处理回调 - 改进错误处理
                    runOnUiThread(() -> {
                        // 检查是否是超时异常
                        if (throwable instanceof TimeoutException) {
                            LogUtils.wTag(TAG, "读取数据超时，这是正常现象: " + throwable.getMessage());
                            // 超时不显示错误提示，因为这是正常的
                            return;
                        }

                        // 其他异常才显示错误信息
                        LogUtils.eTag(TAG, "定时读取数据失败: " + Log.getStackTraceString(throwable));
                        Toast.makeText(this, "读取数据失败: " + throwable.getMessage(),
                                Toast.LENGTH_SHORT).show();
                    });
                }, true // 轮询模式
        );

        // 将Disposable添加到CompositeDisposable中管理
        compositeDisposable.add(periodicReadingDisposable);
        binding.setIsPolling(true);
        Toast.makeText(this, "开始轮询数据", Toast.LENGTH_SHORT).show();
        LogUtils.iTag(TAG, "开始轮询数据");
    }

    /**
     * 停止轮询
     */
    private void stopPolling() {
        if (periodicReadingDisposable != null && !periodicReadingDisposable.isDisposed()) {
            compositeDisposable.remove(periodicReadingDisposable);
            periodicReadingDisposable.dispose();
            periodicReadingDisposable = null;
        }

        binding.setIsPolling(false);
        Toast.makeText(this, "停止轮询数据", Toast.LENGTH_SHORT).show();
        LogUtils.iTag(TAG, "停止轮询数据");
    }

    /**
     * 执行单次琦星数据读取
     */
    @SuppressLint("CheckResult")
    private void sendSingleCommand() {
        if (!binding.getIsConnected()) {
            Toast.makeText(this, "请先初始化Modbus连接", Toast.LENGTH_SHORT).show();
            return;
        }

        Disposable disposable = ModbusExecutor.INSTANCE.sendSingleCommand(
                qixingDataResult -> {
                    // 在后台线程执行数据库操作
                    // 处理成功结果
                    Disposable dbDisposable = Single.fromCallable(() -> {
                                updateDatabase(qixingDataResult);
                                return qixingDataResult;
                            })
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(
                                    success -> {
                                        // 数据库更新成功后，在UI线程更新界面
                                        updateUI(qixingDataResult);
                                    },
                                    throwable -> {
                                        String errorMsg = "数据库更新异常: " + throwable.getMessage();
                                        Toast.makeText(this, errorMsg, Toast.LENGTH_SHORT).show();
                                    }
                            );
                    compositeDisposable.add(dbDisposable);
                }, error -> { //读取异常
                    runOnUiThread(() -> {
                        String errorMsg = "读取异常: " + error.getMessage();
                        Toast.makeText(this, errorMsg, Toast.LENGTH_SHORT).show();
                        binding.setReceivedData(binding.getReceivedData() + "\n" + errorMsg);
                    });
                }
        );

        compositeDisposable.add(disposable);
    }

    private void updateDatabase(QixingDataResult result) {
        // 针距数据
        if (result.getNeedleGapData() != null) {
            // TODO:
        }

        // 计数器数据
        if (result.getCounterData() != null) {
            MachineDataCollectionHelper instance = MachineDataCollectionHelper.INSTANCE;
            String machineId = instance.getCurrentMachineId();
            Long pieceCountValue = result.getPieceCountValue();
            Long cutCountValue = result.getCutCountValue();
            Long raiseCountValue = result.getRaiseCountValue();
            Long needleCountValue = result.getNeedleCountValue();
            instance.updatePieceCounter(machineId, pieceCountValue);
            instance.updateNeedleCounter(machineId, needleCountValue);
            instance.updateCutCounter(machineId, cutCountValue);
            instance.updateRaiseCounter(machineId, raiseCountValue);
        }

        // 显示状态数据
        if (result.getStatusData() != null) {
            // TODO:
        }

        // 显示速度数据
        if (result.getSpeedData() != null) {
            // TODO:
        }
    }

    private void updateUI(QixingDataResult result) {
        StringBuilder dataBuilder = new StringBuilder();

        // 显示针距数据
        if (result.getNeedleGapData() != null) {
            dataBuilder.append("针距数据: ")
                    .append(CommonUtils.convertByteArrayToString(result.getNeedleGapData()))
                    .append("\n\t针距: ").append(result.getNeedleGapValue())
                    .append("\n");
        } else if (result.getNeedleGapError() != null) {
            dataBuilder.append("针距错误: ")
                    .append(result.getNeedleGapError())
                    .append("\n");
        }

        // 显示计数器数据
        if (result.getCounterData() != null) {
            dataBuilder.append("计数器数据: ")
                    .append(CommonUtils.convertByteArrayToString(result.getCounterData()))
                    .append("\n\t针数: ").append(result.getNeedleCountValue())
                    .append("\n\t计件数: ").append(result.getPieceCountValue())
                    .append("\n\t剪线次数: ").append(result.getCutCountValue())
                    .append("\n\t抬压脚次数: ").append(result.getRaiseCountValue())
                    .append("\n");
        } else if (result.getCounterError() != null) {
            dataBuilder.append("计数器错误: ").append(result.getCounterError()).append("\n");
        }

        // 显示状态数据
        if (result.getStatusData() != null) {
            dataBuilder.append("状态数据: ")
                    .append(CommonUtils.convertByteArrayToString(result.getStatusData()))
                    .append("\n\t设备状态: ").append(result.getDeviceStatusValue())
                    .append("\n\t设备故障码: ").append(result.getErrorCodeValue())
                    .append("\n");
        } else if (result.getStatusError() != null) {
            dataBuilder.append("状态错误: ").append(result.getStatusError()).append("\n");
        }

        // 显示速度数据
        if (result.getSpeedData() != null) {
            dataBuilder.append("速度数据: ")
                    .append(CommonUtils.convertByteArrayToString(result.getSpeedData()))
                    .append("\n\t最高转速: ").append(result.getMaxSpeedValue())
                    .append("\n");
        } else if (result.getSpeedError() != null) {
            dataBuilder.append("速度错误: ").append(result.getSpeedError()).append("\n");
        }

        //  String currentData = binding.getReceivedData() != null ? binding.getReceivedData() : "";
        //  String newData = currentData + getString(R.string.received_prefix) + dataBuilder + "\n";
        String newData = getString(R.string.received_prefix) + dataBuilder + "\n";
        // 更新UI显示
        binding.setReceivedData(newData);
    }

    /**
     * 清除接收数据显示
     */
    private void clearReceivedData() {
        binding.setReceivedData("");
        Toast.makeText(this, "已清除接收数据", Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onDestroy() {
        // 停止轮询
        stopPolling();

        // 释放所有订阅
        if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
            compositeDisposable.dispose();
        }

        // 清理Modbus资源
        ModbusExecutor.INSTANCE.cleanup();

        LogUtils.iTag(TAG, "Activity销毁，资源已清理");
        super.onDestroy();
    }
}