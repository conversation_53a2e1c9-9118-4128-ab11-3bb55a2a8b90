package com.supreme.smart.sewing.serial;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.utils.CommonUtils;
import com.yujing.yserialport.ThreadMode;
import com.yujing.yserialport.YSerialPort;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

public class SerialPortManager {
    private static final String TAG = "SerialPortManager";

    // 单例实例
    private static volatile SerialPortManager instance;

    private final CompositeDisposable compositeDisposable = new CompositeDisposable();
    private YSerialPort ySerialPort;
    private ObservableEmitter<byte[]> dataEmitter;

    /**
     * 私有构造函数，防止外部实例化
     */
    private SerialPortManager() {
        // 私有构造函数
    }

    /**
     * 获取SerialPortManager的单例实例
     *
     * @return SerialPortManager实例
     */
    public static SerialPortManager getInstance() {
        if (instance == null) {
            synchronized (SerialPortManager.class) {
                if (instance == null) {
                    instance = new SerialPortManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化串口
     *
     * @param context  上下文
     * @param portPath 串口路径，例如："/dev/ttyS4"
     * @param baudRate 波特率，例如：9600
     * @return 是否成功初始化
     */
    public boolean initSerialPort(Context context, String portPath, int baudRate) {
        try {
            // 创建YSerialPort实例
            ySerialPort = YSerialPort.getInstance(context, portPath, String.valueOf(baudRate));

            // 设置回调线程为主线程
            ySerialPort.setThreadMode(ThreadMode.MAIN);

            // 设置自动组包，每次组包时长为200毫秒
            ySerialPort.setToAuto(100);

            // 添加数据监听器
            ySerialPort.addDataListener((hexString, bytes) -> {
                // 当收到数据时，通过RxJava发射数据
                if (dataEmitter != null && !dataEmitter.isDisposed()) {
                    dataEmitter.onNext(bytes);
                }
            });

            // 启动串口
            ySerialPort.start();

            if (ySerialPort.getSerialPort() == null) {
                LogUtils.eTag(TAG, "初始化串口失败。");
                return false;
            }
            LogUtils.dTag(TAG, "初始化串口成功。");
            return true;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "初始化串口失败: " + Log.getStackTraceString(e));
            return false;
        }
    }

    /**
     * 获取串口数据流
     *
     * @return 返回一个Observable，用于订阅串口数据
     */
    public @NonNull Observable<byte[]> observeSerialData() {
        return Observable.create((@NonNull ObservableEmitter<byte[]> emitter) -> {
                    dataEmitter = emitter;
                    // 当Observable被dispose时，清理资源
                    emitter.setDisposable(new Disposable() {
                        private boolean disposed = false;

                        @Override
                        public void dispose() {
                            disposed = true;
                            dataEmitter = null;
                        }

                        @Override
                        public boolean isDisposed() {
                            return disposed;
                        }
                    });
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }


    public Observable<Boolean> sendData(byte[] data) {
        return Observable.fromCallable(() -> {
                    try {
                        if (ySerialPort.getSerialPort() == null) {
                            LogUtils.eTag(TAG, "串口未初始化，无法发送数据");
                            return false;
                        }

                        if (data == null || data.length == 0) {
                            LogUtils.wTag(TAG, "发送数据为空，跳过发送");
                            return false;
                        }

                        ySerialPort.send(data);
                        return true;
                    } catch (Exception e) {
                        LogUtils.eTag(TAG, "发送数据失败: " + e.getMessage());
                        return false;
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }


    /**
     * 发送多个命令并等待返回数据
     *
     * @param commands 要发送的命令列表
     * @param timeout  超时时间（毫秒）
     * @return 返回接收到的所有数据的Observable
     */
    @SuppressLint("CheckResult")
    public Observable<List<byte[]>> sendCommands(List<byte[]> commands, int timeout) {
        if (commands == null || commands.isEmpty()) {
            return Observable.error(new IllegalArgumentException("命令列表不能为空"));
        }

        return Observable.create(new ObservableOnSubscribe<List<byte[]>>() {
                    Disposable disposable = null;

                    @Override
                    public void subscribe(@NonNull ObservableEmitter<List<byte[]>> emitter) throws Throwable {
                        List<byte[]> responses = new ArrayList<>();
                        AtomicInteger currentIndex = new AtomicInteger(0);
                        disposable = observeSerialData()
                                .timeout(timeout, TimeUnit.MILLISECONDS).subscribe(response -> {
                                            responses.add(response);
                                            int index = currentIndex.incrementAndGet();

                                            if (index < commands.size()) {
                                                Disposable ignored = sendData(commands.get(index)).subscribe(success -> {
                                                    if (!success) {
                                                        emitter.onError(new IOException("发送命令失败"));
                                                    }
                                                }, emitter::onError);
                                            } else {
                                                emitter.onNext(responses);
                                                emitter.onComplete();
                                            }
                                        },
                                        emitter::onError
                                );

                        Disposable ignored = sendData(commands.get(0)).subscribe(success -> {
                            if (!success) {
                                emitter.onError(new IOException("发送命令失败"));
                            }
                        }, emitter::onError);

                        emitter.setDisposable(new Disposable() {
                            private boolean disposed = false;

                            @Override
                            public void dispose() {
                                if (!disposed) {
                                    disposed = true;
                                    if (disposable != null && !disposable.isDisposed()) {
                                        disposable.dispose();
                                    }
                                }
                            }

                            @Override
                            public boolean isDisposed() {
                                return disposed;
                            }
                        });
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }


    /**
     * 实现定时轮询发送数据
     *
     * @param commands     要轮询的字节命令列表
     * @param initialDelay 初始延迟时间（毫秒）
     * @param period       轮询周期（毫秒）
     * @param commandDelay 命令间延迟时间（毫秒）
     * @return Observable<List < byte [ ]>>，发出每轮轮询的响应数据
     */
    public Observable<List<byte[]>> startPolling(List<byte[]> commands,
                                                 long initialDelay,
                                                 long period,
                                                 long commandDelay,
                                                 int commandTimeout) {
        if (commands == null || commands.isEmpty()) {
            return Observable.error(new IllegalArgumentException("命令列表不能为空"));
        }

        return Observable.interval(initialDelay, period, TimeUnit.MILLISECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .flatMap(tick -> {
                    LogUtils.dTag(TAG, "开始第 " + (tick + 1) + " 轮多条命令发送，共 " + commands.size() + " 条命令");

                    return sendCommands(commands, commandTimeout)
                            .doOnNext(responses -> {
                                LogUtils.dTag(TAG, "轮询第 " + (tick + 1) + " 轮完成，收到 " + responses.size() + " 个响应");
                                for (int i = 0; i < responses.size(); i++) {
                                    LogUtils.dTag(TAG, "轮询响应 " + (i + 1) + ": " + CommonUtils.convertByteArrayToString(responses.get(i)));
                                }
                            })
                            .doOnError(throwable -> LogUtils.wTag(TAG, "轮询第 " + (tick + 1) + " 轮异常: " + throwable.getMessage()))
                            .onErrorReturn(throwable -> {
                                // 超时或其他异常时返回空列表，让轮询继续进行
                                LogUtils.wTag(TAG, "轮询第 " + (tick + 1) + " 轮超时或异常，返回空响应继续轮询");
                                return new ArrayList<>();
                            });
                })
                .doOnSubscribe(disposable ->
                        LogUtils.iTag(TAG, "开始多条命令轮询，命令数量: " + commands.size() +
                                ", 初始延迟: " + initialDelay + "ms" +
                                ", 轮询周期: " + period + "ms" +
                                ", 命令延迟: " + commandDelay + "ms"));
    }


    /**
     * 停止特定的轮询任务
     *
     * @param disposable 轮询任务的Disposable
     */
    public void stopPolling(Disposable disposable) {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
            compositeDisposable.remove(disposable);
        }
    }

    /**
     * 停止所有轮询任务
     */
    public void stopAllPolling() {
        compositeDisposable.clear();
    }

    public boolean isConnected() {
        return ySerialPort.getSerialPort() != null;
    }

    /**
     * 关闭串口并释放资源
     */
    public void closeSerialPort() {
        stopAllPolling();

        if (ySerialPort != null) {
            ySerialPort.onDestroy();
            ySerialPort = null;
        }

        if (dataEmitter != null && !dataEmitter.isDisposed()) {
            dataEmitter.onComplete();
            dataEmitter = null;
        }
    }

}