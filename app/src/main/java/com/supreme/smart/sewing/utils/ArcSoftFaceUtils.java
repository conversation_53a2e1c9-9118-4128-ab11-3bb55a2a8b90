package com.supreme.smart.sewing.utils;

import android.graphics.Bitmap;

public class ArcSoftFaceUtils {
    // 添加内存检查
    private static boolean hasEnoughMemory(int width, int height) {
        Runtime runtime = Runtime.getRuntime();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long availableMemory = maxMemory - (totalMemory - freeMemory);

        // 计算所需内存：ARGB数组 + NV21数组
        long requiredMemory = (long) width * height * 4 + (long) width * height * 3 / 2;

        return availableMemory > requiredMemory * 2; // 留出安全边距
    }

    public static byte[] bitmapToNV21(Bitmap bitmap) {
        if (bitmap == null || bitmap.isRecycled()) {
            return null;
        }

        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        // 检查内存是否足够
        if (!hasEnoughMemory(width, height)) {
            // 缩小图片尺寸
            float scale = 0.5f;
            int newWidth = (int) (width * scale);
            int newHeight = (int) (height * scale);
            Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true);

            try {
                return bitmapToNV21Internal(scaledBitmap);
            } finally {
                if (scaledBitmap != bitmap) {
                    scaledBitmap.recycle();
                }
            }
        }

        return bitmapToNV21Internal(bitmap);
    }

    private static byte[] bitmapToNV21Internal(Bitmap bitmap) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        int[] argb;
        byte[] nv21;

        try {
            argb = new int[width * height];
            bitmap.getPixels(argb, 0, width, 0, 0, width, height);

            nv21 = new byte[width * height * 3 / 2];
            encodeARGBToNV21(argb, width, height, nv21);

            return nv21;
        } catch (OutOfMemoryError e) {
            // 内存不足时返回null
            return null;
        } finally {
            // 显式清理引用
            argb = null;
            System.gc(); // 建议垃圾回收
        }
    }

    private static void encodeARGBToNV21(int[] argb, int width, int height, byte[] nv21) {
        final int frameSize = width * height;
        int yIndex = 0;
        int uvIndex = frameSize;
        int R, G, B, Y, U, V;
        int index = 0;

        for (int j = 0; j < height; j++) {
            for (int i = 0; i < width; i++) {
                R = (argb[index] & 0xFF0000) >> 16;
                G = (argb[index] & 0xFF00) >> 8;
                B = argb[index] & 0xFF;

                Y = ((66 * R + 129 * G + 25 * B + 128) >> 8) + 16;
                U = ((-38 * R - 74 * G + 112 * B + 128) >> 8) + 128;
                V = ((112 * R - 94 * G - 18 * B + 128) >> 8) + 128;

                nv21[yIndex++] = (byte) ((Y < 0) ? 0 : ((Y > 255) ? 255 : Y));
                if (j % 2 == 0 && index % 2 == 0) {
                    nv21[uvIndex++] = (byte) ((V < 0) ? 0 : ((V > 255) ? 255 : V));
                    nv21[uvIndex++] = (byte) ((U < 0) ? 0 : ((U > 255) ? 255 : U));
                }
                index++;
            }
        }
    }
}