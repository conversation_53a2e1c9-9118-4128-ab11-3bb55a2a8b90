package com.supreme.smart.sewing.tokenizer;


import com.hankcs.hanlp.dictionary.CustomDictionary;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class CommandMatcher {
    private static final Map<String, List<List<String>>> commandTemplates = new HashMap<>();

    static {

        // 初始化自定义词典，添加不应被分词的专业术语
        // 在HanLP分词系统中，"n 1000"是添加自定义词典条目时使用的格式，其中：
        // - n 表示词性，这里是名词(noun)的缩写
        // - 1000 表示词频，即这个词出现的频率权重
        // 这样设置的目的是告诉分词系统这些是完整的专业术语，应该作为一个整体词语处理，
        // 而不是被拆分成单独的字。
        // 词频设置为较高的1000，是为了增加这些词语在分词过程中被识别为整体的概率，
        // 确保"扫码"、"扫条码"、"扫二维码"等术语不会被错误地分开。
        CustomDictionary.add("扫码", "n 1000");
        CustomDictionary.add("扫条码", "n 1000");
        CustomDictionary.add("扫二维码", "n 1000");


        // 初始化命令模板，使用分词后的结果
        commandTemplates.put("OPEN_APP", Arrays.asList(
                Arrays.asList("打开", "应用"),
                Arrays.asList("启动", "程序"),
                Arrays.asList("运行", "软件")
        ));

        commandTemplates.put("CLOSE_APP", Arrays.asList(
                Arrays.asList("关闭", "应用"),
                Arrays.asList("退出", "程序"),
                Arrays.asList("停止", "运行")
        ));

        commandTemplates.put("MONITOR_CALL", Arrays.asList(
                Arrays.asList("班长", "呼叫"),
                Arrays.asList("呼叫", "班长"),
                Arrays.asList("联系", "班长"),
                Arrays.asList("班长", "联系"),
                Arrays.asList("组长", "呼叫"),
                Arrays.asList("呼叫", "组长"),
                Arrays.asList("联系", "组长"),
                Arrays.asList("组长", "联系"),
                Arrays.asList("班组长", "呼叫"),
                Arrays.asList("呼叫", "班组长"),
                Arrays.asList("联系", "班组长"),
                Arrays.asList("班组长", "联系")
        ));

        commandTemplates.put("READ_DATA", Arrays.asList(
                Arrays.asList("读取", "数据"),
                Arrays.asList("获取", "数据"),
                Arrays.asList("查询", "数据"),
                Arrays.asList("检查", "数据"),
                Arrays.asList("数据", "读取"),
                Arrays.asList("数据", "查询")
        ));

        commandTemplates.put("CHECK_STATUS", Arrays.asList(
                Arrays.asList("检查", "状态"),
                Arrays.asList("查看", "状态"),
                Arrays.asList("状态", "检查"),
                Arrays.asList("状态", "查看"),
                Arrays.asList("设备", "状态"),
                Arrays.asList("机器", "状态")
        ));

        commandTemplates.put("EMERGENCY_STOP", Arrays.asList(
                Arrays.asList("紧急", "停止"),
                Arrays.asList("停止", "机器"),
                Arrays.asList("急停"),
                Arrays.asList("停止", "运行"),
                Arrays.asList("关机"),
                Arrays.asList("关闭", "机器")
        ));

        commandTemplates.put("HELP", Arrays.asList(
                Arrays.asList("帮助"),
                Arrays.asList("使用", "说明"),
                Arrays.asList("操作", "指南"),
                Arrays.asList("功能", "介绍"),
                Arrays.asList("怎么", "使用"),
                Arrays.asList("如何", "操作")
        ));

        commandTemplates.put("RESET_COUNTER", Arrays.asList(
                Arrays.asList("重置", "计数器"),
                Arrays.asList("清零", "计数"),
                Arrays.asList("计数器", "重置"),
                Arrays.asList("计数", "清零"),
                Arrays.asList("重置", "数据"),
                Arrays.asList("清零", "数据")
        ));

        commandTemplates.put("ADJUST_SPEED", Arrays.asList(
                Arrays.asList("调整", "速度"),
                Arrays.asList("设置", "速度"),
                Arrays.asList("速度", "调整"),
                Arrays.asList("速度", "设置"),
                Arrays.asList("改变", "速度"),
                Arrays.asList("修改", "速度")
        ));

        commandTemplates.put("SCAN_BARCODE", Arrays.asList(
                Arrays.asList("扫描", "条码"),
                Arrays.asList("扫描", "二维码"),
                Arrays.asList("读取", "条码"),
                Arrays.asList("识别", "条码"),
                Arrays.asList("条码", "扫描"),
                Arrays.asList("二维码", "扫描"),
                Arrays.asList("扫码"),
                Arrays.asList("扫条码"),
                Arrays.asList("扫二维码")
        ));
    }

    public String matchCommand(List<String> words) {
        double maxSimilarity = 0.0;
        String matchedCommand = null;

        for (Map.Entry<String, List<List<String>>> entry : commandTemplates.entrySet()) {
            for (List<String> template : entry.getValue()) {
                double similarity = calculateSimilarity(words, template);
                if (similarity > maxSimilarity) {
                    maxSimilarity = similarity;
                    matchedCommand = entry.getKey();
                }
            }
        }

        return maxSimilarity > 0.7 ? matchedCommand : null;
    }

    private double calculateSimilarity(List<String> words1, List<String> words2) {
        // 使用Jaccard相似度
        Set<String> set1 = new HashSet<>(words1);
        Set<String> set2 = new HashSet<>(words2);

        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);

        return (double) intersection.size() / union.size();
    }
}
