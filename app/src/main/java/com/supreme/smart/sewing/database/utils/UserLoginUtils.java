package com.supreme.smart.sewing.database.utils;


import com.supreme.smart.sewing.database.beans.sys.SysUserLogin;
import com.supreme.smart.sewing.utils.CommonUtils;

import io.realm.Realm;
import io.realm.Sort;

public class UserLoginUtils {

    public synchronized static boolean login(String userId) {
        Realm realm = Realm.getDefaultInstance();
        try {
            realm.beginTransaction();
            SysUserLogin userLogin = new SysUserLogin();
            userLogin.setUserId(userId);
            userLogin.setLoginTime(CommonUtils.getCurrentTime());
            realm.copyToRealm(userLogin);
            realm.commitTransaction();
            return true;
        } catch (Exception e) {
            realm.cancelTransaction();
            return false;
        } finally {
            realm.close();
        }
    }


    public synchronized static boolean logout(String userId) {
        Realm realm = Realm.getDefaultInstance();
        try {
            realm.beginTransaction();
            SysUserLogin userLogin = realm.where(SysUserLogin.class).equalTo("user.id", userId).findFirst();
            if (userLogin != null) {
                userLogin.setLogoutTime(CommonUtils.getCurrentTime());
                realm.copyToRealmOrUpdate(userLogin);
                realm.commitTransaction();
                return true;
            }
            realm.cancelTransaction();
            return false;
        } catch (Exception e) {
            realm.cancelTransaction();
            return false;
        } finally {
            realm.close();
        }
    }


    public static String getLastLogin() {
        try (Realm realm = Realm.getDefaultInstance()) {
            SysUserLogin userLogin = realm.where(SysUserLogin.class)
                    .sort("loginTime", Sort.DESCENDING).limit(1).findFirst();
            if (userLogin != null) {
                return userLogin.getUserId();
            }
        }
        return null;
    }


}
