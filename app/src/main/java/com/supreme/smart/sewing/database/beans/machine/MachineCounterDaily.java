package com.supreme.smart.sewing.database.beans.machine;


import com.supreme.smart.sewing.utils.UUIDUtils;

import java.util.Date;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

/**
 * 设备记录（一台设备一天一条记录）
 * 包括： 件数读数、剪线次数读数、抬压脚次数读数、针数读数
 */
public class MachineCounterDaily extends RealmObject implements java.io.Serializable {
    /**
     * 主键
     */
    @PrimaryKey
    private String id;

    /**
     * 机器Id
     */
    private Machine machine;

    /**
     * 日期
     */
    private Date recDate;

    /**
     * 件数
     */
    private Long pieceCount;

    /**
     * 剪线次数
     */
    private Long cutCount;

    /**
     * 抬压脚次数
     */
    private Long raiseCount;

    /**
     * 针数读数
     */
    private Long needleCount;


    public MachineCounterDaily() {
        this.id = UUIDUtils.getId();
        this.recDate = UUIDUtils.getCreateDate();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;

    }

    public Machine getMachine() {
        return machine;
    }

    public void setMachine(Machine machine) {
        this.machine = machine;
    }

    public Date getRecDate() {
        return recDate;
    }

    public void setRecDate(Date recDate) {
        this.recDate = recDate;
    }

    public Long getPieceCount() {
        return pieceCount;
    }

    public void setPieceCount(Long pieceCount) {
        this.pieceCount = pieceCount;
    }

    public Long getCutCount() {
        return cutCount;
    }

    public void setCutCount(Long cutCount) {
        this.cutCount = cutCount;
    }

    public Long getRaiseCount() {
        return raiseCount;
    }

    public void setRaiseCount(Long raiseCount) {
        this.raiseCount = raiseCount;
    }

    public Long getNeedleCount() {
        return needleCount;
    }

    public void setNeedleCount(Long needleCount) {
        this.needleCount = needleCount;
    }
}
