package com.supreme.smart.sewing.voice.component;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RadialGradient;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 智能语音助手风格的音频波形动画视图
 */
public class WaveformView extends View {

    private Paint centerLinePaint;
    private Paint glowPaint; // 发光效果
    private Paint pulsePaint; // 脉冲效果
    private List<Float> amplitudes;
    private List<Paint> barPaints; // 每个波形条的画笔
    private final int maxAmplitudes = 50; // 增加更多波形条
    private boolean isAnimating = false;
    private boolean isListening = false; // 是否在监听状态
    private boolean isSpeaking = false; // 是否在说话状态
    private final Random random = new Random();
    private int currentVolume = 0; // 当前音量

    // 动画相关
    private Runnable animationRunnable;
    private long lastUpdateTime = 0;
    private static final long ANIMATION_INTERVAL = 50; // 50ms更新一次，更流畅
    
    // 脉冲动画
    private float pulseRadius = 0f;
    private final float maxPulseRadius = 200f; // 增大脉冲范围
    private ValueAnimator pulseAnimator;
    
    // 发光效果
    private float glowIntensity = 0f;
    private ValueAnimator glowAnimator;
    
    // 波形变化因子
    private float wavePhase = 0f; // 波形相位
    private float waveSpeed = 0.1f; // 波形速度
    private float speakingBoost = 1f; // 说话时的放大倍数
    
    // 颜色配置 - 更现代的渐变色
    private final int[] quietColors = {
        Color.parseColor("#B0BEC5"), // 静默时的灰蓝色
        Color.parseColor("#90A4AE"), // 灰色
        Color.parseColor("#78909C"), // 深灰色
    };
    
    private final int[] speakingColors = {
        Color.parseColor("#FF5722"), // 橙红色
        Color.parseColor("#FF9800"), // 橙色  
        Color.parseColor("#FFC107"), // 琥珀色
        Color.parseColor("#FFEB3B"), // 黄色
        Color.parseColor("#8BC34A"), // 浅绿色
        Color.parseColor("#4CAF50"), // 绿色
        Color.parseColor("#009688"), // 青绿色
        Color.parseColor("#00BCD4"), // 青色
        Color.parseColor("#03A9F4"), // 浅蓝色
        Color.parseColor("#2196F3"), // 蓝色
        Color.parseColor("#3F51B5"), // 靛蓝色
        Color.parseColor("#9C27B0"), // 紫色
        Color.parseColor("#E91E63"), // 粉红色
    };
    
    // 监听状态的主色调
    private final int listeningColor = Color.parseColor("#2196F3");
    private final int speakingActiveColor = Color.parseColor("#FF5722");
    
    // 公用音量检测器
    private final VoiceVolumeDetector volumeDetector = new VoiceVolumeDetector();

    public WaveformView(Context context) {
        super(context);
        init();
    }

    public WaveformView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public WaveformView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // 中心线画笔
        centerLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        centerLinePaint.setColor(Color.parseColor("#E8E8E8")); // 更淡的灰色中心线
        centerLinePaint.setStrokeWidth(1f);

        // 发光效果画笔
        glowPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        glowPaint.setStyle(Paint.Style.FILL);
        
        // 脉冲效果画笔
        pulsePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        pulsePaint.setStyle(Paint.Style.STROKE);
        pulsePaint.setStrokeWidth(4f);

        amplitudes = new ArrayList<>();
        barPaints = new ArrayList<>();
        
        // 初始化波形数据和画笔
        for (int i = 0; i < maxAmplitudes; i++) {
            amplitudes.add(0f);
            
            // 为每个条创建渐变画笔
            Paint barPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            barPaint.setStyle(Paint.Style.FILL);
            barPaint.setStrokeCap(Paint.Cap.ROUND);
            
            barPaints.add(barPaint);
        }



        // 动画更新器
        animationRunnable = new Runnable() {
            @Override
            public void run() {
                if (isAnimating) {
                    updateWaveform();
                    updateAnimationPhase();
                    invalidate();
                    postDelayed(this, ANIMATION_INTERVAL);
                }
            }
        };
        
        initAnimators();
    }
    
    private void initAnimators() {
        // 脉冲动画 - 说话时更快更强烈
        pulseAnimator = ValueAnimator.ofFloat(0f, maxPulseRadius);
        pulseAnimator.setDuration(1200); // 稍微加快
        pulseAnimator.setRepeatCount(ValueAnimator.INFINITE);
        pulseAnimator.setRepeatMode(ValueAnimator.RESTART);
        pulseAnimator.addUpdateListener(animation -> {
            pulseRadius = (Float) animation.getAnimatedValue();
            invalidate();
        });
        
        // 发光强度动画 - 说话时更明显
        glowAnimator = ValueAnimator.ofFloat(0f, 1f);
        glowAnimator.setDuration(600); // 加快闪烁频率
        glowAnimator.setRepeatCount(ValueAnimator.INFINITE);
        glowAnimator.setRepeatMode(ValueAnimator.REVERSE);
        glowAnimator.addUpdateListener(animation -> glowIntensity = (Float) animation.getAnimatedValue());
    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);
        
        int width = getWidth();
        int height = getHeight();
        int centerY = height / 2;
        int centerX = width / 2;
        
        if (width == 0 || height == 0) return;

        // 绘制背景发光效果
        if (isListening) {
            drawBackgroundGlow(canvas, centerX, centerY);
        }
        
        // 绘制脉冲效果（说话时更明显）
        if (isSpeaking && isAnimating) {
            drawPulseEffect(canvas, centerX, centerY);
            // 说话时添加多层脉冲效果
            drawMultiplePulseEffect(canvas, centerX, centerY);
        }

        // 绘制中心线
        canvas.drawLine(0, centerY, width, centerY, centerLinePaint);

        // 计算每个条的宽度和间距
        float totalPadding = width * 0.08f; // 两边各留4%的边距
        float availableWidth = width - totalPadding;
        float barSpacing = availableWidth / maxAmplitudes;
        float barWidth = barSpacing * 0.6f; // 条宽占60%
        
        // 绘制音频波形条
        for (int i = 0; i < amplitudes.size(); i++) {
            drawWaveBar(canvas, i, barSpacing, barWidth, totalPadding, centerY, height);
        }
    }
    
    /**
     * 绘制背景发光效果
     */
    private void drawBackgroundGlow(Canvas canvas, int centerX, int centerY) {
        float baseRadius = 60f;
        float radius = baseRadius + glowIntensity * (isSpeaking ? 80f : 30f); // 说话时发光更强
        int glowColor = isSpeaking ? speakingActiveColor : listeningColor;
        
        // 说话时发光更强烈
        float maxAlpha = isSpeaking ? 0.3f : 0.15f;
        
        RadialGradient gradient = new RadialGradient(
            centerX, centerY, radius,
            new int[]{
                adjustColorAlpha(glowColor, maxAlpha * glowIntensity),
                adjustColorAlpha(glowColor, maxAlpha * 0.5f * glowIntensity),
                Color.TRANSPARENT
            },
            new float[]{0f, 0.6f, 1f},
            Shader.TileMode.CLAMP
        );
        
        glowPaint.setShader(gradient);
        canvas.drawCircle(centerX, centerY, radius, glowPaint);
    }
    
    /**
     * 绘制脉冲效果
     */
    private void drawPulseEffect(Canvas canvas, int centerX, int centerY) {
        float alpha = 1f - (pulseRadius / maxPulseRadius);
        alpha = Math.max(0f, alpha * (isSpeaking ? 0.8f : 0.4f)); // 说话时脉冲更明显
        
        int pulseColor = adjustColorAlpha(speakingActiveColor, alpha);
        pulsePaint.setColor(pulseColor);
        pulsePaint.setStrokeWidth(6f - 4f * (pulseRadius / maxPulseRadius)); // 说话时线条更粗
        
        canvas.drawCircle(centerX, centerY, pulseRadius, pulsePaint);
    }
    
    /**
     * 绘制多层脉冲效果（仅说话时）
     */
    private void drawMultiplePulseEffect(Canvas canvas, int centerX, int centerY) {
        if (!isSpeaking) return;
        
        // 第二层脉冲，相位稍有延迟
        float secondPulseRadius = pulseRadius * 0.7f;
        float alpha = 1f - (secondPulseRadius / (maxPulseRadius * 0.7f));
        alpha = Math.max(0f, alpha * 0.6f);
        
        int pulseColor = adjustColorAlpha(Color.parseColor("#FFC107"), alpha); // 使用不同颜色
        pulsePaint.setColor(pulseColor);
        pulsePaint.setStrokeWidth(4f - 2f * (secondPulseRadius / (maxPulseRadius * 0.7f)));
        
        canvas.drawCircle(centerX, centerY, secondPulseRadius, pulsePaint);
    }
    
    /**
     * 绘制单个波形条
     */
    private void drawWaveBar(Canvas canvas, int index, float barSpacing, float barWidth, 
                           float totalPadding, int centerY, int height) {
        float amplitude = Math.abs(amplitudes.get(index));
        
        // 计算条的位置
        float x = totalPadding / 2 + index * barSpacing + (barSpacing - barWidth) / 2;
        
        // 计算条的高度，说话时大幅增强
        float volumeFactor = getSmoothedVolumeFactor();
        float distanceFromCenter = Math.abs(index - maxAmplitudes / 2f) / (maxAmplitudes / 2f);
        
        // 中心条更高，边缘条较低，形成自然的波形 - 增强层次感
        float centerBoost;
        if (isSpeaking) {
            // 说话时创造更强的中心聚焦效果
            centerBoost = 1f - distanceFromCenter * distanceFromCenter * 0.8f; // 二次衰减
            centerBoost = Math.max(0.2f, centerBoost); // 保证边缘有一定高度
        } else {
            // 静默时保持平缓
            centerBoost = 1f - distanceFromCenter * 0.3f;
            centerBoost = Math.max(0.5f, centerBoost);
        }
        float waveHeight = amplitude * centerBoost;
        
        // 基于相位的正弦波调制 - 从中心向两边扩散
        float centerIndex = maxAmplitudes / 2f;
        float distanceFromCenterAbs = Math.abs(index - centerIndex);
        // 让相位基于距离中心的距离，而不是绝对位置
        float phaseOffset = (distanceFromCenterAbs / centerIndex) * 2f * (float) Math.PI;
        float waveModulation = (float) Math.sin(wavePhase + phaseOffset) * (isSpeaking ? 0.5f : 0.2f) + 1f;
        waveHeight *= waveModulation;
        
        // 说话时的额外放大 - 降低放大倍数
        if (isSpeaking) {
            // 根据音量动态调整放大倍数，避免过度放大
            float adjustedBoost = Math.min(speakingBoost, 1.5f + (volumeFactor * 1.5f)); // 1.5-3.0倍
            waveHeight *= adjustedBoost;
        }
        
        // 添加音量衰减，让边缘条更有层次
        float volumeAttenuation = 0.3f + volumeFactor * 0.7f; // 0.3-1.0的衰减
        waveHeight *= volumeAttenuation;
        
        // 最小高度 - 降低基础高度
        float minHeight = isSpeaking ? 8f : 4f;
        if (waveHeight < minHeight) {
            waveHeight = minHeight + random.nextFloat() * (isSpeaking ? 6f : 3f);
        }
        
        // 最大高度限制 - 降低最大高度，创造更好的视觉效果
        float maxHeight = height * (isSpeaking ? 0.35f : 0.18f); // 降低最大高度
        waveHeight = Math.min(waveHeight, maxHeight);
        
        // 计算条的位置
        float top = centerY - waveHeight / 2;
        float bottom = centerY + waveHeight / 2;
        
        // 创建圆角矩形路径
        Path barPath = new Path();
        float cornerRadius = Math.min(barWidth / 2, isSpeaking ? 6f : 4f); // 说话时圆角更大
        barPath.addRoundRect(x, top, x + barWidth, bottom, cornerRadius, cornerRadius, Path.Direction.CW);
        
        // 设置条的颜色和渐变
        Paint barPaint = barPaints.get(index);
        setupBarPaint(barPaint, index, amplitude, volumeFactor, top, bottom);
        
        canvas.drawPath(barPath, barPaint);
    }
    
    /**
     * 设置波形条的画笔效果
     */
    private void setupBarPaint(Paint barPaint, int index, float amplitude, float volumeFactor, float top, float bottom) {
        // 根据状态选择颜色
        int baseColor;
        if (isSpeaking) {
            // 说话时使用明亮的彩色
            baseColor = speakingColors[index % speakingColors.length];
        } else if (isListening) {
            // 监听但无声音时使用蓝色系
            baseColor = listeningColor;
        } else {
            // 静态时使用灰色
            baseColor = quietColors[index % quietColors.length];
        }
        
        // 计算透明度 - 说话时更不透明
        float alpha = 0.3f;
        if (isSpeaking) {
            alpha = 0.8f + 0.2f * volumeFactor; // 说话时几乎完全不透明
            // 根据振幅调整透明度
            float amplitudeFactor = Math.min(1f, amplitude / 100f);
            alpha = Math.max(alpha, 0.6f + 0.4f * amplitudeFactor);
        } else if (isListening) {
            alpha = 0.4f + 0.3f * volumeFactor;
        }
        
        // 说话时创建更丰富的渐变效果
        LinearGradient gradient;
        if (isSpeaking) {
            // 说话时使用三色渐变，更丰富的视觉效果
            int brighterColor = adjustColorBrightness(baseColor, 1.3f);
            int darkerColor = adjustColorBrightness(baseColor, 0.7f);
            
            gradient = new LinearGradient(
                0, top, 0, bottom,
                new int[]{
                    adjustColorAlpha(brighterColor, alpha),
                    adjustColorAlpha(baseColor, alpha),
                    adjustColorAlpha(darkerColor, alpha * 0.8f)
                },
                new float[]{0f, 0.5f, 1f},
                Shader.TileMode.CLAMP
            );
        } else {
            // 静默时使用简单渐变
            gradient = new LinearGradient(
                0, top, 0, bottom,
                new int[]{
                    adjustColorAlpha(baseColor, alpha),
                    adjustColorAlpha(baseColor, alpha * 0.6f),
                    adjustColorAlpha(baseColor, alpha)
                },
                new float[]{0f, 0.5f, 1f},
                Shader.TileMode.CLAMP
            );
        }
        
        barPaint.setShader(gradient);
    }
    
    /**
     * 调整颜色亮度
     */
    private int adjustColorBrightness(int color, float factor) {
        int r = (int) Math.min(255, Color.red(color) * factor);
        int g = (int) Math.min(255, Color.green(color) * factor);
        int b = (int) Math.min(255, Color.blue(color) * factor);
        return Color.rgb(r, g, b);
    }
    
    /**
     * 调整颜色的透明度
     */
    private int adjustColorAlpha(int color, float alpha) {
        int alphaValue = Math.round(255 * Math.max(0f, Math.min(1f, alpha)));
        return (color & 0x00FFFFFF) | (alphaValue << 24);
    }
    
    /**
     * 获取平滑的音量因子
     */
    private float getSmoothedVolumeFactor() {
        return volumeDetector.getSmoothedVolumeFactor();
    }
    
    /**
     * 更新动画相位
     */
    private void updateAnimationPhase() {
        wavePhase += waveSpeed;
        if (wavePhase > 2 * Math.PI) {
            wavePhase -= (float) (2 * Math.PI);
        }
    }

    /**
     * 开始波形动画
     */
    public void startAnimation() {
        if (!isAnimating) {
            isAnimating = true;
            post(animationRunnable);
            
            // 确保动画操作在主线程中执行
            post(() -> {
                // 启动脉冲动画
                if (pulseAnimator != null && !pulseAnimator.isRunning()) {
                    pulseAnimator.start();
                }
            });
        }
    }
    
    /**
     * 设置监听状态
     */
    public void setListening(boolean listening) {
        this.isListening = listening;
        
        // 确保动画操作在主线程中执行
        post(() -> {
            if (listening) {
                // 开始发光动画
                if (glowAnimator != null && !glowAnimator.isRunning()) {
                    glowAnimator.start();
                }
            } else {
                // 停止发光动画
                if (glowAnimator != null && glowAnimator.isRunning()) {
                    glowAnimator.cancel();
                    glowIntensity = 0f;
                }
                // 停止说话状态
                isSpeaking = false;
            }
        });
        
        invalidate();
    }

    /**
     * 停止波形动画
     */
    public void stopAnimation() {
        isAnimating = false;
        removeCallbacks(animationRunnable);
        
        // 确保动画操作在主线程中执行
        post(() -> {
            // 停止所有动画
            if (pulseAnimator != null && pulseAnimator.isRunning()) {
                pulseAnimator.cancel();
            }
        });
        
        // 停止说话状态
        isSpeaking = false;
        
        // 渐变到静态状态
        smoothToFlat();
    }

    /**
     * 更新音量数据
     * @param volume 音量值 (0-100)
     */
    public void updateVolume(int volume) {
        this.currentVolume = Math.max(0, Math.min(100, volume));
        
        // 使用公用音量检测器
        boolean wasSpeaking = isSpeaking;
        volumeDetector.updateVolume(currentVolume);
        isSpeaking = volumeDetector.isSpeaking();
        
        // 添加调试信息
        if (isSpeaking != wasSpeaking) {
            LogUtils.dTag("WaveformView", "说话状态改变: " + wasSpeaking + " -> " + isSpeaking + ", 音量=" + currentVolume);
        }
        
        // 说话状态改变时的动画调整
        if (isSpeaking != wasSpeaking) {
            if (isSpeaking) {
                // 刚开始说话，加快动画
                speakingBoost = 1.8f; // 降低放大倍数
                waveSpeed = 0.22f;
                // 重新启动更快的发光动画 - 在主线程中执行
                post(() -> {
                    if (glowAnimator != null) {
                        glowAnimator.cancel();
                        glowAnimator.setDuration(400); // 更快闪烁
                        glowAnimator.start();
                    }
                });
            } else {
                // 停止说话，恢复正常
                speakingBoost = 1f;
                waveSpeed = 0.1f;
                // 恢复正常发光动画 - 在主线程中执行
                post(() -> {
                    if (glowAnimator != null && isListening) {
                        glowAnimator.cancel();
                        glowAnimator.setDuration(600);
                        glowAnimator.start();
                    }
                });
            }
        }
        
        // 根据音量调整动画速度和强度 - 降低强度范围
        if (isSpeaking) {
            waveSpeed = 0.18f + (currentVolume / 100f) * 0.25f; // 0.18 到 0.43
            speakingBoost = 1.5f + (currentVolume / 100f) * 0.8f; // 1.5 到 2.3
        }
    }

    /**
     * 更新波形数据
     */
    private void updateWaveform() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUpdateTime < ANIMATION_INTERVAL) {
            return;
        }
        lastUpdateTime = currentTime;

        // 更新所有条的振幅
        for (int i = 0; i < amplitudes.size(); i++) {
            float volumeFactor = getSmoothedVolumeFactor();
            float maxHeight = getHeight() / (isSpeaking ? 2.8f : 4f); // 降低基础高度，避免过满
            
            // 生成更自然的波形
            float newAmplitude;
            if (isSpeaking) {
                // 说话时的强烈动态波形
                float distanceFromCenter = Math.abs(i - maxAmplitudes / 2f) / (maxAmplitudes / 2f);
                
                // 创建更自然的中心聚焦效果
                float centerFactor = 1f - distanceFromCenter * distanceFromCenter * 0.7f; // 二次衰减
                centerFactor = Math.max(0.15f, centerFactor); // 边缘保持较小值
                
                // 添加多重相位调制 - 从中心向外扩散
                float centerIdx = maxAmplitudes / 2f;
                float distFromCenter = Math.abs(i - centerIdx);
                float normalizedDist = distFromCenter / centerIdx; // 0到1的归一化距离
                
                // 第一层波形：主要从中心向外扩散 - 像水波纹一样
                float phaseOffset1 = normalizedDist * 4f * (float) Math.PI;
                float sineWave1 = (float) Math.sin(wavePhase * 2f + phaseOffset1) * 0.4f + 1f;
                
                // 第二层波形：创建反向扩散效果 - 增强中心聚焦
                float phaseOffset2 = normalizedDist * 2.5f * (float) Math.PI;
                float sineWave2 = (float) Math.cos(wavePhase * 1.5f - phaseOffset2) * 0.3f + 1f;
                
                // 第三层：径向扩散效果 - 让中心更突出
                float radialWave = (float) Math.sin(wavePhase * 2.5f - normalizedDist * 3f * (float) Math.PI) * 0.2f + 1f;
                
                // 降低随机因子的变化范围
                float randomFactor = 0.8f + random.nextFloat() * 0.6f; // 0.8 到 1.4，减少变化
                newAmplitude = randomFactor * maxHeight * volumeFactor * centerFactor * sineWave1 * sineWave2 * radialWave;
                
                // 中心向外扩散的"爆发"效果
                if (random.nextFloat() < 0.12f) { // 12%概率
                    // 越靠近中心的条，爆发时越强烈
                    float burstIntensity = 1.2f + (1f - normalizedDist) * 0.4f; // 中心1.6倍，边缘1.2倍
                    newAmplitude *= burstIntensity;
                }
                
                // 更平滑的音量调制
                float volumeModulation = 0.6f + volumeFactor * 0.8f; // 0.6-1.4的范围
                newAmplitude *= volumeModulation;
                
            } else if (isListening) {
                // 监听状态的微小波动，明显小于说话状态
                // 基础波幅
                float baseAmplitude = 8f;
                newAmplitude = baseAmplitude + random.nextFloat() * baseAmplitude * 0.3f;
            } else {
                // 完全静态
                newAmplitude = 0f;
            }
            
            // 平滑过渡到新振幅
            float currentAmplitude = amplitudes.get(i);
            float smoothingFactor = isSpeaking ? 0.4f : 0.8f; // 说话时变化更快
            float smoothedAmplitude = currentAmplitude * (1f - smoothingFactor) + newAmplitude * smoothingFactor;
            
            amplitudes.set(i, smoothedAmplitude);
        }
    }

    /**
     * 平滑过渡到平坦状态
     */
    private void smoothToFlat() {
        post(new Runnable() {
            @Override
            public void run() {
                boolean needsUpdate = false;
                
                for (int i = 0; i < amplitudes.size(); i++) {
                    float current = amplitudes.get(i);
                    if (Math.abs(current) > 1f) {
                        // 逐渐减小波幅
                        amplitudes.set(i, current * 0.85f);
                        needsUpdate = true;
                    } else {
                        amplitudes.set(i, 0f);
                    }
                }
                
                if (needsUpdate) {
                    invalidate();
                    postDelayed(this, 40); // 40ms更新一次，更平滑
                } else {
                    // 最终重置
                    reset();
                }
            }
        });
    }

    /**
     * 重置波形
     */
    public void reset() {
        amplitudes.clear();
        for (int i = 0; i < maxAmplitudes; i++) {
            amplitudes.add(0f);
        }
        currentVolume = 0;
        isSpeaking = false;
        wavePhase = 0f;
        pulseRadius = 0f;
        glowIntensity = 0f;
        speakingBoost = 1f;
        
        // 重置公用音量检测器
        volumeDetector.reset();
        invalidate();
    }

}
