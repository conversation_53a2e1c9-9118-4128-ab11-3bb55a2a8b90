package com.supreme.smart.sewing.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.preference.PreferenceManager;

import java.util.Locale;

/**
 * 语言切换工具类
 * Language switching utility class
 */
public class LocaleHelper {
    
    private static final String SELECTED_LANGUAGE = "Locale.Helper.Selected.Language";
    
    // 支持的语言代码
    public static final String LANGUAGE_CHINESE = "zh";
    public static final String LANGUAGE_ENGLISH = "en";
    public static final String LANGUAGE_VIETNAMESE = "vi";
    public static final String LANGUAGE_INDONESIAN = "in";
    
    /**
     * 设置应用语言
     * @param context 上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    public static Context setLocale(Context context, String language) {
        persist(context, language);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return updateResources(context, language);
        }
        
        return updateResourcesLegacy(context, language);
    }
    
    /**
     * 获取当前设置的语言
     * @param context 上下文
     * @return 语言代码
     */
    public static String getLanguage(Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context)
                .getString(SELECTED_LANGUAGE, LANGUAGE_CHINESE);
    }
    
    /**
     * 保存语言设置
     * @param context 上下文
     * @param language 语言代码
     */
    private static void persist(Context context, String language) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(SELECTED_LANGUAGE, language);
        editor.apply();
    }
    
    /**
     * 更新资源配置 (Android N及以上)
     * @param context 上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    private static Context updateResources(Context context, String language) {
        Locale locale = getLocale(language);
        Configuration configuration = context.getResources().getConfiguration();
        configuration.setLocale(locale);
        configuration.setLayoutDirection(locale);
        
        return context.createConfigurationContext(configuration);
    }
    
    /**
     * 更新资源配置 (Android N以下)
     * @param context 上下文
     * @param language 语言代码
     * @return 上下文
     */
    @SuppressWarnings("deprecation")
    private static Context updateResourcesLegacy(Context context, String language) {
        Locale locale = getLocale(language);
        Locale.setDefault(locale);
        
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        configuration.locale = locale;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLayoutDirection(locale);
        }
        
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());
        return context;
    }
    
    /**
     * 根据语言代码获取Locale对象
     * @param language 语言代码
     * @return Locale对象
     */
    private static Locale getLocale(String language) {
        switch (language) {
            case LANGUAGE_ENGLISH:
                return Locale.ENGLISH;
            case LANGUAGE_VIETNAMESE:
                return new Locale("vi");
            case LANGUAGE_INDONESIAN:
                return new Locale("in");
            case LANGUAGE_CHINESE:
            default:
                return Locale.CHINESE;
        }
    }
    
    /**
     * 获取语言显示名称
     * @param language 语言代码
     * @return 显示名称
     */
    public static String getLanguageDisplayName(String language) {
        switch (language) {
            case LANGUAGE_ENGLISH:
                return "English";
            case LANGUAGE_VIETNAMESE:
                return "Tiếng Việt";
            case LANGUAGE_INDONESIAN:
                return "Bahasa Indonesia";
            case LANGUAGE_CHINESE:
            default:
                return "中文";
        }
    }
}