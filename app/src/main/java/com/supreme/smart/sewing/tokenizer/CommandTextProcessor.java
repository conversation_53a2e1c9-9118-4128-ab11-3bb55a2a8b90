package com.supreme.smart.sewing.tokenizer;

import com.blankj.utilcode.util.LogUtils;
import com.hankcs.hanlp.HanLP;

import java.util.List;
import java.util.stream.Collectors;

public class CommandTextProcessor {


    public List<String> segment(String text) {
        // 使用自定义分词器，确保自定义词典生效
        return HanLP.newSegment().seg(text)
                .stream()
                .map(term -> term.word)
                .collect(Collectors.toList());
    }

    public String process(String text) {
        // 1. 清洗文本
        String cleaned = cleanText(text);

        // 2. 繁体转简体
        cleaned = HanLP.convertToSimplifiedChinese(cleaned);
        return cleaned;
    }


    public String cleanText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }


        try {
            return text
                    // 标点符号
                    .replaceAll("\\p{Punct}", "")
                    // 常见语气词
                    .replaceAll("[啊呀嘛呗咯吗呢吧哎唉哇哟啦哦嗯额咧嘞喔哩哒呐喂]+", "")
                    // 叠词语气词
                    .replaceAll("哈哈+", "")
                    .replaceAll("嘿嘿+", "")
                    .replaceAll("嗯嗯+", "")
                    // 网络语气词
                    .replaceAll("[嘤嘻嘿噢噗嘘嗷嗨嗯哼嗯哈嗯嗯]+", "")
                    // 方言语气词
                    .replaceAll("[嘞咧哩喔嗳嗯哦哟嗯嗯]+", "");
        } catch (Exception e) {
            // 记录错误日志
            LogUtils.eTag("TextCleaning", "文本清理失败: " + e.getMessage());
            return text; // 返回原始文本
        }
    }
}