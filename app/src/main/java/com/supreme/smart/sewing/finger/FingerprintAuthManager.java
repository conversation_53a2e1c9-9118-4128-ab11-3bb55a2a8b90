package com.supreme.smart.sewing.finger;

import static com.supreme.smart.sewing.constants.CommonConstant.SYSTEM_SERVER_URL;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.Utils;
import com.supreme.smart.sewing.utils.HttpUtils;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

/**
 * 指纹认证管理器
 */
public enum FingerprintAuthManager {
    INSTANCE;
    private static final String TAG = "FingerprintAuthManager";

    // 服务器地址配置
    private static final String BASE_URL = SYSTEM_SERVER_URL;

    public interface FingerprintAuthCallback {
        void onSuccess(String data);

        void onError(String error);
    }

    /**
     * 基础设备兼容性检查
     */
    public String checkDeviceCompatibility() {
        // 检查Android版本
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.M) {
            return "Android版本过低（需要6.0+）";
        }

        // 检查设备是否支持指纹识别
        // 检查是否设置了锁屏密码
        Context context = Utils.getApp().getApplicationContext();
        android.app.KeyguardManager keyguardManager =
                (android.app.KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
        if (keyguardManager != null && !keyguardManager.isDeviceSecure()) {
            return "设备未设置锁屏密码";
        }

        return null; // 兼容性检查通过
    }

    /**
     * 绑定指纹
     */
    public void bindFingerprint(String userId,
                                FingerprintAuthCallback fingerprintAuthCallback) {
        JSONObject params = new JSONObject();
        params.put("userId", userId);
        params.put("deviceId", getDeviceId());
        params.put("deviceModel", getDeviceModel());
        params.put("deviceManufacturer", getDeviceManufacturer());
        params.put("androidVersion", getAndroidVersion());

        String jsonString = params.toJSONString();

        HttpUtils.post(BASE_URL, "/api/fingerprint/bind", new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                LogUtils.eTag(TAG, "绑定请求失败", Log.getStackTraceString(e));
                fingerprintAuthCallback.onError("绑定请求失败: " + e.getMessage());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) {
                String msg;
                try {
                    if (response.body() != null) {
                        String responseString = response.body().string();
                        //  LogUtils.iTag("responseString", responseString);
                        JSONObject json = JSON.parseObject(responseString);
                        if (json.getBoolean("success") && json.getInteger("code") == 200) {
                            msg = "绑定成功";
                            LogUtils.dTag(TAG, msg);
                            fingerprintAuthCallback.onSuccess("绑定成功");
                        } else {
                            msg = json.getString("message");
                            LogUtils.eTag(TAG, msg);
                            fingerprintAuthCallback.onError(msg);
                        }
                    } else {
                        msg = "服务器响应为空";
                        LogUtils.eTag(TAG, msg);
                        fingerprintAuthCallback.onError(msg);
                    }
                } catch (Exception e) {
                    msg = "解析绑定响应失败,绑定失败";
                    LogUtils.eTag(TAG, msg, Log.getStackTraceString(e));
                    fingerprintAuthCallback.onError(msg);
                }
            }
        }, jsonString);
    }

    /**
     * 指纹登录
     */
    public void loginFingerprint(FingerprintAuthCallback fingerprintAuthCallback) {
        JSONObject params = new JSONObject();
        params.put("deviceId", getDeviceId());
        String jsonString = params.toJSONString();
        HttpUtils.post(BASE_URL, "/api/fingerprint/login", new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                LogUtils.eTag(TAG, "指纹登录请求失败", Log.getStackTraceString(e));
                fingerprintAuthCallback.onError("指纹登录请求失败: " + e.getMessage());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) {
                String msg;
                try {
                    if (response.body() != null) {
                        String responseString = response.body().string();
                        LogUtils.iTag(TAG, responseString);
                        JSONObject json = JSON.parseObject(responseString);

                        if (json.getBoolean("success") && json.getInteger("code") == 200) {
                            // 服务器返回用户ID
                            msg = "指纹登录成功";
                            LogUtils.dTag(TAG, msg);
                            fingerprintAuthCallback.onSuccess(msg);

                            String userId = json.getString("result");
                            LogUtils.dTag(TAG, "userId = " + userId);
                            // 之后将通过用户ID，调用原先的系统登录接口实现登录
                        } else {
                            msg = json.getString("message");
                            LogUtils.eTag(TAG, msg);
                            fingerprintAuthCallback.onError(msg);
                        }
                    } else {
                        msg = "服务器响应为空,指纹登录失败";
                        LogUtils.eTag(TAG, msg);
                        fingerprintAuthCallback.onError(msg);
                    }
                } catch (Exception e) {
                    msg = "解析登录响应失败,指纹登录失败";
                    LogUtils.eTag(TAG, msg, Log.getStackTraceString(e));
                    fingerprintAuthCallback.onError(msg);
                }
            }
        }, jsonString);
    }

    /**
     * 解绑指纹
     */
    public void unbindFingerprint(FingerprintAuthCallback fingerprintAuthCallback) {
        JSONObject params = new JSONObject();
        params.put("deviceId", getDeviceId());

        String jsonString = params.toJSONString();

        HttpUtils.post(BASE_URL, "/api/fingerprint/unbind", new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                LogUtils.wTag(TAG, "解绑失败", Log.getStackTraceString(e));
                fingerprintAuthCallback.onError("解绑失败: " + e.getMessage());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) {
                String msg;
                try {
                    if (response.body() != null) {
                        String responseString = response.body().string();
                        JSONObject json = JSON.parseObject(responseString);
                        if (json.getBoolean("success") && json.getInteger("code") == 200) {
                            msg = "解绑成功";
                            LogUtils.dTag(TAG, msg);
                            fingerprintAuthCallback.onSuccess(msg);
                        } else {
                            msg = json.getString("message");
                            LogUtils.eTag(TAG, msg);
                            fingerprintAuthCallback.onError(msg);
                        }
                    } else {
                        msg = "服务器响应为空，解绑失败";
                        LogUtils.eTag(TAG, msg);
                        fingerprintAuthCallback.onError(msg);
                    }
                } catch (Exception e) {
                    msg = "解析响应失败，解绑失败";
                    LogUtils.eTag(TAG, msg, Log.getStackTraceString(e));
                    fingerprintAuthCallback.onError(msg);
                }
            }
        }, jsonString);
    }

    /**
     * 获取设备唯一标识符
     */
    @SuppressLint("HardwareIds")
    private String getDeviceId() {
        try {
            // 构建设备特征码：制造商+型号+Android版本+应用安装时间戳
            StringBuilder deviceBuilder = new StringBuilder();

            // 1. 设备硬件信息（相对稳定）
            deviceBuilder.append(getDeviceManufacturer())
                    .append("_")
                    .append(getDeviceModel())
                    .append("_")
                    .append(getAndroidVersion());


            // 2. 获取应用首次安装时间作为设备绑定标识
            Context context = Utils.getApp().getApplicationContext();
            long firstInstallTime = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0).firstInstallTime;
            deviceBuilder.append("_").append(firstInstallTime);


            // 3. 使用Android ID作为补充
            String androidId = DeviceUtils.getAndroidID();
            if (androidId != null && !androidId.isEmpty() && !"9774d56d682e549c".equals(androidId)) {
                deviceBuilder.append("_").append(androidId.substring(0, Math.min(8, androidId.length())));
            }

            // 4. 对最终字符串进行MD5处理，确保长度一致
            String deviceString = deviceBuilder.toString();
            return com.blankj.utilcode.util.EncryptUtils.encryptMD5ToString(deviceString);

        } catch (Exception e) {
            LogUtils.eTag(TAG, "获取设备标识失败", e);
            // 兜底方案：使用固定的设备硬件信息
            String fallback = getDeviceManufacturer() + "_" + getDeviceModel() + "_" + getAndroidVersion();
            return com.blankj.utilcode.util.EncryptUtils.encryptMD5ToString(fallback);
        }
    }

    /**
     * 获取设备型号
     */
    private String getDeviceModel() {
        return DeviceUtils.getModel();
    }

    /**
     * 获取设备制造商
     */
    private String getDeviceManufacturer() {
        return DeviceUtils.getManufacturer();
    }

    /**
     * 获取Android版本
     */
    private String getAndroidVersion() {
        return DeviceUtils.getSDKVersionName();
    }
}