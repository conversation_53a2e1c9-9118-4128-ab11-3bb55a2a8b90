package com.supreme.smart.sewing.voice.ability;

import android.os.Handler;
import android.os.Looper;

public class ThreadExt {
    private static final Handler mainThreadHandler = new Handler(Looper.getMainLooper());
    
    public static boolean isMainThread() {
        return Looper.myLooper() != Looper.getMainLooper();
    }
    
    public interface BlockRunner {
        void run();
    }
    
    public static void mainThread(BlockRunner block) {
        if (isMainThread()) {
            mainThreadHandler.post(block::run);
        } else {
            block.run();
        }
    }
    
    public static void mainThread(long delayMillis, BlockRunner block) {
        mainThreadHandler.postDelayed(() -> block.run(), delayMillis);
    }
} 