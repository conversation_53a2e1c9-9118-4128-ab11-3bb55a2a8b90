package com.supreme.smart.sewing.face;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;
import com.google.mlkit.vision.face.Face;

import java.util.ArrayList;
import java.util.List;

public class FaceOverlayView extends View {
    private static final String TAG = "FaceOverlayView";
    private List<Face> faces;
    private List<Face> previousFaces; // 保存上一帧的人脸数据
    private Paint paint;
    private RectF rectF; // 添加可重用的RectF对象
    private int rectColor= Color.YELLOW;
    private ValueAnimator faceAnimator;
    private ValueAnimator fadeOutAnimator;
    private float animationProgress = 1.0f;
    private float fadeAlpha = 1.0f; // 透明度控制
    private boolean isClearing = false; // 是否正在清除

    // 视图尺寸
    private int viewWidth;
    private int viewHeight;
    
    // 相机预览尺寸 - 动态更新
    private int cameraWidth = 640;
    private int cameraHeight = 480;
    
    // 平滑参数
    private static final long ANIMATION_DURATION = 100; // 100ms动画时长

    public FaceOverlayView(Context context) {
        super(context);
        init();
    }
    
    private void init() {
        paint = new Paint();
        paint.setStyle(Paint.Style.STROKE);
        paint.setColor(rectColor);
        paint.setStrokeWidth(5f);
        paint.setAntiAlias(true); // 开启抗锯齿

        // 初始化可重用的RectF对象
        rectF = new RectF();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        viewWidth = w;
        viewHeight = h;
    }

    public void setCameraSize(int width, int height) {
        this.cameraWidth = width;
        this.cameraHeight = height;
        LogUtils.dTag(TAG, "相机尺寸更新为: " + width + "x" + height);
    }
    
    /**
     * 带淡出动画的清除人脸框
     */
    public void clearFacesWithAnimation() {
        if (faces == null || faces.isEmpty()) {
            return;
        }
        
        isClearing = true;
        
        // 取消现有动画
        if (faceAnimator != null && faceAnimator.isRunning()) {
            faceAnimator.cancel();
        }
        if (fadeOutAnimator != null && fadeOutAnimator.isRunning()) {
            fadeOutAnimator.cancel();
        }
        
        // 创建淡出动画
        fadeOutAnimator = ValueAnimator.ofFloat(1.0f, 0.0f);
        fadeOutAnimator.setDuration(200); // 200ms淡出时间
        fadeOutAnimator.setInterpolator(new DecelerateInterpolator());
        fadeOutAnimator.addUpdateListener(animation -> {
            fadeAlpha = (float) animation.getAnimatedValue();
            postInvalidate();
        });
        fadeOutAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 动画结束后清除人脸数据
                faces = null;
                previousFaces = null;
                fadeAlpha = 1.0f;
                isClearing = false;
                postInvalidate();
            }
        });
        fadeOutAnimator.start();
    }
    
    /**
     * 立即清除人脸框（无动画）
     */
    public void clearFacesImmediately() {
        synchronized (this) {
            if (faceAnimator != null && faceAnimator.isRunning()) {
                faceAnimator.cancel();
            }
            if (fadeOutAnimator != null && fadeOutAnimator.isRunning()) {
                fadeOutAnimator.cancel();
            }
            
            faces = null;
            previousFaces = null;
            fadeAlpha = 1.0f;
            isClearing = false;
            postInvalidate();
        }
    }

    /**
     * 平滑更新人脸框
     */
    public void updateFaces(final List<Face> faces) {
        synchronized (this) {
            // 如果正在清除动画中，不更新
            if (isClearing) {
                return;
            }
            
            // 重置透明度
            fadeAlpha = 1.0f;
            
            // 如果有动画正在进行，先取消
            if (faceAnimator != null && faceAnimator.isRunning()) {
                faceAnimator.cancel();
            }
            
            this.previousFaces = this.faces != null ? new ArrayList<>(this.faces) : null;
            this.faces = faces != null ? new ArrayList<>(faces) : null;
            
            if (faces != null && !faces.isEmpty()) {
                // 启动平滑动画
                startSmoothAnimation();
            } else {
                // 没有人脸时直接更新
                animationProgress = 1.0f;
                postInvalidate();
            }
        }
    }
    
    private void startSmoothAnimation() {
        faceAnimator = ValueAnimator.ofFloat(0f, 1f);
        faceAnimator.setDuration(ANIMATION_DURATION);
        faceAnimator.setInterpolator(new DecelerateInterpolator());
        faceAnimator.addUpdateListener(animation -> {
            animationProgress = (float) animation.getAnimatedValue();
            postInvalidate();
        });
        faceAnimator.start();
    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);

        if (faces == null || faces.isEmpty())
            return;

        canvas.save();
        // 前置摄像头需要水平镜像
        canvas.scale(-1, 1, viewWidth / 2f, viewHeight / 2f);

        // 设置透明度
        int alpha = (int) (255 * fadeAlpha);
        paint.setAlpha(alpha);
        paint.setColor(rectColor);

        // 计算缩放比例
        float scaleX = (float) viewWidth / cameraWidth;
        float scaleY = (float) viewHeight / cameraHeight;

        for (int i = 0; i < faces.size(); i++) {
            Face face = faces.get(i);
            Rect bounds = face.getBoundingBox();

            // 计算当前帧坐标
            float left = bounds.left * scaleX;
            float top = bounds.top * scaleY;
            float right = bounds.right * scaleX;
            float bottom = bounds.bottom * scaleY;

            // 如果有上一帧数据，进行平滑插值
            if (previousFaces != null && i < previousFaces.size() && animationProgress < 1.0f) {
                Rect prevBounds = previousFaces.get(i).getBoundingBox();
                float prevLeft = prevBounds.left * scaleX;
                float prevTop = prevBounds.top * scaleY;
                float prevRight = prevBounds.right * scaleX;
                float prevBottom = prevBounds.bottom * scaleY;

                // 线性插值
                left = lerp(prevLeft, left, animationProgress);
                top = lerp(prevTop, top, animationProgress);
                right = lerp(prevRight, right, animationProgress);
                bottom = lerp(prevBottom, bottom, animationProgress);
            }

            // 重用RectF对象，避免在onDraw中创建新对象
            rectF.set(left, top, right, bottom);
            canvas.drawRoundRect(rectF, 20f, 20f, paint);
        }

        canvas.restore();

        // 恢复画笔透明度
        paint.setAlpha(255);
    }
    
    /**
     * 线性插值函数
     */
    private float lerp(float start, float end, float progress) {
        return start + (end - start) * progress;
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (faceAnimator != null && faceAnimator.isRunning()) {
            faceAnimator.cancel();
        }
        if (fadeOutAnimator != null && fadeOutAnimator.isRunning()) {
            fadeOutAnimator.cancel();
        }
    }

    /**
     * 取得方框颜色
     */
    public int getRectColor() {
        return rectColor;
    }

    /**
     * 设置方框颜色
     * @param rectColor
     */
    public void setRectColor(int rectColor) {
        this.rectColor = rectColor;
    }
}