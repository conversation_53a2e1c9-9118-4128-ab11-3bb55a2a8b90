package com.supreme.smart.sewing.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.usage.UsageStatsManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.media.AudioAttributes;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager;
import android.provider.Settings;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.supreme.smart.sewing.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 权限管理工具类
 * 简化权限请求和处理逻辑
 */
public class PermissionHelper {
    private static final String TAG = "PermissionHelper";

    // 通知配置枚举
    public enum NotificationMode {
        SILENT,          // 静音模式
        SOUND_ONLY,      // 仅声音
        VIBRATE_ONLY,    // 仅震动
        SOUND_AND_VIBRATE, // 声音+震动
        FOLLOW_SYSTEM    // 跟随系统设置
    }

    // 通知重要性级别
    public enum NotificationImportance {
        LOW,      // 低重要性（不显示在状态栏）
        DEFAULT,  // 默认重要性
        HIGH      // 高重要性（会弹出通知）
    }

    // 必需权限（影响核心功能）
    private static final String[] ESSENTIAL_PERMISSIONS = {
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CAMERA
    };


    private final AppCompatActivity activity;
    private final ActivityResultLauncher<String[]> permissionLauncher;
    private final ActivityResultLauncher<Intent> batteryOptimizationLauncher;
    private PermissionCallback callback;

    public interface PermissionCallback {
        void onPermissionResult(boolean allEssentialGranted, List<String> deniedPermissions);
    }

    public PermissionHelper(AppCompatActivity activity) {
        this.activity = activity;
        this.permissionLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.RequestMultiplePermissions(),
                this::handlePermissionResult
        );
        this.batteryOptimizationLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    LogUtils.iTag(TAG, "=== 电池优化设置页面返回 ===");
                    LogUtils.iTag(TAG, "结果码: " + result.getResultCode());
                    LogUtils.iTag(TAG, "Intent数据: " + (result.getData() != null ? result.getData().toString() : "null"));

                    // 延迟检查电池优化状态（给系统时间更新状态）
                    activity.getWindow().getDecorView().postDelayed(() -> {
                        boolean isOptimized = isIgnoringBatteryOptimizations();
                        LogUtils.iTag(TAG, "电池优化状态检查: " + (isOptimized ? "已加入白名单" : "未加入白名单"));

                        if (isOptimized) {
                            ToastUtils.showShort("电池优化设置成功！");
                            LogUtils.iTag(TAG, "电池优化权限设置成功，继续下一步");
                            // 继续下一步权限设置
                            requestAutoStartPermission();
                        } else {
                            LogUtils.wTag(TAG, "电池优化权限仍未设置，显示手动指导");
                            ToastUtils.showShort("请手动设置电池优化白名单");
                            // 显示手动设置指导
                            showBatteryOptimizationManualGuide();
                        }
                    }, 1000); // 延迟1秒检查
                }
        );
    }

    /**
     * 请求所有必要权限
     */
    public void requestPermissions(PermissionCallback callback) {
        this.callback = callback;

        List<String> permissionsToRequest = getAllPermissionsToRequest();

        if (permissionsToRequest.isEmpty()) {
            LogUtils.dTag(TAG, "所有权限已授予");
            if (callback != null) {
                callback.onPermissionResult(true, new ArrayList<>());
            }
            // 权限处理完成后，检查后台权限
            checkAndAutoOpenBackgroundPermissionSettings();
        } else {
            LogUtils.dTag(TAG, "请求权限: " + permissionsToRequest);
            permissionLauncher.launch(permissionsToRequest.toArray(new String[0]));
        }

        // 处理Android 11+的特殊存储权限
        handleSpecialStoragePermission();
    }

    /**
     * 获取所有需要请求的权限
     */
    private List<String> getAllPermissionsToRequest() {
        List<String> permissionsToRequest = new ArrayList<>();

        // 添加必需权限
        for (String permission : ESSENTIAL_PERMISSIONS) {
            if (!isPermissionGranted(permission)) {
                permissionsToRequest.add(permission);
            }
        }

        // 添加存储权限（根据API级别）
        addStoragePermissions(permissionsToRequest);

        // 添加通知权限（Android 13+）
        addNotificationPermissions(permissionsToRequest);

        // 添加蓝牙权限（Android 12+）
        // addBluetoothPermissions(permissionsToRequest);

        return permissionsToRequest;
    }

    /**
     * 添加存储权限（完整版本适配）
     */
    @SuppressLint("ObsoleteSdkInt")
    private void addStoragePermissions(List<String> permissions) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+): 不再申请分类媒体权限，只使用基本存储权限
            // 如果需要访问媒体文件，将依赖 MANAGE_EXTERNAL_STORAGE 权限
            LogUtils.dTag(TAG, "Android 13+: 跳过媒体权限，只使用基本存储权限");

        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11-12 (API 30-32): 使用传统存储权限
            if (!isPermissionGranted(Manifest.permission.READ_EXTERNAL_STORAGE)) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            }
            if (!isPermissionGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
            LogUtils.dTag(TAG, "添加Android 11-12存储权限");

        } else {
            // Android 10及以下 (API ≤ 29): 使用传统存储权限
            if (!isPermissionGranted(Manifest.permission.READ_EXTERNAL_STORAGE)) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            }
            if (!isPermissionGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
            LogUtils.dTag(TAG, "添加Android 10及以下存储权限");
        }

        // 添加管理外部存储权限（Android 11+，特殊权限）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // MANAGE_EXTERNAL_STORAGE 是特殊权限，需要通过 Settings 申请
            // 这里只记录，不添加到普通权限列表中
            boolean hasManagePermission = false;
            try {
                hasManagePermission = android.os.Environment.isExternalStorageManager();
            } catch (Exception e) {
                LogUtils.wTag(TAG, "检查MANAGE_EXTERNAL_STORAGE权限失败", e);
            }
            LogUtils.dTag(TAG, "MANAGE_EXTERNAL_STORAGE权限状态: " + hasManagePermission);
        }
    }

    /**
     * 添加通知权限（Android 13+）
     */
    @SuppressLint("ObsoleteSdkInt")
    private void addNotificationPermissions(List<String> permissions) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 需要动态申请通知权限
            if (!isPermissionGranted(Manifest.permission.POST_NOTIFICATIONS)) {
                permissions.add(Manifest.permission.POST_NOTIFICATIONS);
                LogUtils.dTag(TAG, "添加通知权限: POST_NOTIFICATIONS");
            }
        } else {
            // Android 12及以下不需要动态申请通知权限
            LogUtils.dTag(TAG, "Android 12及以下，无需动态申请通知权限");
        }
    }

    // ==================== 通知配置一键设定 ====================

    /**
     * 一键设定通知方式
     *
     * @param channelId   通知渠道ID
     * @param channelName 通知渠道名称
     * @param mode        通知模式
     * @param importance  重要性级别
     */
    @SuppressLint("ObsoleteSdkInt")
    public void setupNotificationChannel(String channelId, String channelName,
                                         NotificationMode mode, NotificationImportance importance) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                NotificationManager notificationManager = (NotificationManager)
                        activity.getSystemService(Context.NOTIFICATION_SERVICE);

                if (notificationManager == null) {
                    LogUtils.eTag(TAG, "NotificationManager为空，无法创建通知渠道");
                    return;
                }

                // 设置重要性级别
                int importanceLevel = getImportanceLevel(importance);

                // 创建通知渠道
                NotificationChannel channel = new NotificationChannel(channelId, channelName, importanceLevel);

                // 根据模式配置通知方式
                configureNotificationMode(channel, mode);

                // 创建渠道
                notificationManager.createNotificationChannel(channel);

                LogUtils.iTag(TAG, String.format("通知渠道已创建 - ID: %s, 模式: %s, 重要性: %s",
                        channelId, mode, importance));

            } catch (Exception e) {
                LogUtils.eTag(TAG, "创建通知渠道失败", e);
            }
        } else {
            LogUtils.dTag(TAG, "Android 8以下版本，无需创建通知渠道");
        }
    }

    /**
     * 配置通知模式
     */
    @SuppressLint("ObsoleteSdkInt")
    private void configureNotificationMode(NotificationChannel channel, NotificationMode mode) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            switch (mode) {
                case SILENT:
                    // 静音模式
                    channel.setSound(null, null);
                    channel.enableVibration(false);
                    channel.enableLights(false);
                    LogUtils.dTag(TAG, "设置为静音模式");
                    break;

                case SOUND_ONLY:
                    // 仅声音
                    Uri defaultSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
                    AudioAttributes audioAttributes = new AudioAttributes.Builder()
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                            .build();
                    channel.setSound(defaultSound, audioAttributes);
                    channel.enableVibration(false);
                    channel.enableLights(true);
                    LogUtils.dTag(TAG, "设置为仅声音模式");
                    break;

                case VIBRATE_ONLY:
                    // 仅震动
                    channel.setSound(null, null);
                    channel.enableVibration(true);
                    channel.setVibrationPattern(new long[]{0, 250, 250, 250}); // 震动模式
                    channel.enableLights(true);
                    LogUtils.dTag(TAG, "设置为仅震动模式");
                    break;

                case SOUND_AND_VIBRATE:
                    // 声音+震动
                    Uri sound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
                    AudioAttributes audio = new AudioAttributes.Builder()
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                            .build();
                    channel.setSound(sound, audio);
                    channel.enableVibration(true);
                    channel.setVibrationPattern(new long[]{0, 250, 250, 250});
                    channel.enableLights(true);
                    LogUtils.dTag(TAG, "设置为声音+震动模式");
                    break;

                case FOLLOW_SYSTEM:
                default:
                    // 跟随系统设置（默认行为）
                    // 不做特殊设置，使用系统默认
                    LogUtils.dTag(TAG, "设置为跟随系统模式");
                    break;
            }
        }
    }

    /**
     * 获取重要性级别
     */
    @SuppressLint("ObsoleteSdkInt")
    private int getImportanceLevel(NotificationImportance importance) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            switch (importance) {
                case LOW:
                    return NotificationManager.IMPORTANCE_LOW;
                case HIGH:
                    return NotificationManager.IMPORTANCE_HIGH;
                case DEFAULT:
                default:
                    return NotificationManager.IMPORTANCE_DEFAULT;
            }
        }
        return NotificationManager.IMPORTANCE_DEFAULT;
    }

    /**
     * 快速设置推送通知渠道（预设配置）
     */
    public void setupPushNotificationChannel() {
        setupNotificationChannel(
                "smart_sewing_push",
                "智能缝纫系统推送",
                NotificationMode.SOUND_AND_VIBRATE,
                NotificationImportance.DEFAULT
        );
    }

    /**
     * 快速设置前台服务通知渠道（预设配置）
     */
    public void setupForegroundServiceChannel() {
        setupNotificationChannel(
                "smart_sewing_service",
                "智能缝纫系统服务",
                NotificationMode.SILENT,
                NotificationImportance.LOW
        );
    }

    /**
     * 批量设置所有通知渠道
     */
    public void setupAllNotificationChannels() {
        LogUtils.iTag(TAG, "开始设置所有通知渠道");

        // 推送通知渠道
        setupPushNotificationChannel();

        // 前台服务通知渠道
        setupForegroundServiceChannel();

        // 可以添加更多渠道...

        LogUtils.iTag(TAG, "所有通知渠道设置完成");
    }

    /**
     * 获取所有通知渠道的状态
     */
    @SuppressLint("ObsoleteSdkInt")
    public String getAllNotificationChannelsStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== 所有通知渠道状态 ===\n");

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                NotificationManager notificationManager = (NotificationManager)
                        activity.getSystemService(Context.NOTIFICATION_SERVICE);

                if (notificationManager != null) {
                    List<NotificationChannel> channels = notificationManager.getNotificationChannels();
                    status.append("渠道总数: ").append(channels.size()).append("\n");

                    for (NotificationChannel channel : channels) {
                        status.append("渠道ID: ").append(channel.getId()).append("\n");
                        status.append("  名称: ").append(channel.getName()).append("\n");
                        status.append("  重要性: ").append(getImportanceString(channel.getImportance())).append("\n");
                        status.append("  声音: ").append(channel.getSound() != null ? "启用" : "禁用").append("\n");
                        status.append("  震动: ").append(channel.shouldVibrate() ? "启用" : "禁用").append("\n");
                        status.append("  指示灯: ").append(channel.shouldShowLights() ? "启用" : "禁用").append("\n");
                        status.append("---\n");
                    }
                }
            } catch (Exception e) {
                status.append("获取渠道状态失败: ").append(e.getMessage()).append("\n");
            }
        } else {
            status.append("Android 8以下版本，无通知渠道概念\n");
        }

        return status.toString();
    }

    /**
     * 获取重要性级别的字符串描述
     */
    @SuppressLint("ObsoleteSdkInt")
    private String getImportanceString(int importance) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            switch (importance) {
                case NotificationManager.IMPORTANCE_NONE:
                    return "无";
                case NotificationManager.IMPORTANCE_MIN:
                    return "最低";
                case NotificationManager.IMPORTANCE_LOW:
                    return "低";
                case NotificationManager.IMPORTANCE_DEFAULT:
                    return "默认";
                case NotificationManager.IMPORTANCE_HIGH:
                    return "高";
                case NotificationManager.IMPORTANCE_MAX:
                    return "最高";
                default:
                    return "未知";
            }
        }
        return "不支持";
    }

    /**
     * 检查权限是否已授予
     */
    private boolean isPermissionGranted(String permission) {
        return ActivityCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 处理权限请求结果
     */
    private void handlePermissionResult(Map<String, Boolean> permissions) {
        List<String> deniedEssential = new ArrayList<>();
        List<String> deniedOptional = new ArrayList<>();

        for (Map.Entry<String, Boolean> entry : permissions.entrySet()) {
            if (!entry.getValue()) {
                String permission = entry.getKey();
                if (isEssentialPermission(permission)) {
                    deniedEssential.add(getPermissionDisplayName(permission));
                } else {
                    deniedOptional.add(getPermissionDisplayName(permission));
                }
            }
        }

        // 显示结果
        showPermissionResult(deniedEssential, deniedOptional);

        // 回调结果
        if (callback != null) {
            List<String> allDenied = new ArrayList<>(deniedEssential);
            allDenied.addAll(deniedOptional);
            callback.onPermissionResult(deniedEssential.isEmpty(), allDenied);
        }

        // 权限处理完成后，检查后台权限
        checkAndAutoOpenBackgroundPermissionSettings();
    }

    /**
     * 判断是否为必需权限
     */
    private boolean isEssentialPermission(String permission) {
        return Arrays.asList(ESSENTIAL_PERMISSIONS).contains(permission);
    }

    /**
     * 显示权限请求结果
     */
    private void showPermissionResult(List<String> deniedEssential, List<String> deniedOptional) {
        if (deniedEssential.isEmpty() && deniedOptional.isEmpty()) {
            Toast.makeText(activity, "权限授予成功", Toast.LENGTH_SHORT).show();
            LogUtils.dTag(TAG, "所有权限授予成功");
        } else if (!deniedEssential.isEmpty()) {
            String message = "缺少必需权限：" + String.join("、", deniedEssential) +
                    "\n请在设置中手动开启";
            Toast.makeText(activity, message, Toast.LENGTH_LONG).show();
            LogUtils.wTag(TAG, "必需权限被拒绝: " + deniedEssential);
        } else {
            LogUtils.dTag(TAG, "可选权限被拒绝: " + deniedOptional);
        }
    }

    /**
     * 获取权限的显示名称
     */
    private String getPermissionDisplayName(String permission) {
        switch (permission) {
            case Manifest.permission.RECORD_AUDIO:
                return activity.getString(R.string.permission_name_record_audio);
            case Manifest.permission.CAMERA:
                return activity.getString(R.string.permission_name_camera);
            case Manifest.permission.WRITE_EXTERNAL_STORAGE:
            case Manifest.permission.READ_EXTERNAL_STORAGE:
                return activity.getString(R.string.permission_name_storage);
            case Manifest.permission.POST_NOTIFICATIONS:
                return activity.getString(R.string.permission_name_notification);
            case Manifest.permission.BLUETOOTH_CONNECT:
                return activity.getString(R.string.permission_name_bluetooth);
            case Manifest.permission.ACCESS_FINE_LOCATION:
                return activity.getString(R.string.permission_name_location);
            // 已移除的媒体权限（不再使用）
            // case Manifest.permission.READ_MEDIA_IMAGES: return "图片";
            // case Manifest.permission.READ_MEDIA_VIDEO: return "视频";
            // case Manifest.permission.READ_MEDIA_AUDIO: return "音频";
            default:
                return permission.substring(permission.lastIndexOf('.') + 1);
        }
    }

    /**
     * 处理Android 11+的特殊存储权限
     */
    @SuppressLint("ObsoleteSdkInt")
    private void handleSpecialStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                try {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                    intent.setData(Uri.parse("package:" + activity.getPackageName()));
                    activity.startActivity(intent);
                    LogUtils.dTag(TAG, "打开存储权限设置页面");
                } catch (Exception e) {
                    LogUtils.wTag(TAG, "无法打开存储权限设置页面: " + e.getMessage());
                }
            }
        }
    }

    // ==================== 电源管理权限设置 ====================

    /**
     * 检查所有电源管理权限状态
     */
    private List<String> checkAllPowerPermissions() {
        List<String> deniedPermissions = new ArrayList<>();

        // 1. 检查电池优化白名单
        if (!isIgnoringBatteryOptimizations()) {
            deniedPermissions.add(activity.getString(R.string.power_permission_battery_optimization));
        }

        // 2. 检查自启动权限（通过检测是否有开机广播接收器）
        if (!hasBootPermission()) {
            deniedPermissions.add(activity.getString(R.string.power_permission_auto_start));
        }

        // 3. 检查后台运行权限
        if (!hasBackgroundRunPermission()) {
            deniedPermissions.add(activity.getString(R.string.power_permission_background_run));
        }

        return deniedPermissions;
    }

    /**
     * 检查应用是否在电池优化白名单中
     */
    @SuppressLint("ObsoleteSdkInt")
    public boolean isIgnoringBatteryOptimizations() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                PowerManager powerManager = (PowerManager) activity.getSystemService(Context.POWER_SERVICE);
                return powerManager != null && powerManager.isIgnoringBatteryOptimizations(activity.getPackageName());
            } catch (Exception e) {
                LogUtils.eTag(TAG, "检查电池优化白名单失败", e);
                return false;
            }
        }
        return true; // Android 6.0以下不需要此权限
    }

    /**
     * 启动电池优化设置
     */
    @SuppressLint("ObsoleteSdkInt")
    private void startBatteryOptimizationSetting() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                // 方法1: 直接请求忽略电池优化（推荐方式）
                @SuppressLint("BatteryLife")
                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));

                // 检查Intent是否可以被处理
                if (intent.resolveActivity(activity.getPackageManager()) != null) {
                    batteryOptimizationLauncher.launch(intent);
                    LogUtils.iTag(TAG, "使用直接请求方式打开电池优化设置");
                } else {
                    // 方法2: 打开电池优化设置列表
                    requestBatteryOptimizationFallback();
                }

            } catch (Exception e) {
                LogUtils.eTag(TAG, "启动电池优化设置失败", e);
                requestBatteryOptimizationFallback();
            }
        }
    }

    /**
     * 电池优化设置备选方案
     */
    private void requestBatteryOptimizationFallback() {
        try {
            Intent intent = new Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
            batteryOptimizationLauncher.launch(intent);
            ToastUtils.showShort(activity.getString(R.string.toast_battery_optimization_guide));
            LogUtils.iTag(TAG, "已打开电池优化设置列表页面");
        } catch (Exception e2) {
            LogUtils.eTag(TAG, "备选方案也失败，打开应用详情页面", e2);
            try {
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                batteryOptimizationLauncher.launch(intent);
                ToastUtils.showShort(activity.getString(R.string.toast_battery_optimization_app_info));
            } catch (Exception e3) {
                LogUtils.eTag(TAG, "所有电池优化设置方案都失败", e3);
                showBatteryOptimizationManualGuide();
            }
        }
    }

    /**
     * 显示电池优化手动设置指导
     */
    private void showBatteryOptimizationManualGuide() {
        new AlertDialog.Builder(activity)
                .setTitle(activity.getString(R.string.dialog_battery_optimization_manual_title))
                .setMessage(activity.getString(R.string.dialog_battery_optimization_manual_message))
                .setPositiveButton(activity.getString(R.string.dialog_battery_optimization_manual_positive), (dialog, which) -> requestAutoStartPermission())
                .setNegativeButton(activity.getString(R.string.dialog_battery_optimization_manual_negative), (dialog, which) -> startBatteryOptimizationSetting())
                .show();
    }

    /**
     * 检查是否有开机自启动权限
     */
    private boolean hasBootPermission() {
        // 检查是否声明了RECEIVE_BOOT_COMPLETED权限
        try {
            PackageManager pm = activity.getPackageManager();
            int result = pm.checkPermission(Manifest.permission.RECEIVE_BOOT_COMPLETED, activity.getPackageName());
            return result == PackageManager.PERMISSION_GRANTED;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查开机权限失败", e);
            return false;
        }
    }

    /**
     * 请求自启动权限
     */
    public void requestAutoStartPermission() {
        LogUtils.iTag(TAG, "开始设置自启动权限");

        // 显示自启动权限设置对话框
        showAutoStartPermissionDialog();
    }

    /**
     * 显示自启动权限设置对话框
     */
    private void showAutoStartPermissionDialog() {
        new AlertDialog.Builder(activity)
                .setTitle(activity.getString(R.string.dialog_auto_start_title))
                .setMessage(activity.getString(R.string.dialog_auto_start_message))
                .setPositiveButton(activity.getString(R.string.dialog_auto_start_positive), (dialog, which) -> openAutoStartSettings())
                .setNegativeButton(activity.getString(R.string.dialog_auto_start_negative), (dialog, which) -> requestBackgroundRunPermission())
                .setCancelable(false)
                .show();
    }

    /**
     * 打开自启动设置页面
     */
    private void openAutoStartSettings() {
        // 各厂商自启动设置页面Intent
        String[] autoStartIntents = {
                // 小米
                "com.miui.securitycenter/com.miui.permcenter.autostart.AutoStartManagementActivity",
                "com.miui.securitycenter/com.miui.powercenter.PowerSettings",
                // 华为
                "com.huawei.systemmanager/com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity",
                "com.huawei.systemmanager/com.huawei.systemmanager.optimize.process.ProtectActivity",
                // OPPO
                "com.coloros.safecenter/com.coloros.safecenter.permission.startup.FakeActivity",
                "com.oppo.safe/com.oppo.safe.permission.startup.StartupAppListActivity",
                // vivo
                "com.iqoo.secure/com.iqoo.secure.ui.phoneoptimize.AddWhiteListActivity",
                "com.vivo.permissionmanager/com.vivo.permissionmanager.activity.BgStartUpManagerActivity",
                // 魅族
                "com.meizu.safe/com.meizu.safe.security.SHOW_APPSEC",
                // 三星
                "com.samsung.android.lool/com.samsung.android.sm.ui.battery.BatteryActivity",
                // 一加
                "com.oneplus.security/com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity"
        };

        boolean found = false;
        for (String intentStr : autoStartIntents) {
            try {
                String[] parts = intentStr.split("/");
                Intent intent = new Intent();
                intent.setComponent(new ComponentName(parts[0], parts[1]));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

                // 检查这个Intent是否可以被解析
                if (intent.resolveActivity(activity.getPackageManager()) != null) {
                    activity.startActivity(intent);
                    ToastUtils.showShort(activity.getString(R.string.toast_auto_start_guide));
                    found = true;
                    break;
                }
            } catch (Exception e) {
                LogUtils.dTag(TAG, "尝试厂商自启动设置页面失败: " + intentStr);
            }
        }

        if (!found) {
            // 如果都不行，打开通用应用管理页面
            try {
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                activity.startActivity(intent);
                ToastUtils.showShort(activity.getString(R.string.toast_auto_start_app_info));
            } catch (Exception e) {
                LogUtils.eTag(TAG, "打开应用详情页面失败", e);
                ToastUtils.showShort(activity.getString(R.string.toast_auto_start_manual));
            }
        }

        // 继续下一步
        requestBackgroundRunPermission();
    }

    /**
     * 检查后台运行权限（更全面的检测）
     */
    @SuppressLint("ObsoleteSdkInt")
    private boolean hasBackgroundRunPermission() {
        // 在Android 6.0+上，检查是否被限制后台运行
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                // 1. 检查电池优化状态
                boolean batteryOptimized = isIgnoringBatteryOptimizations();
                LogUtils.dTag(TAG, "电池优化白名单状态: " + batteryOptimized);

                // 2. 检查后台限制（Android 7.0+）
                boolean backgroundRestricted = false;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    try {
                        ActivityManager activityManager = (ActivityManager) activity.getSystemService(Context.ACTIVITY_SERVICE);
                        if (activityManager != null) {
                            backgroundRestricted = activityManager.isBackgroundRestricted();
                            LogUtils.dTag(TAG, "后台限制状态: " + backgroundRestricted);
                        }
                    } catch (Exception e) {
                        LogUtils.eTag(TAG, "检查后台限制失败", e);
                    }
                }

                // 3. 检查应用待机状态（Android 6.0+）
                boolean isAppStandby = false;
                try {
                    UsageStatsManager usageStatsManager = (UsageStatsManager) activity.getSystemService(Context.USAGE_STATS_SERVICE);
                    if (usageStatsManager != null) {
                        isAppStandby = usageStatsManager.isAppInactive(activity.getPackageName());
                        LogUtils.dTag(TAG, "应用待机状态: " + isAppStandby);
                    }
                } catch (Exception e) {
                    LogUtils.eTag(TAG, "检查应用待机状态失败", e);
                }

                // 更严格的检查：需要同时满足多个条件
                // 1. 电池优化白名单：已加入
                // 2. 后台限制：未被限制
                // 3. 应用待机：未处于待机状态
                boolean hasPermission = batteryOptimized && !backgroundRestricted && !isAppStandby;

                LogUtils.dTag(TAG, "综合后台权限检查结果: " + hasPermission +
                        " (电池优化:" + batteryOptimized +
                        ", 后台限制:" + (!backgroundRestricted) +
                        ", 应用待机:" + (!isAppStandby) + ")");

                // 为了确保MQTT推送正常工作，可以设置更严格的条件
                // 如果你希望更容易触发弹窗，可以取消下面这行的注释
                // hasPermission = false;

                return hasPermission;
            } catch (Exception e) {
                LogUtils.eTag(TAG, "检查后台运行权限失败", e);
                return false;
            }
        }
        LogUtils.dTag(TAG, "hasBackgroundRunPermission: true (Android 6.0以下)");
        return true;
    }

    /**
     * 请求后台运行权限
     */
    public void requestBackgroundRunPermission() {
        LogUtils.iTag(TAG, "开始设置后台运行权限");

        if (hasBackgroundRunPermission()) {
            LogUtils.dTag(TAG, "后台运行权限已授予");
            completePowerPermissionSetup();
            return;
        }

        // 显示后台运行权限设置对话框
        showBackgroundRunPermissionDialog();
    }

    /**
     * 显示后台运行权限设置对话框
     */
    private void showBackgroundRunPermissionDialog() {
        new AlertDialog.Builder(activity)
                .setTitle(activity.getString(R.string.dialog_background_run_title))
                .setMessage(activity.getString(R.string.dialog_background_run_message))
                .setPositiveButton(activity.getString(R.string.dialog_background_run_positive), (dialog, which) -> openBackgroundRunSettings())
                .setNegativeButton(activity.getString(R.string.dialog_background_run_negative), (dialog, which) -> completePowerPermissionSetup())
                .setCancelable(false)
                .show();
    }

    /**
     * 打开后台运行设置页面
     */
    private void openBackgroundRunSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            activity.startActivity(intent);
            ToastUtils.showShort(activity.getString(R.string.toast_background_run_app_info));
        } catch (Exception e) {
            LogUtils.eTag(TAG, "打开应用详情页面失败", e);
            ToastUtils.showShort(activity.getString(R.string.toast_background_run_manual));
        }

        // 完成设置流程
        completePowerPermissionSetup();
    }

    /**
     * 完成电源管理权限设置
     */
    private void completePowerPermissionSetup() {
        LogUtils.iTag(TAG, "电源管理权限设置流程完成");

        // 重新检查权限状态
        List<String> stillDenied = checkAllPowerPermissions();


        // 显示设置结果
        if (stillDenied.isEmpty()) {
            ToastUtils.showShort(activity.getString(R.string.toast_power_management_complete));
            LogUtils.iTag(TAG, "所有电源管理权限已授予");
        } else {
            ToastUtils.showShort(activity.getString(R.string.toast_partial_permissions_manual));
            LogUtils.wTag(TAG, "仍有权限未授予: " + stillDenied);
        }
    }

    /**
     * 自动检测后台权限，未授予时弹窗确认后跳转设置页
     * 添加状态记录，设置过一次就不再弹窗
     */
    public void checkAndAutoOpenBackgroundPermissionSettings() {
        LogUtils.dTag(TAG, "开始检查后台权限...");

        // 首先检查用户是否已经设置过后台权限
        if (isBackgroundPermissionDialogCompleted()) {
            LogUtils.dTag(TAG, "用户已设置过后台权限，跳过弹窗");
            return;
        }

        boolean hasPermission = hasBackgroundRunPermission();
        LogUtils.dTag(TAG, "后台权限检查结果: " + hasPermission);

        if (!hasPermission) {
            LogUtils.dTag(TAG, "后台权限未授予，弹出设置对话框");
            showBackgroundPermissionDialogWithCompletion();
        } else {
            LogUtils.dTag(TAG, "后台权限已授予，不需要弹窗");
        }
    }

    // ==================== 后台权限对话框状态管理 ====================

    /**
     * 检查后台权限对话框是否已设置完成
     */
    private boolean isBackgroundPermissionDialogCompleted() {
        try {
            SharedPreferences prefs = activity.getSharedPreferences("permission_settings", Context.MODE_PRIVATE);
            boolean completed = prefs.getBoolean("background_permission_dialog_completed", false);
            long completedTime = prefs.getLong("background_permission_dialog_completed_time", 0);

            // 如果超过30天，重新提醒一次（防止系统更新后设置失效）
            long thirtyDaysInMillis = 30 * 24 * 60 * 60 * 1000L;
            boolean isExpired = (System.currentTimeMillis() - completedTime) > thirtyDaysInMillis;

            if (completed && isExpired) {
                LogUtils.dTag(TAG, "后台权限对话框设置已过期（超过30天），需要重新检查");
                return false;
            }

            LogUtils.dTag(TAG, "后台权限对话框设置状态: " + completed);
            return completed;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查后台权限对话框设置状态失败", e);
            return false;
        }
    }

    /**
     * 保存后台权限对话框设置完成状态
     */
    private void saveBackgroundPermissionDialogCompleted() {
        try {
            SharedPreferences prefs = activity.getSharedPreferences("permission_settings", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putBoolean("background_permission_dialog_completed", true);
            editor.putLong("background_permission_dialog_completed_time", System.currentTimeMillis());
            editor.apply();
            LogUtils.iTag(TAG, "后台权限对话框设置完成状态已保存");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "保存后台权限对话框设置状态失败", e);
        }
    }

    /**
     * 显示带完成状态记录的后台权限设置对话框
     */
    private void showBackgroundPermissionDialogWithCompletion() {
        new AlertDialog.Builder(activity)
                .setTitle(R.string.dialog_power_management_settings_title)
                .setMessage(activity.getString(R.string.dialog_background_run_settings_message))
                .setPositiveButton(activity.getString(R.string.dialog_power_management_settings_positive_button), (dialog, which) -> {
                    boolean jumped = false;
                    try {
                        String brand = Build.MANUFACTURER.toLowerCase();
                        // 小米/Redmi
                        if (brand.contains("xiaomi") || brand.contains("redmi")) {
                            Intent intent = new Intent();
                            intent.setComponent(new ComponentName("com.miui.powerkeeper", "com.miui.powerkeeper.ui.HiddenAppsConfigActivity"));
                            intent.putExtra("package_name", activity.getPackageName());
                            intent.putExtra("package_label", activity.getString(R.string.app_name));
                            if (intent.resolveActivity(activity.getPackageManager()) != null) {
                                activity.startActivity(intent);
                                jumped = true;
                            }
                        }
                        // 华为/荣耀
                        else if (brand.contains("huawei") || brand.contains("honor")) {
                            Intent intent = new Intent();
                            intent.setComponent(new ComponentName("com.huawei.systemmanager", "com.huawei.systemmanager.optimize.process.ProtectActivity"));
                            intent.putExtra("packageName", activity.getPackageName());
                            if (intent.resolveActivity(activity.getPackageManager()) != null) {
                                activity.startActivity(intent);
                                jumped = true;
                            }
                        }
                        // 三星
                        else if (brand.contains("samsung")) {
                            Intent intent = new Intent();
                            intent.setComponent(new ComponentName("com.samsung.android.sm", "com.samsung.android.sm.ui.battery.BatteryActivity"));
                            if (intent.resolveActivity(activity.getPackageManager()) != null) {
                                activity.startActivity(intent);
                                jumped = true;
                            }
                        }
                        // 其他品牌可继续补充...
                    } catch (Exception e) {
                        LogUtils.eTag(TAG, "品牌私有电池详情跳转失败", e);
                    }
                    if (!jumped) {
                        try {
                            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                            intent.setData(Uri.parse("package:" + activity.getPackageName()));
                            activity.startActivity(intent);
                            ToastUtils.showShort(activity.getString(R.string.toast_set_all_power_permissions_hint));
                        } catch (Exception e) {
                            LogUtils.eTag(TAG, "跳转应用详情页失败", e);
                            ToastUtils.showShort(activity.getString(R.string.toast_manual_background_run_hint));
                        }
                    }
                    // 延迟显示完成确认对话框
                    showBackgroundPermissionCompletionDialog();
                })
                .setNegativeButton(activity.getString(R.string.dialog_power_management_settings_negative_button), null)
                .setCancelable(false)
                .show();
    }

    /**
     * 显示后台权限设置完成确认对话框
     */
    private void showBackgroundPermissionCompletionDialog() {
        // 延迟5秒显示，给用户时间去设置
        new Handler(Looper.getMainLooper()).postDelayed(() ->
                new AlertDialog.Builder(activity)
                        .setTitle(activity.getString(R.string.dialog_background_permission_completion_title))
                        .setMessage(activity.getString(R.string.dialog_background_permission_completion_message))
                        .setPositiveButton(activity.getString(R.string.dialog_background_permission_completion_positive), (dialog, which) -> {
                            // 记录用户已设置完成
                            saveBackgroundPermissionDialogCompleted();
                            ToastUtils.showShort(activity.getString(R.string.toast_background_permission_completion));
                            LogUtils.iTag(TAG, "用户确认已完成启动管理权限设置");
                        })
                        .setNegativeButton(activity.getString(R.string.dialog_background_permission_completion_negative), null)
                        .setCancelable(true)
                        .show(), 5000);
    }

}