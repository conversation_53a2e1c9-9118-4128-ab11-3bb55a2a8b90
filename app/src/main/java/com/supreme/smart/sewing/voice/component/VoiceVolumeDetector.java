package com.supreme.smart.sewing.voice.component;

import com.blankj.utilcode.util.LogUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 音量和说话状态检测工具类
 * 提供统一的音量监控和说话状态判断逻辑
 */
public class VoiceVolumeDetector {

    private static final String TAG = "VoiceVolumeDetector";

    // 音量历史记录，用于平滑处理
    private final Object volumeLock = new Object();
    private final List<Integer> volumeHistory = new ArrayList<>();
    private final int historySize = 5;

    // 说话状态判断
    private boolean isSpeaking = false;
    private int currentVolume = 0;

    // 音量阈值，超过此值认为在说话
    private static final int SPEAKING_VOLUME_THRESHOLD = 1;

    public VoiceVolumeDetector() {
        // 初始化音量历史
        synchronized (volumeLock) {
            for (int i = 0; i < historySize; i++) {
                volumeHistory.add(0);
            }
        }
    }

    /**
     * 更新音量数据并判断说话状态
     *
     * @param volume 音量值 (0-100)
     */
    public void updateVolume(int volume) {
        this.currentVolume = Math.max(0, Math.min(100, volume));

        // 更新音量历史
        synchronized (volumeLock) {
            volumeHistory.add(currentVolume);
            if (volumeHistory.size() > historySize) {
                volumeHistory.remove(0);
            }
        }

        // 判断是否在说话
        boolean wasSpeaking = isSpeaking;
        isSpeaking = currentVolume > SPEAKING_VOLUME_THRESHOLD;

        // 添加调试信息
        if (isSpeaking != wasSpeaking) {
            LogUtils.dTag(TAG, "说话状态改变: " + wasSpeaking + " -> " + isSpeaking + ", 音量=" + currentVolume);
        }
    }

    /**
     * 是否在说话
     */
    public boolean isSpeaking() {
        return isSpeaking;
    }

    /**
     * 获取平滑的音量因子 (0.0-1.0)
     */
    public float getSmoothedVolumeFactor() {
        synchronized (volumeLock) {
            float sum = 0f;
            for (int volume : volumeHistory) {
                sum += volume;
            }
            return !volumeHistory.isEmpty() ? (sum / volumeHistory.size()) / 100f : 0f;
        }
    }

    /**
     * 重置检测器状态
     */
    public void reset() {
        currentVolume = 0;
        isSpeaking = false;
        synchronized (volumeLock) {
            volumeHistory.clear();
            for (int i = 0; i < historySize; i++) {
                volumeHistory.add(0);
            }
        }
        LogUtils.dTag(TAG, "音量检测器已重置");
    }

    /**
     * 检查是否为低音量说话（说话但音量过低）
     */
    public boolean isLowVolumeSpeaking(int lowVolumeThreshold) {
        return isSpeaking && currentVolume < lowVolumeThreshold;
    }
} 