package com.supreme.smart.sewing.database.beans.machine;


import com.supreme.smart.sewing.utils.UUIDUtils;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

/**
 * 设备读数记录（一台设备一条记录）
 * 包括： 件数读数、剪线次数读数、抬压脚次数读数、针数读数
 */
public class MachineCounter extends RealmObject implements java.io.Serializable {

    /**
     * 主键
     */
    @PrimaryKey
    private String id;

    /**
     * 机器ID
     */
    private Machine machine;

    /**
     * 件数读数
     */
    private Long pieceCounterReading;

    /**
     * 剪线次数读数
     */
    private Long cutCounterReading;

    /**
     * 抬压脚次数读数
     */
    private Long raiseCounterReading;

    /**
     * 针数读数
     */
    private Long needleCounterReading;


    public MachineCounter() {
        this.id = UUIDUtils.getId();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;

    }

    public Machine getMachine() {
        return machine;
    }

    public void setMachine(Machine machine) {
        this.machine = machine;
    }

    public Long getPieceCounterReading() {
        return pieceCounterReading;
    }

    public void setPieceCounterReading(Long pieceCounterReading) {
        this.pieceCounterReading = pieceCounterReading;
    }

    public Long getCutCounterReading() {
        return cutCounterReading;
    }

    public void setCutCounterReading(Long cutCounterReading) {
        this.cutCounterReading = cutCounterReading;
    }

    public Long getRaiseCounterReading() {
        return raiseCounterReading;
    }

    public void setRaiseCounterReading(Long raiseCounterReading) {
        this.raiseCounterReading = raiseCounterReading;
    }

    public Long getNeedleCounterReading() {
        return needleCounterReading;
    }

    public void setNeedleCounterReading(Long needleCounterReading) {
        this.needleCounterReading = needleCounterReading;
    }
}
