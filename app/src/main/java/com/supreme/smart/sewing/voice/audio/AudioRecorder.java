package com.supreme.smart.sewing.voice.audio;

import android.Manifest;
import android.annotation.SuppressLint;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.RequiresPermission;

import com.blankj.utilcode.util.LogUtils;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 音频录音器
 */
public class AudioRecorder {

    private static final String TAG = "SimpleAudioRecorder";
    
    private static final int SAMPLE_RATE_IN_HZ = 16000;
    private static final int CHANNEL_CONFIGURATION = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    
    private AudioRecord audioRecord;
    private RecorderCallback recordCallback;
    private final AtomicBoolean isRecording = new AtomicBoolean(false);
    private Thread recordingThread;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private static volatile AudioRecorder instance;

    private AudioRecorder() {
    }

    public static AudioRecorder getInstance() {
        if (instance == null) {
            synchronized (AudioRecorder.class) {
                if (instance == null) {
                    instance = new AudioRecorder();
                }
            }
        }
        return instance;
    }

    @SuppressLint("MissingPermission")
    public void init() {
        try {
            if (audioRecord != null) {
                audioRecord.release();
                audioRecord = null;
            }

            // 使用固定的小缓冲区来减少延迟
            int bufferSize = 320; // 10ms at 16kHz
            int minBufferSize = AudioRecord.getMinBufferSize(
                    SAMPLE_RATE_IN_HZ,
                    CHANNEL_CONFIGURATION,
                    AUDIO_FORMAT
            );
            
            if (minBufferSize <= 0) {
                LogUtils.eTag(TAG, "无法获取有效的缓冲区大小");
                return;
            }
            
            // 确保我们的缓冲区至少等于系统要求的最小值
            if (bufferSize < minBufferSize) {
                bufferSize = minBufferSize;
            }

            audioRecord = new AudioRecord(
                    MediaRecorder.AudioSource.MIC,
                    SAMPLE_RATE_IN_HZ,
                    CHANNEL_CONFIGURATION,
                    AUDIO_FORMAT,
                    bufferSize * 4 // 使用较大的内部缓冲区但读取小块数据
            );

            if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                LogUtils.eTag(TAG, "AudioRecord初始化失败");
                audioRecord = null;
            } else {
                LogUtils.dTag(TAG, "AudioRecord初始化成功，缓冲区大小: " + bufferSize);
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "初始化AudioRecord时出错", e);
            audioRecord = null;
        }
    }

    public void setRecorderCallback(RecorderCallback callback) {
        this.recordCallback = callback;
    }

    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    public void startRecording() {
        if (isRecording.get()) {
            LogUtils.wTag(TAG, "录音已在进行中");
            return;
        }

        // 如果AudioRecord已被释放，重新初始化
        if (audioRecord == null) {
            LogUtils.dTag(TAG, "AudioRecord需要重新初始化");
            init();
        }

        if (audioRecord == null) {
            LogUtils.eTag(TAG, "AudioRecord初始化失败，无法开始录音");
            return;
        }

        try {
            LogUtils.iTag(TAG, "准备开始录音，AudioRecord状态: " + audioRecord.getState());
            audioRecord.startRecording();
            LogUtils.iTag(TAG, "AudioRecord.startRecording()调用完成，录音状态: " + audioRecord.getRecordingState());
            isRecording.set(true);

            if (recordCallback != null) {
                LogUtils.dTag(TAG, "回调onStartRecord");
                mainHandler.post(() -> recordCallback.onStartRecord());
            } else {
                LogUtils.wTag(TAG, "recordCallback为null，无法回调开始录音");
            }

            recordingThread = new Thread(this::recordingLoop, "SimpleRecordThread");
            recordingThread.start();

            LogUtils.dTag(TAG, "录音线程已启动");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "启动录音失败", e);
            isRecording.set(false);
            // 如果启动失败，尝试重新初始化
            init();
        }
    }

    public void stopRecording() {
        if (!isRecording.get()) {
            LogUtils.wTag(TAG, "录音未在进行中");
            return;
        }
        
        isRecording.set(false);
        
        if (recordingThread != null && recordingThread.isAlive()) {
            try {
                recordingThread.join(1000); // 等待1秒
            } catch (InterruptedException e) {
                LogUtils.wTag(TAG, "等待录音线程结束时被中断", e);
            }
        }

        if (audioRecord != null) {
            try {
                audioRecord.stop();
                // 不释放AudioRecord，保留以供下次使用
                LogUtils.dTag(TAG, "停止录音，保留AudioRecord实例");
            } catch (Exception e) {
                LogUtils.eTag(TAG, "停止录音时出错", e);
                // 如果停止失败，释放并重置
                try {
                    audioRecord.release();
                } catch (Exception ex) {
                    LogUtils.eTag(TAG, "释放AudioRecord时出错", ex);
                }
                audioRecord = null;
            }
        }

        if (recordCallback != null) {
            mainHandler.post(() -> recordCallback.onStopRecord(null));
        }

        LogUtils.dTag(TAG, "停止录音");
    }

    private void recordingLoop() {
        if (audioRecord == null) {
            LogUtils.eTag(TAG, "录音循环中AudioRecord为null");
            return;
        }

        // 使用更小的读取缓冲区来提高响应速度
        byte[] buffer = new byte[320]; // 固定320字节，约10ms的音频数据
        
        LogUtils.iTag(TAG, "录音循环开始，AudioRecord状态: " + audioRecord.getRecordingState());
        
        while (isRecording.get()) {
            try {
                int readSize = audioRecord.read(buffer, 0, buffer.length);
                
                if (readSize > 0) {
                    if (recordCallback != null) {
                        // 计算音量
                        int volume = calculateVolume(buffer, readSize);
                        LogUtils.dTag(TAG, "录音循环: readSize=" + readSize + ", volume=" + volume);
                        recordCallback.onRecordProgress(buffer, readSize, volume);
                    } else {
                        LogUtils.wTag(TAG, "recordCallback为null，无法回调音频数据");
                    }
                } else if (readSize < 0) {
                    LogUtils.eTag(TAG, "读取音频数据错误: " + readSize);
                    break;
                } else {
                    LogUtils.wTag(TAG, "读取到0字节音频数据");
                }
                
                // 稍微休息一下，避免CPU占用过高，但不要太长以保持响应性
                Thread.sleep(5);
            } catch (Exception e) {
                LogUtils.eTag(TAG, "录音循环中发生错误", e);
                break;
            }
        }
        
        LogUtils.dTag(TAG, "录音循环结束");
    }

    private int calculateVolume(byte[] buffer, int readSize) {
        if (readSize <= 0) return 0;

        // 方式1：16位PCM样本
        long sum16 = 0;
        int max16 = 0;
        for (int i = 0; i < readSize - 1; i += 2) {
            // 小端序：低字节在前
            short sample = (short) ((buffer[i + 1] << 8) | (buffer[i] & 0xFF));
            int abs = Math.abs(sample);
            sum16 += abs;
            max16 = Math.max(max16, abs);
        }
        
        // 方式2：字节级别的音量（简单方式）
        long sumByte = 0;
        int maxByte = 0;
        for (int i = 0; i < readSize; i++) {
            int abs = Math.abs(buffer[i]);
            sumByte += abs;
            maxByte = Math.max(maxByte, abs);
        }
        
        // 计算两种方式的结果
        int sampleCount16 = readSize / 2;
        int avg16 = sampleCount16 > 0 ? (int)(sum16 / sampleCount16) : 0;
        int avgByte = (int)(sumByte / readSize);
        
        // 使用16位样本的结果，但如果值太小就使用字节级别的结果
        int averageAmplitude = avg16;
        int maxAmp = max16;
        
        // 如果16位结果太小，可能是字节序问题，使用字节级别结果
        if (avg16 < 50 && avgByte > 10) {
            averageAmplitude = avgByte * 50; // 放大字节级结果
            maxAmp = maxByte * 50;
            LogUtils.dTag(TAG, "使用字节级音量计算，avgByte=" + avgByte + ", 放大后=" + averageAmplitude);
        }
        
        // 使用平均值和最大值的组合来计算音量
        int combinedAmplitude = (averageAmplitude + maxAmp) / 2;
        
        // 映射到0-100的范围，使用更低的阈值
        int volume;
        if (combinedAmplitude < 50) {
            volume = 0; // 噪声过滤
        } else if (combinedAmplitude < 200) {
            volume = (combinedAmplitude - 50) * 20 / 150; // 1-20
        } else if (combinedAmplitude < 1000) {
            volume = 20 + (combinedAmplitude - 200) * 30 / 800; // 20-50
        } else if (combinedAmplitude < 4000) {
            volume = 50 + (combinedAmplitude - 1000) * 30 / 3000; // 50-80
        } else {
            volume = 80 + Math.min(20, (combinedAmplitude - 4000) * 20 / 4000); // 80-100
        }
        
        volume = Math.max(0, Math.min(100, volume));
        
        // 详细日志
        LogUtils.iTag(TAG, "音量计算: readSize=" + readSize + 
              "\n  16位: avg=" + avg16 + ", max=" + max16 + 
              "\n  字节: avg=" + avgByte + ", max=" + maxByte + 
              "\n  最终: combined=" + combinedAmplitude + ", volume=" + volume);
        
        // 显示原始数据样本
        if (readSize >= 8) {
            LogUtils.dTag(TAG, "原始字节: " + 
                  String.format("%02X %02X %02X %02X %02X %02X %02X %02X", 
                  buffer[0] & 0xFF, buffer[1] & 0xFF, buffer[2] & 0xFF, buffer[3] & 0xFF,
                  buffer[4] & 0xFF, buffer[5] & 0xFF, buffer[6] & 0xFF, buffer[7] & 0xFF));
        }
        
        return volume;
    }

    public boolean isRecording() {
        return isRecording.get();
    }

    /**
     * 销毁录音器，释放所有资源
     */
    public void destroy() {
        stopRecording();
        
        if (audioRecord != null) {
            try {
                audioRecord.release();
                audioRecord = null;
                LogUtils.dTag(TAG, "AudioRecord资源已释放");
            } catch (Exception e) {
                LogUtils.eTag(TAG, "释放AudioRecord资源时出错", e);
            }
        }
    }
} 
