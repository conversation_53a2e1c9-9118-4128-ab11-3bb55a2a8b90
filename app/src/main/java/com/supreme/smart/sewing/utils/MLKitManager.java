package com.supreme.smart.sewing.utils;

import com.blankj.utilcode.util.LogUtils;
import com.google.mlkit.vision.face.FaceDetection;
import com.google.mlkit.vision.face.FaceDetector;
import com.google.mlkit.vision.face.FaceDetectorOptions;

/**
 * MLKit管理器
 * 提供单例模式的人脸检测器，确保全局只有一个实例，提高性能
 */
public class MLKitManager {
    private static final String TAG = "MLKitManager";
    private static volatile MLKitManager instance;
    private FaceDetector faceDetector;
    
    private MLKitManager() {
        initializeFaceDetector();
    }
    
    /**
     * 获取MLKitManager单例实例
     */
    public static MLKitManager getInstance() {
        if (instance == null) {
            synchronized (MLKitManager.class) {
                if (instance == null) {
                    instance = new MLKitManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化人脸检测器
     */
    private void initializeFaceDetector() {
        try {
            LogUtils.iTag(TAG, "初始化人脸检测器");
            
            FaceDetectorOptions options = new FaceDetectorOptions.Builder()
                    .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
                    .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
                    .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE)
                    .setMinFaceSize(0.15f)
                    .enableTracking()
                    .build();
            
            faceDetector = FaceDetection.getClient(options);
            LogUtils.iTag(TAG, "人脸检测器初始化完成");
            
        } catch (Exception e) {
            LogUtils.eTag(TAG, "初始化人脸检测器失败", e);
        }
    }
    
    /**
     * 获取人脸检测器实例
     */
    public FaceDetector getFaceDetector() {
        if (faceDetector == null) {
            initializeFaceDetector();
        }
        return faceDetector;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (faceDetector != null) {
            try {
                faceDetector.close();
                LogUtils.iTag(TAG, "人脸检测器资源已释放");
            } catch (Exception e) {
                LogUtils.eTag(TAG, "释放人脸检测器资源失败", e);
            }
            faceDetector = null;
        }
    }
}