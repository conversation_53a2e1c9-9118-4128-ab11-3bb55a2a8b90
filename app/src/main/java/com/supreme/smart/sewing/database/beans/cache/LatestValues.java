package com.supreme.smart.sewing.database.beans.cache;



import com.supreme.smart.sewing.utils.CommonUtils;
import com.supreme.smart.sewing.utils.UUIDUtils;

import java.io.Serializable;
import java.util.Date;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class LatestValues extends RealmObject implements Serializable {
    /**
     * 主键
     */
    @PrimaryKey
    private String id;

    private String key;

    private Date createTime;

    private String value;

    private Integer timeoutSecond;

    public LatestValues() {
        this.id = UUIDUtils.getId();
        this.createTime = CommonUtils.getCurrentTime();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public Integer getTimeoutSecond() {
        return timeoutSecond;
    }

    public void setTimeoutSecond(Integer timeoutSecond) {
        this.timeoutSecond = timeoutSecond;
    }
}
