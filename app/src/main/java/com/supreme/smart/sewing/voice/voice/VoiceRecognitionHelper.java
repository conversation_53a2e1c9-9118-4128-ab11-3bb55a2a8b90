package com.supreme.smart.sewing.voice.voice;

import android.annotation.SuppressLint;
import android.util.Log;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;
import com.iflytek.aikit.core.AiAudio;
import com.iflytek.aikit.core.AiHandle;
import com.iflytek.aikit.core.AiHelper;
import com.iflytek.aikit.core.AiListener;
import com.iflytek.aikit.core.AiRequest;
import com.iflytek.aikit.core.AiResponse;
import com.iflytek.aikit.core.AiStatus;
import com.iflytek.aikit.core.DataStatus;
import com.supreme.smart.sewing.voice.ability.AbilityCallback;
import com.supreme.smart.sewing.voice.ability.AbilityConstant;
import com.supreme.smart.sewing.voice.audio.AudioRecorder;
import com.supreme.smart.sewing.voice.audio.RecorderCallback;

import java.io.File;
import java.nio.charset.Charset;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 语音识别帮助类
 */
public class VoiceRecognitionHelper implements AiListener, RecorderCallback {

    private static final String TAG = "VoiceRecognition";

    private final AbilityCallback callback;
    private AudioRecorder recorder;
    private AiHandle aiHandle;
    private final AtomicBoolean audioBegin = new AtomicBoolean(false);
    private final AtomicBoolean isRecognizing = new AtomicBoolean(false);
    private final Object lockArray = new Object();
    
    // 添加音频数据回调接口
    public interface AudioDataCallback {
        void onAudioData(byte[] data, int sampleSize, int volume);
    }
    
    private AudioDataCallback audioDataCallback;

    public VoiceRecognitionHelper(AbilityCallback callback) {
        this.callback = callback;
        init();
    }

    private void init() {
        LogUtils.dTag(TAG, "初始化VoiceRecognitionHelper");
        
        // 注册能力回调
        AiHelper.getInst().registerListener(AbilityConstant.ED_ENCN_ID, this);
        LogUtils.dTag(TAG, "已注册AiHelper监听器");
        
        // 初始化录音器
        recorder = AudioRecorder.getInstance();
        recorder.init();
        recorder.setRecorderCallback(this);
        LogUtils.dTag(TAG, "VoiceRecognitionHelper初始化完成");
    }

    /**
     * 设置音频数据回调
     */
    public void setAudioDataCallback(AudioDataCallback audioDataCallback) {
        this.audioDataCallback = audioDataCallback;
    }

    /**
     * 开始语音识别
     */
    @SuppressLint("MissingPermission")
    public void startRecording() {
        if (isRecognizing.get()) {
            LogUtils.wTag(TAG, "语音识别已在进行中");
            return;
        }
        
        if (initParams()) {
            return;
        }
        
        isRecognizing.set(true);
        LogUtils.dTag(TAG, "准备开始录音");
        recorder.startRecording();
        
        // 在主线程中回调
        if (callback != null) {
            LogUtils.dTag(TAG, "调用onAbilityBegin回调");
            callback.onAbilityBegin();
        }
    }

    /**
     * 停止语音识别
     */
    public void stopRecording() {
        if (!isRecognizing.get()) {
            LogUtils.wTag(TAG, "语音识别未在进行中，跳过停止");
            return;
        }
        
        LogUtils.dTag(TAG, "停止语音识别开始");
        isRecognizing.set(false); // 立即设置状态，防止重复调用
        
        if (recorder != null && recorder.isRecording()) {
            LogUtils.dTag(TAG, "停止录音器");
            recorder.stopRecording();
        } else {
            LogUtils.wTag(TAG, "录音器未在运行中，跳过停止录音");
        }
        
        // 发送结束帧
        if (aiHandle != null) {
            LogUtils.dTag(TAG, "发送结束帧");
            writeData(new byte[0], AiStatus.END);
        }
        
        endAiHandle();
        LogUtils.dTag(TAG, "停止语音识别完成");
    }

    /**
     * 初始化参数
     */
    private boolean initParams() {
        // 能力逆初始化
        AiHelper.getInst().engineUnInit(AbilityConstant.ED_ENCN_ID);
        
        int ret = AiHelper.getInst().engineInit(AbilityConstant.ED_ENCN_ID);
        if (ret != AbilityConstant.ABILITY_SUCCESS_CODE) {
            LogUtils.eTag(TAG, "引擎初始化失败: " + ret);
            if (callback != null) {
                callback.onAbilityError(ret, new Throwable("引擎初始化失败：" + ret));
            }
            return true;
        } else {
            LogUtils.dTag(TAG, "引擎初始化成功");
        }

        AiRequest.Builder paramBuilder = AiRequest.builder();
        paramBuilder.param("lmLoad", true);
        paramBuilder.param("vadLoad", true);
        paramBuilder.param("puncLoad", true);
        paramBuilder.param("numLoad", true);
        paramBuilder.param("postprocOn", true);
        paramBuilder.param("lmOn", true);
        paramBuilder.param("vadOn", true);
        paramBuilder.param("vadLinkOn", false);
        paramBuilder.param("vadNeed", true);
        paramBuilder.param("vadThreshold", 0.1332);
        paramBuilder.param("vadEnergyThreshold", 9);

        aiHandle = AiHelper.getInst().start(AbilityConstant.ED_ENCN_ID, paramBuilder.build(), null);
        if (aiHandle == null || aiHandle.getCode() != AbilityConstant.ABILITY_SUCCESS_CODE) {
            LogUtils.wTag(TAG, "开始语音识别失败: " + (aiHandle != null ? aiHandle.getCode() : "null"));
            if (callback != null) {
                callback.onAbilityError(
                    aiHandle != null ? aiHandle.getCode() : AbilityConstant.ABILITY_CUSTOM_UNKNOWN_CODE,
                    new Throwable("开始语音识别失败")
                );
            }
            return true;
        }
        return false;
    }

    /**
     * 写入音频数据
     */
    private void writeData(byte[] audio, AiStatus status) {
        if (aiHandle == null) {
            LogUtils.wTag(TAG, "aiHandle为null，无法写入音频数据");
            return;
        }
        
        synchronized (lockArray) {
            AiRequest.Builder dataBuilder = AiRequest.builder();
            AiAudio.Holder holder = AiAudio.get("PCM").data(audio);
            holder.status(status);
            dataBuilder.payload(holder.valid());
            
            int ret = AiHelper.getInst().write(dataBuilder.build(), aiHandle);
            if (ret != AbilityConstant.ABILITY_SUCCESS_CODE) {
                LogUtils.eTag(TAG, "写入音频数据失败: " + ret);
                endAiHandle();
            } else {
                Log.v(TAG, "音频数据写入成功，状态: " + status);
                ret = AiHelper.getInst().read(AbilityConstant.ED_ENCN_ID, aiHandle);
                if (ret != AbilityConstant.ABILITY_SUCCESS_CODE) {
                    LogUtils.wTag(TAG, "读取识别结果失败: " + ret);
                    endAiHandle();
                } else {
                    Log.v(TAG, "读取识别结果成功");
                }
            }
        }
    }

    /**
     * 手动结束会话
     */
    private void endAiHandle() {
        if (aiHandle == null) {
            return;
        }
        
        int ret = AiHelper.getInst().end(aiHandle);
        if (callback != null) {
            if (ret == AbilityConstant.ABILITY_SUCCESS_CODE) {
                callback.onAbilityEnd();
            } else {
                callback.onAbilityError(ret, new Throwable("结束会话失败"));
            }
        }
        aiHandle = null;
        
        // 重置识别状态，允许下次重新开始识别
        isRecognizing.set(false);
        LogUtils.dTag(TAG, "语音识别会话结束，状态已重置");
    }

    // AudioRecorder 回调方法
    @Override
    public void onStartRecord() {
        LogUtils.iTag(TAG, "开始录音");
        audioBegin.set(true);
    }

    @Override
    public void onPauseRecord() {
        LogUtils.iTag(TAG, "暂停录音");
    }

    @Override
    public void onResumeRecord() {
        LogUtils.iTag(TAG, "恢复录音");
        audioBegin.set(true);
    }

    @Override
    public void onRecordProgress(@NonNull byte[] data, int sampleSize, int volume) {
        if (sampleSize > 0) {
            // 改为INFO级别，确保日志可见
            LogUtils.iTag(TAG, "接收到音频数据: size=" + sampleSize + ", volume=" + volume);
            
            // 将音量信息传给回调 - 在主线程中执行UI更新
            if (callback != null) {
                LogUtils.dTag(TAG, "传递音量到Activity: " + volume);
                // 使用统一的回调接口，不依赖具体实现类
                callback.onVolumeUpdate(volume);
            } else {
                LogUtils.wTag(TAG, "callback为空，无法传递音量");
            }
            
            // 传递音频数据给音频数据回调
            if (audioDataCallback != null) {
                audioDataCallback.onAudioData(data, sampleSize, volume);
            }
            
            AiStatus status = AiStatus.CONTINUE;
            if (audioBegin.get()) {
                status = AiStatus.BEGIN;
                audioBegin.set(false);
                LogUtils.dTag(TAG, "发送首帧音频数据");
            }
            writeData(data, status);
        } else {
            LogUtils.wTag(TAG, "接收到空的音频数据，sampleSize=" + sampleSize);
        }
    }

    @Override
    public void onStopRecord(File output) {
        LogUtils.iTag(TAG, "停止录音: " + (output != null ? output.getAbsolutePath() : "null"));
    }

    // AiListener 回调方法
    @Override
    public void onResult(int handleID, List<AiResponse> responseData, Object usrContext) {
        if (responseData == null || responseData.isEmpty()) {
            LogUtils.wTag(TAG, "识别结果为空");
            return;
        }

        LogUtils.iTag(TAG, "识别结果回调: handleID=" + handleID + ", 结果数量=" + responseData.size());

        for (AiResponse item : responseData) {
            String tempKey = item.getKey();
            String tempValue = new String(item.getValue(), Charset.forName("GBK"));
            
            LogUtils.dTag(TAG, "结果项: key=" + tempKey + ", value=" + tempValue);
            
            if (tempKey.contains("pgs")) {
                // 实时识别结果，立即发送
                LogUtils.dTag(TAG, "发送实时识别结果到回调: " + tempValue);
                if (callback != null) {
                    callback.onAbilityResult(tempKey + ": \n" + tempValue);
                }
            } else if (tempKey.contains("plain")) {
                // 最终识别结果
                LogUtils.dTag(TAG, "发送最终识别结果到回调: " + tempValue);
                if (callback != null) {
                    callback.onAbilityResult(tempKey + ": \n" + tempValue);
                }
            }

            if (tempKey.contains("vad")) {
                LogUtils.dTag(TAG, "VAD结果: " + tempValue);
                if (callback != null) {
                    callback.onAbilityResult(tempKey + ": \n" + tempValue);
                }
            }
        }

        if (responseData.get(0).getStatus() == DataStatus.END.getValue()) {
            LogUtils.dTag(TAG, "VAD检测到结束，调用endAiHandle触发onAbilityEnd");
            // VAD结束时，需要调用endAiHandle来触发onAbilityEnd回调
            endAiHandle();
        }
    }

    @Override
    public void onEvent(int handleID, int event, List<AiResponse> eventData, Object usrContext) {
        // 语音识别不会执行该事件回调
    }

    @Override
    public void onError(int handleID, int err, String msg, Object usrContext) {
        String tips = "识别错误: " + msg + ", 错误码: " + err;
        if (callback != null) {
            callback.onAbilityError(err, new Throwable(tips));
        }
        LogUtils.eTag(TAG, tips);
    }

    /**
     * 释放资源
     */
    public void destroy() {
        LogUtils.dTag(TAG, "开始释放VoiceRecognitionHelper资源");
        
        // 停止录音
        stopRecording();
        
        // 引擎反初始化 - 这会清理与该能力相关的所有资源
        try {
            AiHelper.getInst().engineUnInit(AbilityConstant.ED_ENCN_ID);
            LogUtils.dTag(TAG, "已执行引擎反初始化");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "引擎反初始化失败", e);
        }
        
        // 清理录音器
        if (recorder != null) {
            recorder.destroy();
            recorder = null;
            LogUtils.dTag(TAG, "已清理录音器");
        }
        
        // 清理其他资源
        audioDataCallback = null;
        aiHandle = null;
        
        LogUtils.dTag(TAG, "VoiceRecognitionHelper资源释放完成");
    }
} 
