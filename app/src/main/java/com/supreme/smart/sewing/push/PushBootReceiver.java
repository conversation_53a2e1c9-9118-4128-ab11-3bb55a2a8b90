package com.supreme.smart.sewing.push;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.blankj.utilcode.util.LogUtils;

/**
 * 推送服务广播接收器
 * 监听系统事件，自动重启推送服务
 */
public class PushBootReceiver extends BroadcastReceiver {
    
    private static final String TAG = "PushBootReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || intent.getAction() == null) {
            return;
        }
        
        String action = intent.getAction();
        LogUtils.dTag(TAG, "收到广播: " + action);
        
        switch (action) {
            case Intent.ACTION_BOOT_COMPLETED:
                // 开机完成，启动推送服务
                LogUtils.dTag(TAG, "系统开机完成，启动推送服务");
                startPushService(context);
                break;
                
            case Intent.ACTION_MY_PACKAGE_REPLACED:
            case Intent.ACTION_PACKAGE_REPLACED:
                // 应用更新完成，重启推送服务
                LogUtils.dTag(TAG, "应用更新完成，重启推送服务");
                startPushService(context);
                break;
                
            case "android.net.conn.CONNECTIVITY_CHANGE":
                // 网络状态变化，检查并重启推送服务
                LogUtils.dTag(TAG, "网络状态变化，检查推送服务");
                checkAndRestartPushService(context);
                break;
                
            default:
                LogUtils.dTag(TAG, "未处理的广播: " + action);
                break;
        }
    }
    
    /**
     * 启动推送服务
     */
    private void startPushService(Context context) {
        try {
            PushManager.getInstance(context).startPushService();
            LogUtils.dTag(TAG, "推送服务启动成功");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "启动推送服务失败", e);
        }
    }
    
    /**
     * 检查并重启推送服务
     */
    private void checkAndRestartPushService(Context context) {
        // 延迟检查，避免频繁重启
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            try {
                PushManager.getInstance(context).startPushService();
                LogUtils.dTag(TAG, "推送服务检查并重启完成");
            } catch (Exception e) {
                LogUtils.eTag(TAG, "检查并重启推送服务失败", e);
            }
        }, 5000); // 延迟5秒
    }
} 