package com.supreme.smart.sewing.database.module;


import com.supreme.smart.sewing.database.beans.machine.Machine;
import com.supreme.smart.sewing.database.beans.machine.MachineCounter;
import com.supreme.smart.sewing.database.beans.machine.MachineCounterDaily;
import com.supreme.smart.sewing.database.beans.machine.MachineCounterDailySplit;
import com.supreme.smart.sewing.database.beans.sys.SysPowerAction;
import com.supreme.smart.sewing.database.beans.sys.SysUserLogin;

import io.realm.annotations.RealmModule;


@RealmModule(
        classes = {
                SysUserLogin.class,
                SysPowerAction.class,
                Machine.class,
                MachineCounter.class,
                MachineCounterDaily.class,
                MachineCounterDailySplit.class,
        }
)
public class DefaultModule {
}
