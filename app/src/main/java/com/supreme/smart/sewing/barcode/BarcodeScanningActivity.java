
package com.supreme.smart.sewing.barcode;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.widget.ImageView;

import com.google.mlkit.vision.barcode.common.Barcode;
import com.king.app.dialog.AppDialog;
import com.king.app.dialog.AppDialogConfig;
import com.king.camera.scan.AnalyzeResult;
import com.king.camera.scan.CameraScan;
import com.king.mlkit.vision.barcode.BarcodeCameraScanActivity;
import com.supreme.smart.sewing.R;


import java.util.List;

/**
 * 条形码/二维码扫描示例
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jenly</a>
 */
public class BarcodeScanningActivity extends BarcodeCameraScanActivity {

    @Override
    public void initCameraScan(CameraScan<List<Barcode>> cameraScan) {
        super.initCameraScan(cameraScan);
        cameraScan.setPlayBeep(true)
                .setVibrate(true);
    }

    @Override
    public void onScanResultCallback(AnalyzeResult<List<Barcode>> result) {
        // 停止分析
        CameraScan<List<Barcode>> cameraScan = getCameraScan();
        cameraScan.setAnalyzeImage(false);
        StringBuilder buffer = new StringBuilder();

        Bitmap bitmap = drawRectOnBitmap(result.getBitmap(), result.getResult(), buffer);

        AppDialogConfig dialog = new AppDialogConfig(this, R.layout.barcode_result_dialog);
        dialog.setContent(buffer.toString())
                .setOnClickConfirm(v -> {
                    AppDialog.INSTANCE.dismissDialog();
                    cameraScan.setAnalyzeImage(true);
                })
                .setOnClickCancel(v -> {
                    AppDialog.INSTANCE.dismissDialog();
                    finish();
                });

        ImageView imageView = dialog.getView(R.id.ivDialogContent);
        imageView.setImageBitmap(bitmap);
        AppDialog.INSTANCE.showDialog(dialog, false);
    }

    private Bitmap drawRectOnBitmap(Bitmap originalBitmap, List<Barcode> barcodes, StringBuilder buffer) {
        if (originalBitmap == null) {
            return null;
        }

        Bitmap mutableBitmap = originalBitmap.copy(Bitmap.Config.ARGB_8888, true);
        Canvas canvas = new Canvas(mutableBitmap);
        Paint paint = new Paint();
        paint.setStyle(Paint.Style.STROKE);
        paint.setColor(0xFF00FF00); // 绿色
        paint.setStrokeWidth(4);

        for (int i = 0; i < barcodes.size(); i++) {
            Barcode barcode = barcodes.get(i);
            buffer.append("[").append(i).append("] ").append(barcode.getDisplayValue()).append("\n");

            Rect boundingBox = barcode.getBoundingBox();
            if (boundingBox != null) {
                canvas.drawRect(boundingBox, paint);
            }
        }

        return mutableBitmap;
    }
}