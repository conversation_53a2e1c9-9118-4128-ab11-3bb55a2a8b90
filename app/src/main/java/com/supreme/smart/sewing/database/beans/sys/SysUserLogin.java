package com.supreme.smart.sewing.database.beans.sys;



import com.supreme.smart.sewing.utils.UUIDUtils;

import java.io.Serializable;
import java.util.Date;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

/**
 * 用户登录
 */
public class SysUserLogin extends RealmObject implements Serializable {
    /**
     * 主键
     */
    @PrimaryKey
    private String id;
    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 员工
     */
    private String userId;

    /**
     * 退出时间
     */
    private Date logoutTime;

    public SysUserLogin() {
        this.id = UUIDUtils.getId();
        this.loginTime = UUIDUtils.getCreateTime();
    }

    public String getId() {
        return id;
    }


    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getLogoutTime() {
        return logoutTime;
    }

    public void setLogoutTime(Date logoutTime) {
        this.logoutTime = logoutTime;
    }
}
