package com.supreme.smart.sewing.voice.ability;

import android.annotation.SuppressLint;
import android.content.Context;

import com.blankj.utilcode.util.LogUtils;
import com.iflytek.aikit.core.AiHelper;
import com.iflytek.aikit.core.BaseLibrary;
import com.iflytek.aikit.core.ErrType;
import com.supreme.smart.sewing.R;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 讯飞语音初始化辅助类 - Java版本
 */
public class AbilityManager {

    private static final String TAG = "IFlytekAbilityManager";
    
    // 在线授权校验间隔时长，默认为300s，可自定义设置，最短为60s，单位 秒
    private static final int AUTH_INTERVAL = 333;

    private static volatile AbilityManager instance;
    private final ExecutorService executor = Executors.newSingleThreadExecutor();

    private AbilityManager() {
    }

    public static AbilityManager getInstance() {
        if (instance == null) {
            synchronized (AbilityManager.class) {
                if (instance == null) {
                    instance = new AbilityManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化sdk
     * 只需要初始化一次
     */
    public void initializeSdk(Context context) {
        @SuppressLint("SdCardPath")
        BaseLibrary.Params params = BaseLibrary.Params.builder()
                .appId(context.getResources().getString(R.string.appId))
                .apiKey(context.getResources().getString(R.string.apiKey))
                .apiSecret(context.getResources().getString(R.string.apiSecret))
                .workDir("/sdcard/iflytekAikit")
                .iLogMaxCount(1)
                .authInterval(AUTH_INTERVAL)
                .ability(getEngineIds())
                .build();
        
        // 鉴权
        AiHelper.getInst().registerListener((type, code) -> LogUtils.dTag(TAG, "引擎初始化状态: " + (type == ErrType.AUTH && code == 0)));
        
        executor.execute(() -> AiHelper.getInst().init(context, params));
    }

    /**
     * 添加所需的能力引擎id,多个能力用;隔开，如"xxx;xxx"
     */
    private String getEngineIds() {
        return AbilityConstant.ED_ENCN_ID;
    }
} 
