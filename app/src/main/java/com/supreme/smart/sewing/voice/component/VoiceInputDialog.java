package com.supreme.smart.sewing.voice.component;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;

import java.util.Locale;
import java.util.Objects;

/**
 * 语音输入对话框
 */
public class VoiceInputDialog extends Dialog {

    private WaveformView waveformView;
    private TextView tvRealTimeText;
    private LinearLayout cancelHintArea;
    private LinearLayout sendVoiceHintArea;
    private TextView tvBottomHint;
    private TextView tvCancelHint;
    private TextView tvSendVoiceHint;

    // 使用场景类型
    private VoiceUsageType currentUsageType = VoiceUsageType.VOICE_CHAT;

    // 音量监控相关
    private int lowVolumeCount = 0;
    private boolean hasShownLowVolumeHint = false;
    private static final int LOW_VOLUME_THRESHOLD = 10;   // 音量阈值提高到10
    private static final int LOW_VOLUME_COUNT_THRESHOLD = 5;   // 计数阈值降低到5次

    // 公用音量检测器
    private final VoiceVolumeDetector volumeDetector = new VoiceVolumeDetector();
    
    // 触发按钮的位置信息
    private int buttonCenterX = -1;
    private int buttonCenterY = -1;

    public VoiceInputDialog(@NonNull Context context) {
        super(context);
    }
    
    /**
     * 设置触发按钮的位置，用于智能调整对话框位置
     * @param centerX 按钮中心X坐标
     * @param centerY 按钮中心Y坐标
     */
    public void setTriggerButtonPosition(int centerX, int centerY) {
        this.buttonCenterX = centerX;
        this.buttonCenterY = centerY;
        LogUtils.dTag("VoiceInputDialog", "设置触发按钮位置: (" + centerX + ", " + centerY + ")");
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置对话框样式
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setCancelable(false);
        setCanceledOnTouchOutside(false);

        // 设置对话框布局
        @SuppressLint("InflateParams")
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_voice_input, null);
        setContentView(dialogView);

        // 设置对话框大小和位置
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            
            // 获取屏幕方向和尺寸
            android.util.DisplayMetrics displayMetrics = getContext().getResources().getDisplayMetrics();
            int screenWidth = displayMetrics.widthPixels;
            int screenHeight = displayMetrics.heightPixels;
            
            // 判断是否为横屏（宽度大于高度）
            boolean isLandscape = screenWidth > screenHeight;
            
            if (isLandscape) {
                // 横屏时，使用屏幕宽度的50%，避免对话框过宽
                params.width = (int) (screenWidth * 0.5f);
                LogUtils.dTag("VoiceInputDialog", "横屏模式：对话框宽度设置为屏幕宽度的50% = " + params.width + "px");
            } else {
                // 竖屏时，使用屏幕宽度的80%，留有边距
                params.width = (int) (screenWidth * 0.9f);
                LogUtils.dTag("VoiceInputDialog", "竖屏模式：对话框宽度设置为屏幕宽度的80% = " + params.width + "px");
            }
            
            params.height = WindowManager.LayoutParams.WRAP_CONTENT;
            
            // 设置对话框位置：基于按钮位置智能选择
            if (buttonCenterX != -1 && buttonCenterY != -1) {
                // 有按钮位置信息，智能选择对话框位置
                setupDialogPositionBasedOnButton(params, screenHeight, isLandscape);
            } else {
                // 没有按钮位置信息，使用默认位置
                setupDefaultDialogPosition(params, screenHeight, isLandscape);
            }
            
            window.setAttributes(params);

            // 设置背景透明
            window.setBackgroundDrawableResource(android.R.color.transparent);
            
            LogUtils.dTag("VoiceInputDialog", "屏幕尺寸: " + screenWidth + "x" + screenHeight + 
                ", 方向: " + (isLandscape ? "横屏" : "竖屏") + ", 对话框宽度: " + params.width);
        }

        initViews(dialogView);
    }
    
    /**
     * 基于按钮位置智能设置对话框位置
     */
    private void setupDialogPositionBasedOnButton(WindowManager.LayoutParams params,
                                                  int screenHeight, boolean isLandscape) {
        // 计算按钮在屏幕中的相对位置
        float buttonRelativeY = (float) buttonCenterY / screenHeight;
        
        LogUtils.dTag("VoiceInputDialog", "按钮位置分析: 中心(" + buttonCenterX + ", " + buttonCenterY + 
            "), 相对Y位置: " + String.format(Locale.US,"%.2f", buttonRelativeY));
        
        if (buttonRelativeY > 0.6f) {
            // 按钮在屏幕下半部分，对话框显示在上方
            params.gravity = android.view.Gravity.CENTER_HORIZONTAL | android.view.Gravity.TOP;
            if (isLandscape) {
                params.y = (int) (screenHeight * 0.15f); // 横屏：顶部15%
            } else {
                params.y = (int) (screenHeight * 0.2f);  // 竖屏：顶部20%
            }
            LogUtils.dTag("VoiceInputDialog", "按钮在下半部分，对话框显示在上方，Y=" + params.y);
            
        } else if (buttonRelativeY < 0.4f) {
            // 按钮在屏幕上半部分，对话框显示在下方
            params.gravity = android.view.Gravity.CENTER_HORIZONTAL | android.view.Gravity.BOTTOM;
            if (isLandscape) {
                params.y = (int) (screenHeight * 0.15f); // 横屏：底部留15%
            } else {
                params.y = (int) (screenHeight * 0.2f);  // 竖屏：底部留20%
            }
            LogUtils.dTag("VoiceInputDialog", "按钮在上半部分，对话框显示在下方，Y=" + params.y);
            
        } else {
            // 按钮在屏幕中间，根据屏幕方向选择较好的位置
            if (isLandscape) {
                // 横屏时优先显示在左侧或右侧（这里选择上方作为备选）
                params.gravity = android.view.Gravity.CENTER_HORIZONTAL | android.view.Gravity.TOP;
                params.y = (int) (screenHeight * 0.1f);
                LogUtils.dTag("VoiceInputDialog", "按钮在中间，横屏模式对话框显示在顶部");
            } else {
                // 竖屏时显示在上方
                params.gravity = android.view.Gravity.CENTER_HORIZONTAL | android.view.Gravity.TOP;
                params.y = (int) (screenHeight * 0.15f);
                LogUtils.dTag("VoiceInputDialog", "按钮在中间，竖屏模式对话框显示在上方");
            }
        }
    }
    
    /**
     * 设置默认对话框位置（当没有按钮位置信息时）
     */
    private void setupDefaultDialogPosition(WindowManager.LayoutParams params, 
                                          int screenHeight, boolean isLandscape) {
        params.gravity = android.view.Gravity.CENTER_HORIZONTAL | android.view.Gravity.TOP;
        if (isLandscape) {
            params.y = (int) (screenHeight * 0.2f); // 距离顶部20%的位置
            LogUtils.dTag("VoiceInputDialog", "使用默认位置 - 横屏模式：顶部20%");
        } else {
            params.y = (int) (screenHeight * 0.25f); // 距离顶部25%的位置
            LogUtils.dTag("VoiceInputDialog", "使用默认位置 - 竖屏模式：顶部25%");
        }
    }

    private void initViews(View dialogView) {
        waveformView = dialogView.findViewById(R.id.waveformView);
        tvRealTimeText = dialogView.findViewById(R.id.tvRealTimeText);
        cancelHintArea = dialogView.findViewById(R.id.cancelHintArea);
        sendVoiceHintArea = dialogView.findViewById(R.id.sendVoiceHintArea);
        tvBottomHint = dialogView.findViewById(R.id.tvBottomHint);
        tvCancelHint = dialogView.findViewById(R.id.tvCancelHint);
        tvSendVoiceHint = dialogView.findViewById(R.id.tvSendVoiceHint);

        // 更新底部提示文字
        updateBottomHint();
    }

    /**
     * 提示类型枚举
     */
    public enum HintType {
        NONE,           // 不显示提示
        CANCEL,         // 显示取消提示
        SEND_VOICE      // 显示发送语音提示
    }

    /**
     * 显示指定类型的提示
     */
    public void showHint(HintType hintType) {
        // 隐藏所有提示区域
        cancelHintArea.setVisibility(View.GONE);
        sendVoiceHintArea.setVisibility(View.GONE);

        switch (hintType) {
            case CANCEL:
                cancelHintArea.setVisibility(View.VISIBLE);
                tvRealTimeText.setVisibility(View.GONE);
                break;
            case SEND_VOICE:
                sendVoiceHintArea.setVisibility(View.VISIBLE);
                tvRealTimeText.setVisibility(View.GONE);
                break;
            case NONE:
            default:
                tvRealTimeText.setVisibility(View.VISIBLE);
                break;
        }
    }


    /**
     * 更新取消提示文本
     */
    public void updateCancelHintText(String text) {
        if (tvCancelHint != null) {
            tvCancelHint.setText(text);
            LogUtils.dTag("VoiceInputDialog", "更新取消提示文本: " + text);
        } else {
            LogUtils.wTag("VoiceInputDialog", "tvCancelHint为null，无法更新提示文本: " + text);
        }
    }

    /**
     * 更新发送语音提示文本
     */
    public void updateSendVoiceHintText(String text) {
        if (tvSendVoiceHint != null) {
            tvSendVoiceHint.setText(text);
            LogUtils.dTag("VoiceInputDialog", "更新发送语音提示文本: " + text);
        } else {
            LogUtils.wTag("VoiceInputDialog", "tvSendVoiceHint为null，无法更新提示文本: " + text);
        }
    }

    /**
     * 更新实时文本
     */
    public void updateRealTimeText(String text) {
        if (tvRealTimeText != null) {
            // 确保实时文本区域可见
            if (tvRealTimeText.getVisibility() != View.VISIBLE) {
                LogUtils.dTag("VoiceInputDialog", "实时文本区域不可见，强制显示");
                // 隐藏所有提示区域，显示实时文本
                cancelHintArea.setVisibility(View.GONE);
                sendVoiceHintArea.setVisibility(View.GONE);
                tvRealTimeText.setVisibility(View.VISIBLE);
            }

            // 使用深色文字（在浅色背景上可见）
            tvRealTimeText.setTextColor(ContextCompat.getColor(getContext(), android.R.color.black));
            tvRealTimeText.setText(text);
            LogUtils.dTag("VoiceInputDialog", "实时文本已更新: " + text + "，可见性: " +
                    (tvRealTimeText.getVisibility() == View.VISIBLE ? "VISIBLE" : "GONE"));
        } else {
            LogUtils.wTag("VoiceInputDialog", "tvRealTimeText为null，无法更新文本: " + text);
        }
    }

    /**
     * 更新识别文本（ChatActivity 需要的方法）
     */
    public void updateRecognitionText(String text) {
        updateRealTimeText(text);
    }

    /**
     * 重置对话框状态，清空文本内容
     */
    public void reset() {
        LogUtils.dTag("VoiceInputDialog", "reset() 被调用，清除所有状态");

        // 重置音量监控状态
        lowVolumeCount = 0;
        hasShownLowVolumeHint = false;

        // 重置音量检测器
        volumeDetector.reset();

        if (tvRealTimeText != null) {
            tvRealTimeText.setText("正在听...");
            // 使用深色文字（在浅色背景上可见）
            tvRealTimeText.setTextColor(ContextCompat.getColor(getContext(), android.R.color.black));
            LogUtils.dTag("VoiceInputDialog", "实时文本已重置为: 正在听...");
        }

        // 清空提示文本
        if (tvCancelHint != null) {
            tvCancelHint.setText("松开取消");
        }
        if (tvSendVoiceHint != null) {
            tvSendVoiceHint.setText("松开发送语音");
        }

        // 重置底部提示（现在会根据UsageType显示不同内容）
        if (tvBottomHint != null) {
            tvBottomHint.setText(getSwipeHintText());
            // 使用灰色文字，在白色背景上可见
            tvBottomHint.setTextColor(ContextCompat.getColor(getContext(), android.R.color.darker_gray));
            tvBottomHint.setVisibility(View.VISIBLE);
        }

        // 确保显示实时文本区域，隐藏所有提示
        showHint(HintType.NONE);

        // 重置波形视图
        if (waveformView != null) {
            waveformView.reset();
            LogUtils.dTag("VoiceInputDialog", "波形视图已重置");
        }
    }

    /**
     * 开始波形动画
     */
    public void startWaveformAnimation() {
        LogUtils.dTag("VoiceInputDialog", "startWaveformAnimation() 被调用，waveformView=" + (waveformView != null ? "存在" : "null"));
        if (waveformView != null) {
            LogUtils.dTag("VoiceInputDialog", "设置WaveformView监听状态为true");
            waveformView.setListening(true);
            LogUtils.dTag("VoiceInputDialog", "启动WaveformView动画");
            waveformView.startAnimation();
            LogUtils.dTag("VoiceInputDialog", "波形动画已启动：setListening(true) + startAnimation()");
        } else {
            LogUtils.wTag("VoiceInputDialog", "waveformView为null，无法启动波形动画");
        }
    }

    /**
     * 停止波形动画
     */
    public void stopWaveformAnimation() {
        LogUtils.dTag("VoiceInputDialog", "stopWaveformAnimation() 被调用，waveformView=" + (waveformView != null ? "存在" : "null"));
        if (waveformView != null) {
            waveformView.setListening(false);
            waveformView.stopAnimation();
            waveformView.reset();
            LogUtils.dTag("VoiceInputDialog", "波形动画已停止：setListening(false) + stopAnimation() + reset()");
        } else {
            LogUtils.wTag("VoiceInputDialog", "waveformView为null，无法停止波形动画");
        }
    }

    /**
     * 更新音量
     */
    public void updateVolume(int volume) {
        LogUtils.dTag("VoiceInputDialog", "updateVolume: " + volume + ", waveformView=" + (waveformView != null ? "存在" : "null"));

        // 更新公用音量检测器
        volumeDetector.updateVolume(volume);

        if (waveformView != null) {
            waveformView.updateVolume(volume);
            LogUtils.dTag("VoiceInputDialog", "已传递音量到波形视图: " + volume);
        } else {
            LogUtils.wTag("VoiceInputDialog", "波形视图为空，无法更新音量");
        }

        // 使用公用音量检测器的说话状态来检测音量
        checkLowVolumeWithVolumeDetector(volume);
    }

    /**
     * 基于公用音量检测器的音量检测
     */
    private void checkLowVolumeWithVolumeDetector(int volume) {
        // 使用公用音量检测器判断说话状态
        boolean isSpeaking = volumeDetector.isSpeaking();

        LogUtils.dTag("VoiceInputDialog", "检测音量: 音量=" + volume + ", 说话状态=" + isSpeaking + ", 计数=" + lowVolumeCount + ", 已提示=" + hasShownLowVolumeHint);

        // 使用公用检测器的便捷方法
        if (volumeDetector.isLowVolumeSpeaking(LOW_VOLUME_THRESHOLD)) {
            // 只有在确实在说话但音量太低时才计数
            lowVolumeCount++;
            LogUtils.dTag("VoiceInputDialog", "说话中但音量过低，计数增加到: " + lowVolumeCount + "/" + LOW_VOLUME_COUNT_THRESHOLD);

            if (lowVolumeCount >= LOW_VOLUME_COUNT_THRESHOLD && !hasShownLowVolumeHint) {
                LogUtils.wTag("VoiceInputDialog", "触发低音量提示！用户在说话但音量太低：" + lowVolumeCount);
                showLowVolumeHint();
                hasShownLowVolumeHint = true;
            }
        } else {
            // 如果不在说话或音量正常，重置计数器
            if (lowVolumeCount > 0) {
                LogUtils.dTag("VoiceInputDialog", "重置计数器，说话状态=" + isSpeaking + ", 音量=" + volume + ", 之前计数: " + lowVolumeCount);
                lowVolumeCount = 0;
            }
        }
    }

    /**
     * 显示音量过低提示
     */
    @SuppressLint("ObsoleteSdkInt")

    private void showLowVolumeHint() {
        LogUtils.wTag("VoiceInputDialog", "检测到音量过低，显示提示");
        if (tvRealTimeText != null) {
            tvRealTimeText.setText("🔊 说话声音太轻，请大声一些");
            tvRealTimeText.setTextColor(ContextCompat.getColor(getContext(), android.R.color.holo_orange_dark));

            // 确保文本区域可见
            showHint(HintType.NONE);

            // 3秒后自动恢复
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                if (tvRealTimeText != null) {
                    tvRealTimeText.setText("正在听...");
                    tvRealTimeText.setTextColor(ContextCompat.getColor(getContext(), android.R.color.black));
                }
            }, 3000);
        }

        // 添加震动提示
        try {
            android.os.Vibrator vibrator = (android.os.Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null && vibrator.hasVibrator()) {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    vibrator.vibrate(android.os.VibrationEffect.createOneShot(200, android.os.VibrationEffect.DEFAULT_AMPLITUDE));
                } else {
                    //noinspection deprecation
                    vibrator.vibrate(200);
                }
            }
        } catch (Exception e) {
            LogUtils.wTag("VoiceInputDialog", "震动提示失败", e);
        }
    }

    /**
     * 根据当前滑动方向和使用场景生成提示文字
     */
    private String getSwipeHintText() {
        if (Objects.requireNonNull(currentUsageType) == VoiceUsageType.VOICE_CHAT) {
            return "上滑发送语音，左滑取消，松开转文字";
        }
        return "左滑取消，松开转文字";
    }

    /**
     * 更新底部提示文字
     */
    private void updateBottomHint() {
        if (tvBottomHint != null) {
            tvBottomHint.setText(getSwipeHintText());
            // 确保底部提示可见且颜色正确
            tvBottomHint.setTextColor(ContextCompat.getColor(getContext(), android.R.color.darker_gray));
            tvBottomHint.setVisibility(View.VISIBLE);
            LogUtils.dTag("VoiceInputDialog", "底部提示已更新: " + getSwipeHintText());
        }
    }

    /**
     * 设置使用场景类型
     */
    public void setUsageType(VoiceUsageType usageType) {
        this.currentUsageType = usageType;
        updateBottomHint();
        LogUtils.dTag("VoiceInputDialog", "设置使用场景: " + usageType + "，底部提示已更新");
    }


    /**
     * 显示短录音提示
     */
    @SuppressLint("ObsoleteSdkInt")
    @SuppressWarnings("deprecation")
    public void showShortRecordingHint(String message) {
        LogUtils.dTag("VoiceInputDialog", "显示短录音提示: " + message);
        if (tvRealTimeText != null) {
            tvRealTimeText.setText(message);
            tvRealTimeText.setTextColor(ContextCompat.getColor(getContext(), android.R.color.holo_orange_dark));

            // 确保文本区域可见
            showHint(HintType.NONE);
        }

        // 可以添加震动提示
        try {
            android.os.Vibrator vibrator = (android.os.Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null && vibrator.hasVibrator()) {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    vibrator.vibrate(android.os.VibrationEffect.createOneShot(100, android.os.VibrationEffect.DEFAULT_AMPLITUDE));
                } else {
                    long duration = 100;
                    vibrator.vibrate(duration);
                }
            }
        } catch (Exception e) {
            LogUtils.wTag("VoiceInputDialog", "震动提示失败", e);
        }
    }

    /**
     * 显示超时提示
     */
    public void showTimeoutHint(String message) {
        LogUtils.wTag("VoiceInputDialog", "显示超时提示: " + message);
        if (tvRealTimeText != null) {
            tvRealTimeText.setText("⏰ " + message);
            tvRealTimeText.setTextColor(ContextCompat.getColor(getContext(), android.R.color.holo_orange_dark));

            // 确保文本区域可见
            showHint(HintType.NONE);
        }
    }


}
