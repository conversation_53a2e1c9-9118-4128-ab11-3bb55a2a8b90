package com.supreme.smart.sewing.business;

import android.util.Log;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.database.beans.machine.Machine;
import com.supreme.smart.sewing.database.beans.machine.MachineCounter;
import com.supreme.smart.sewing.database.beans.machine.MachineCounterDaily;
import com.supreme.smart.sewing.database.beans.machine.MachineCounterDailySplit;
import com.supreme.smart.sewing.database.config.RealmConfig;
import com.supreme.smart.sewing.database.utils.DataCacheUtils;
import com.supreme.smart.sewing.utils.CommonUtils;
import com.supreme.smart.sewing.utils.UUIDUtils;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.concurrent.locks.ReentrantLock;

import io.realm.Realm;

/**
 * 机器数据采集辅助类
 */
public enum MachineDataCollectionHelper {

    INSTANCE;

    private static final String TAG = MachineDataCollectionHelper.class.getSimpleName();
    private final ReentrantLock lock = new ReentrantLock();


    private final Object cacheInitLock = new Object();

    /**
     * 取得当前设备ID
     * 说明：系统支持切换多台设备，也就是Machine表中可以保存多条设备信息记录，但是当前设备只用一个，
     * 切换设备时，系统会将当前设备的Id记录下来，
     *
     * @return 设备Id
     */
    public String getCurrentMachineId() {
        // 双重检查锁定模式，确保线程安全且性能最优
        synchronized (cacheInitLock) {
            try {
                // 从缓存数据库获取
                String currentMachineId = DataCacheUtils.get("currentMachineId");

                if (StringUtils.isEmpty(currentMachineId)) {
                    // 生成新的机器ID
                    currentMachineId = UUIDUtils.getId();
                    LogUtils.i(TAG, "生成新的机器ID: " + currentMachineId);

                    // 保存到缓存数据库
                    if (!DataCacheUtils.save("currentMachineId", currentMachineId)) {
                        LogUtils.e(TAG, "保存机器ID到缓存失败");
                    }

                    // 创建机器记录到主数据库
                    createMachineRecord(currentMachineId);
                } else {
                    LogUtils.d(TAG, "从缓存获取机器ID: " + currentMachineId);
                }

                return currentMachineId;

            } catch (Exception e) {
                LogUtils.e(TAG, "获取当前机器ID失败", e);
            }

            return null;
        }
    }

    /**
     * 创建机器记录到数据库
     *
     * @param machineId 机器ID
     */
    private void createMachineRecord(String machineId) {
        Realm realm = null;
        try {
            realm = Realm.getInstance(RealmConfig.getDefaultConfig());
            realm.beginTransaction();

            Machine existingMachine = realm.where(Machine.class)
                    .equalTo("id", machineId)
                    .findFirst();

            if (existingMachine == null) {
                Machine machine = new Machine();
                machine.setId(machineId);
                machine.setMachineNo(machineId);
                machine.setMachineName(machineId);
                machine.setCustomNo(machineId);
                realm.copyToRealm(machine);
            }

            realm.commitTransaction();

        } catch (Exception e) {
            if (realm != null && realm.isInTransaction()) {
                realm.cancelTransaction();
            }
            LogUtils.e(TAG, "创建机器记录失败: " + machineId, e);
        } finally {
            if (realm != null) {
                realm.close();
            }
        }
    }

    /**
     * 更新件数
     *
     * @param machineId  机器ID
     * @param newReading 新的件数读数
     */
    public void updatePieceCounter(String machineId, Long newReading) {
        updateCounters(machineId, CounterType.PIECE, newReading);
    }

    /**
     * 更新剪线次数
     *
     * @param machineId  机器ID
     * @param newReading 新的剪线次数读数
     */
    public void updateCutCounter(String machineId, Long newReading) {
        updateCounters(machineId, CounterType.CUT, newReading);
    }

    /**
     * 更新抬压脚次数
     *
     * @param machineId  机器ID
     * @param newReading 新的抬压脚次数读数
     */
    public void updateRaiseCounter(String machineId, Long newReading) {
        updateCounters(machineId, CounterType.RAISE, newReading);
    }

    /**
     * 更新针数
     *
     * @param machineId  机器ID
     * @param newReading 新的针数读数
     */
    public void updateNeedleCounter(String machineId, Long newReading) {
        updateCounters(machineId, CounterType.NEEDLE, newReading);
    }

    /**
     * 批量更新所有计数器
     *
     * @param machineId     机器ID
     * @param pieceReading  件数读数
     * @param cutReading    剪线次数读数
     * @param raiseReading  抬压脚次数读数
     * @param needleReading 针数读数
     */
    public void updateAllCounter(String machineId,
                                 Long pieceReading, Long cutReading,
                                 Long raiseReading, Long needleReading) {
        updateCounters(machineId, CommonUtils.getCurrentTime(),
                pieceReading, cutReading, raiseReading, needleReading);
    }

    /**
     * 统一的数据采集方法，确保数据一致性
     *
     * @param machineId   机器ID
     * @param counterType 计数器类型
     * @param newReading  新的读数
     */
    private void updateCounters(String machineId,
                                CounterType counterType, Long newReading) {
        if (newReading == null) {
            LogUtils.w(TAG, "newReading is null for counterType: " + counterType);
            return;
        }
        
        lock.lock();
        try (Realm realm = Realm.getInstance(RealmConfig.getDefaultConfig())) {
            realm.executeTransaction(myRealm -> {
                Date recTime = CommonUtils.getCurrentTime();
                // 1. 获取当前读数（作为"上一次"读数）
                Long prevReading = getPrevCounterReadingInTransaction(myRealm, machineId, counterType);

                // 2. 计算增量
                Long increment;
                if (counterType == CounterType.NEEDLE) {
                    // 针数使用特殊的增量计算逻辑
                    increment = calculateNeedleIncrement(prevReading, newReading);
                } else {
                    increment = calculateIncrement(prevReading, newReading);
                }

                // 3. 更新每日统计
                updateDailyCounterInTransaction(myRealm, machineId, recTime, counterType, increment);

                // 4. 更新每小时统计
                updateHourlyCounterInTransaction(myRealm, machineId, recTime, counterType, increment);

                // 5. 最后更新当前读数
                updateCounterReadingInTransaction(myRealm, machineId, counterType, newReading);

            });
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
        } finally {
            lock.unlock();
        }
    }

    /**
     * 批量更新多个计数器
     *
     * @param machineId     机器ID
     * @param recTime       记录时间
     * @param pieceReading  件数读数
     * @param cutReading    剪线次数读数
     * @param raiseReading  抬压脚次数读数
     * @param needleReading 针数读数
     */
    private void updateCounters(String machineId, Date recTime,
                                Long pieceReading, Long cutReading,
                                Long raiseReading, Long needleReading) {
        lock.lock();
        try (Realm realm = Realm.getInstance(RealmConfig.getDefaultConfig())) {
            realm.executeTransaction(myRealm -> {

                if (pieceReading != null) {
                    updateSingleCounterInTransaction(myRealm, machineId, recTime, CounterType.PIECE, pieceReading);
                }

                if (cutReading != null) {
                    updateSingleCounterInTransaction(myRealm, machineId, recTime, CounterType.CUT, cutReading);
                }

                if (raiseReading != null) {
                    updateSingleCounterInTransaction(myRealm, machineId, recTime, CounterType.RAISE, raiseReading);
                }

                if (needleReading != null) {
                    updateSingleCounterInTransaction(myRealm, machineId, recTime, CounterType.NEEDLE, needleReading);
                }

            });
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
        } finally {
            lock.unlock();
        }
    }

    /**
     * 在事务中更新单个计数器
     */
    private void updateSingleCounterInTransaction(Realm realm, String machineId, Date recDate,
                                                  CounterType type, Long newReading) {
        if (newReading == null) {
            return;
        }
        
        Long prevReading = getPrevCounterReadingInTransaction(realm, machineId, type);
        Long increment;
        if (type == CounterType.NEEDLE) {
            // 针数使用特殊的增量计算逻辑
            increment = calculateNeedleIncrement(prevReading, newReading);
        } else {
            increment = calculateIncrement(prevReading, newReading);
        }
        updateDailyCounterInTransaction(realm, machineId, recDate, type, increment);
        updateHourlyCounterInTransaction(realm, machineId, recDate, type, increment);
        updateCounterReadingInTransaction(realm, machineId, type, newReading);
    }


    /**
     * 在事务中获取上一次计数器读数
     */
    private Long getPrevCounterReadingInTransaction(Realm realm, String machineId, CounterType type) {
        MachineCounter item = realm.where(MachineCounter.class)
                .equalTo("machine.id", machineId)
                .findFirst();
        if (item != null) {
            switch (type) {
                case PIECE:
                    return item.getPieceCounterReading();
                case CUT:
                    return item.getCutCounterReading();
                case RAISE:
                    return item.getRaiseCounterReading();
                case NEEDLE:
                    return item.getNeedleCounterReading();
            }
        }
        return null;
    }

    /**
     * 计算增量
     */
    private Long calculateIncrement(Long prevReading, Long newReading) {
        if (newReading == null) {
            return 0L;
        }
        
        if (prevReading == null) {
            return newReading;
        }

        if (newReading >= prevReading) {
            return newReading - prevReading;
        } else {
            // 处理计数器重置情况
            return newReading;
        }
    }

    /**
     * 计算针数增量（处理关机重置的特殊情况）
     *
     * @param prevReading 上一次读数
     * @param newReading  新的读数
     * @return 增量值
     */
    private Long calculateNeedleIncrement(Long prevReading, Long newReading) {
        if (newReading == null) {
            return 0L;
        }
        
        if (prevReading == null) {
            return newReading;
        }

        // 检测是否发生了重置（关机重启）
        // 如果新读数显著小于旧读数（比如小于10），则认为发生了重置
        if (newReading < prevReading && newReading < 10) {
            // 发生重置：返回上一次的读数作为增量（表示关机前累积的针数）
            // 新读数将在后续作为新的起点
            return prevReading;
        } else if (newReading >= prevReading) {
            // 正常情况：返回差值
            return newReading - prevReading;
        } else {
            // 其他情况（可能是计数器溢出等）
            return 0L;
        }
    }

    /**
     * 在事务中更新每日计数器
     */
    private void updateDailyCounterInTransaction(Realm realm, String machineId, Date recTime,
                                                 CounterType type, Long increment) {
        if (increment == null || increment <= 0) return;

        Date recDate = CommonUtils.getDateOfDatetime(recTime);
        MachineCounterDaily daily = realm.where(MachineCounterDaily.class)
                .equalTo("machine.id", machineId)
                .equalTo("recDate", recDate)
                .findFirst();

        if (daily == null) {
            daily = realm.createObject(MachineCounterDaily.class, UUIDUtils.getId());
            Machine machine = realm.where(Machine.class)
                    .equalTo("id", machineId)
                    .findFirst();
            daily.setMachine(machine);
            daily.setRecDate(recDate);

            // 新记录直接设置
            switch (type) {
                case PIECE:
                    daily.setPieceCount(increment);
                    break;
                case CUT:
                    daily.setCutCount(increment);
                    break;
                case RAISE:
                    daily.setRaiseCount(increment);
                    break;
                case NEEDLE:
                    daily.setNeedleCount(increment);
                    break;
            }
        } else {
            // 现有记录累加
            switch (type) {
                case PIECE:
                    Long currentPiece = daily.getPieceCount();
                    daily.setPieceCount((currentPiece != null ? currentPiece : 0) + increment);
                    break;
                case CUT:
                    Long currentCut = daily.getCutCount();
                    daily.setCutCount((currentCut != null ? currentCut : 0) + increment);
                    break;
                case RAISE:
                    Long currentRaise = daily.getRaiseCount();
                    daily.setRaiseCount((currentRaise != null ? currentRaise : 0) + increment);
                    break;
                case NEEDLE:
                    Long currentNeedle = daily.getNeedleCount();
                    daily.setNeedleCount((currentNeedle != null ? currentNeedle : 0) + increment);
                    break;
            }
        }
    }

    /**
     * 在事务中更新每小时计数器
     */
    private void updateHourlyCounterInTransaction(Realm realm, String machineId, Date recTime,
                                                  CounterType type, Long increment) {
        if (increment == null || increment <= 0) return;

        Date recDate = CommonUtils.getDateOfDatetime(recTime);
        // int period = CommonUtils.getDatePart(recDate, CommonUtils.DatePart.HOUR);
        String period = CommonUtils.dateToString(recTime, "HH:mm");
        MachineCounterDailySplit split = realm.where(MachineCounterDailySplit.class)
                .equalTo("machine.id", machineId)
                .equalTo("recDate", recDate)
                .equalTo("period", period)
                .findFirst();

        if (split == null) {
            split = realm.createObject(MachineCounterDailySplit.class, UUIDUtils.getId());
            Machine machine = realm.where(Machine.class)
                    .equalTo("id", machineId)
                    .findFirst();
            split.setMachine(machine);
            split.setRecDate(recDate);
            split.setPeriod(period);

            // 新记录直接设置
            switch (type) {
                case PIECE:
                    split.setPieceCount(increment);
                    break;
                case CUT:
                    split.setCutCount(increment);
                    break;
                case RAISE:
                    split.setRaiseCount(increment);
                    break;
                case NEEDLE:
                    split.setNeedleCount(increment);
                    break;
            }
        } else {
            // 现有记录累加
            switch (type) {
                case PIECE:
                    Long currentPiece = split.getPieceCount();
                    split.setPieceCount((currentPiece != null ? currentPiece : 0) + increment);
                    break;
                case CUT:
                    Long currentCut = split.getCutCount();
                    split.setCutCount((currentCut != null ? currentCut : 0) + increment);
                    break;
                case RAISE:
                    Long currentRaise = split.getRaiseCount();
                    split.setRaiseCount((currentRaise != null ? currentRaise : 0) + increment);
                    break;
                case NEEDLE:
                    Long currentNeedle = split.getNeedleCount();
                    split.setNeedleCount((currentNeedle != null ? currentNeedle : 0) + increment);
                    break;
            }
        }
    }

    /**
     * 在事务中更新计数器读数
     */
    private void updateCounterReadingInTransaction(Realm realm, String machineId,
                                                   CounterType type, Long newReading) {
        if (newReading == null) {
            return;
        }
        MachineCounter counter = realm.where(MachineCounter.class)
                .equalTo("machine.id", machineId)
                .findFirst();

        if (counter == null) {
            counter = realm.createObject(MachineCounter.class, UUIDUtils.getId());
            Machine machine = realm.where(Machine.class)
                    .equalTo("id", machineId)
                    .findFirst();
            counter.setMachine(machine);
        }

        switch (type) {
            case PIECE:
                counter.setPieceCounterReading(newReading);
                break;
            case CUT:
                counter.setCutCounterReading(newReading);
                break;
            case RAISE:
                counter.setRaiseCounterReading(newReading);
                break;
            case NEEDLE:
                counter.setNeedleCounterReading(newReading);
                break;
        }
    }

}