package com.supreme.smart.sewing.database.beans.cache;



import com.supreme.smart.sewing.utils.UUIDUtils;

import java.io.Serializable;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

/**
 * 三方接口调用失败的记录
 */
public class InvokeFailureCache extends RealmObject implements Serializable {
    /**
     * 主键
     */
    @PrimaryKey
    private String id;

    private String contextUrl;

    private String contextPath;

    private String param;

    private String invokeTag;

    private Integer recordTag;

    public InvokeFailureCache() {
        this.id = UUIDUtils.getId();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContextUrl() {
        return contextUrl;
    }

    public void setContextUrl(String contextUrl) {
        this.contextUrl = contextUrl;
    }

    public String getContextPath() {
        return contextPath;
    }

    public void setContextPath(String contextPath) {
        this.contextPath = contextPath;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getInvokeTag() {
        return invokeTag;
    }

    public void setInvokeTag(String invokeTag) {
        this.invokeTag = invokeTag;
    }

    public Integer getRecordTag() {
        return recordTag;
    }

    public void setRecordTag(Integer recordTag) {
        this.recordTag = recordTag;
    }
}
