package com.supreme.smart.sewing.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;

import cn.hutool.core.io.FileUtil;
import top.zibin.luban.InputStreamProvider;
import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

public class BitmapUtils {

    /**
     * 使用Luban压缩Bitmap并获取压缩后的Bitmap
     *
     * @param context   上下文
     * @param srcBitmap 需要压缩的Bitmap
     * @param callback  回调函数，返回压缩后的Bitmap
     */
    public static void compress(Context context, Bitmap srcBitmap, CompressCallback callback) {
        // 将Bitmap转换为InputStream
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        srcBitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);

        // 创建临时文件用于存储压缩结果
        final File cacheDir = context.getCacheDir();
        final File tempFile = new File(cacheDir, "temp_" + System.currentTimeMillis() + ".jpg");

        // 使用InputStreamProvider
        InputStreamProvider provider = new InputStreamProvider() {
            @Override
            public InputStream open() {
                return new ByteArrayInputStream(baos.toByteArray());
            }

            @Override
            public String getPath() {
                return tempFile.getAbsolutePath();
            }
        };

        // 使用Luban压缩
        Luban.with(context)
                .load(provider)
                .setTargetDir(cacheDir.getAbsolutePath())
                .setCompressListener(new OnCompressListener() {
                    private final Handler handler = new Handler(Looper.getMainLooper());

                    @Override
                    public void onStart() {
                        // 压缩开始
                        callback.onStart();
                    }

                    @Override
                    public void onSuccess(File file) {
                        // 压缩成功，将文件转换为Bitmap
                        Bitmap result = BitmapFactory.decodeFile(file.getAbsolutePath());
                        callback.onSuccess(result);
                        FileUtil.del(file);

                        recycleBitmap();
                    }

                    private void recycleBitmap() {
                        // 在压缩成功后延迟回收源位图
                        handler.post(() -> {
                            if (!srcBitmap.isRecycled()) {
                                srcBitmap.recycle();
                            }
                        });
                    }

                    @Override
                    public void onError(Throwable e) {
                        // 压缩失败
                        callback.onError(e);
                        recycleBitmap();
                    }
                })
                .launch();
    }


    /**
     * 将Bitmap转换为Base64字符串
     */
    public static String bitmapToBase64(Bitmap bitmap) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        return Base64.encodeToString(byteArray, Base64.DEFAULT);
    }

    /**
     * 将Base64字符串转换为Bitmap
     */
    public static Bitmap base64ToBitmap(String base64String) {
        byte[] decodedString = Base64.decode(base64String, Base64.DEFAULT);
        return BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);
    }

    /**
     * 缩放Bitmap
     */
    public static Bitmap scaleBitmap(Bitmap bitmap, int width, int height) {
        float scaleWidth = ((float) width) / bitmap.getWidth();
        float scaleHeight = ((float) height) / bitmap.getHeight();
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }

    /**
     * 旋转Bitmap
     */
    public static Bitmap rotateBitmap(Bitmap bitmap, float degrees) {
        Matrix matrix = new Matrix();
        matrix.postRotate(degrees);
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }

    /**
     * 裁剪Bitmap
     */
    public static Bitmap cropBitmap(Bitmap bitmap, int x, int y, int width, int height) {
        return Bitmap.createBitmap(bitmap, x, y, width, height);
    }
}