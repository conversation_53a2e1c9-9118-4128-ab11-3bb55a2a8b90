package com.supreme.smart.sewing.modbus;

public final class ModbusConstants {
    // Modbus功能码
    public static final byte FUNCTION_READ_COILS = 0x01;                    // 读取线圈状态
    public static final byte FUNCTION_READ_DISCRETE_INPUTS = 0x02;          // 读取离散输入状态
    public static final byte FUNCTION_READ_HOLDING_REGISTERS = 0x03;        // 读取保持寄存器
    public static final byte FUNCTION_READ_INPUT_REGISTERS = 0x04;          // 读取输入寄存器
    public static final byte FUNCTION_WRITE_SINGLE_COIL = 0x05;             // 写单个线圈
    public static final byte FUNCTION_WRITE_SINGLE_REGISTER = 0x06;         // 写单个寄存器
    public static final byte FUNCTION_WRITE_MULTIPLE_COILS = 0x0F;          // 写多个线圈
    public static final byte FUNCTION_WRITE_MULTIPLE_REGISTERS = 0x10;      // 写多个寄存器
    
    // Modbus异常码
    public static final byte EXCEPTION_ILLEGAL_FUNCTION = 0x01;             // 非法功能码
    public static final byte EXCEPTION_ILLEGAL_DATA_ADDRESS = 0x02;         // 非法数据地址
    public static final byte EXCEPTION_ILLEGAL_DATA_VALUE = 0x03;           // 非法数据值
    public static final byte EXCEPTION_SLAVE_DEVICE_FAILURE = 0x04;         // 从站设备故障
    
}