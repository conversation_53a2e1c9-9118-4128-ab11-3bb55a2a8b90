package com.supreme.smart.sewing.update;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.databinding.UpgradeBinding;
import com.supreme.smart.sewing.utils.LocaleHelper;

import java.io.File;

/**
 * 应用更新Activity
 * Application Update Activity
 */
public class UpgradeActivity extends AppCompatActivity implements UpgradeService.DownloadCallback {
    
    private static final String TAG = "UpdateActivity";
    
    private UpgradeBinding binding;
    private UpgradeInfo upgradeInfo;
    private UpgradeService upgradeService;
    private boolean serviceBound = false;
    private File downloadedFile;
    
    // 使用新的Activity Result API
    private ActivityResultLauncher<String> requestStoragePermissionLauncher;
    private ActivityResultLauncher<Intent> requestInstallPermissionLauncher;
    
    // DataBinding 变量
    private final ObservableField<String> currentVersion = new ObservableField<>();
    private final ObservableInt downloadProgress = new ObservableInt(0);
    private final ObservableField<String> downloadSpeed = new ObservableField<>("0KB/s");
    private final ObservableBoolean isDownloading = new ObservableBoolean(false);
    private final ObservableBoolean showInstallButton = new ObservableBoolean(false);
    
    // 服务连接
    private final ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            LogUtils.dTag(TAG, "Service connected");
            UpgradeService.UpdateServiceBinder binder = (UpgradeService.UpdateServiceBinder) service;
            upgradeService = binder.getService();
            serviceBound = true;
        }
        
        @Override
        public void onServiceDisconnected(ComponentName name) {
            LogUtils.dTag(TAG, "Service disconnected");
            upgradeService = null;
            serviceBound = false;
        }
    };

    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase)));
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化Activity Result Launchers
        initializeActivityResultLaunchers();
        
        // 初始化DataBinding
        binding = DataBindingUtil.setContentView(this, R.layout.upgrade);
        
        // 获取更新信息
        upgradeInfo = (UpgradeInfo) getIntent().getSerializableExtra("updateInfo");
        if (upgradeInfo == null) {
            LogUtils.eTag(TAG, "UpdateInfo is null");
            finish();
            return;
        }
        
        initViews();
        setupData();
        bindUpdateService();
    }
    
    /**
     * 初始化Activity Result Launchers
     */
    private void initializeActivityResultLaunchers() {
        // 存储权限请求
        requestStoragePermissionLauncher = registerForActivityResult(
            new ActivityResultContracts.RequestPermission(),
            isGranted -> {
                if (isGranted) {
                    startDownload();
                } else {
                    Toast.makeText(this, "需要存储权限才能下载更新", Toast.LENGTH_LONG).show();
                }
            }
        );
        
        // 安装权限请求
        requestInstallPermissionLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    handleInstallPermissionResult();
                }
            }
        );
    }
    
    /**
     * 处理安装权限请求结果
     */
    private void handleInstallPermissionResult() {
        if (checkInstallPermission()) {
            if (downloadedFile != null) {
                installApk(downloadedFile);
            }
        } else {
            Toast.makeText(this, "需要安装权限才能安装更新", Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        // 设置点击事件
        binding.btnLater.setOnClickListener(v -> onLaterClick());
        binding.btnUpdate.setOnClickListener(v -> onUpdateClick());
        binding.btnCancel.setOnClickListener(v -> onCancelClick());
        binding.btnInstall.setOnClickListener(v -> onInstallClick());
        
        // 初始化DataBinding变量
        currentVersion.set(UpgradeManager.getInstance(this).getCurrentVersionName());
        
        // 设置数据绑定
        binding.setUpgradeInfo(upgradeInfo);
        binding.setCurrentVersion(currentVersion);
        binding.setDownloadProgress(downloadProgress);
        binding.setDownloadSpeed(downloadSpeed);
        binding.setIsDownloading(isDownloading);
        binding.setShowInstallButton(showInstallButton);
    }
    
    /**
     * 设置数据
     */
    private void setupData() {
        // DataBinding会自动处理数据绑定，这里只需要确保数据已设置
        // 数据绑定在initViews()中已完成
    }
    
    /**
     * 绑定更新服务
     */
    private void bindUpdateService() {
        Intent serviceIntent = new Intent(this, UpgradeService.class);
        startService(serviceIntent);
        bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE);
    }
    
    /**
     * 稍后更新按钮点击
     */
    private void onLaterClick() {
        if (upgradeInfo.isForceUpdate()) {
            Toast.makeText(this, "此版本为强制更新，无法跳过", Toast.LENGTH_SHORT).show();
            return;
        }
        finish();
    }
    
    /**
     * 立即更新按钮点击
     */
    private void onUpdateClick() {
        // 检查存储权限
        if (!checkStoragePermission()) {
            requestStoragePermission();
            return;
        }
        
        startDownload();
    }
    
    /**
     * 取消下载按钮点击
     */
    private void onCancelClick() {
        if (upgradeService != null) {
            upgradeService.cancelDownload();
        }
        showInitialUI();
    }
    
    /**
     * 安装按钮点击
     */
    private void onInstallClick() {
        if (downloadedFile != null && downloadedFile.exists()) {
            installApk(downloadedFile);
        } else {
            Toast.makeText(this, "安装文件不存在", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 开始下载
     */
    private void startDownload() {
        if (upgradeService != null) {
            showDownloadUI();
            upgradeService.startDownload(upgradeInfo, this);
        } else {
            Toast.makeText(this, "服务未准备就绪，请稍后重试", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 显示初始UI
     */
    private void showInitialUI() {
        isDownloading.set(false);
        showInstallButton.set(false);
    }
    
    /**
     * 显示下载UI
     */
    private void showDownloadUI() {
        isDownloading.set(true);
        showInstallButton.set(false);
        downloadProgress.set(0);
        downloadSpeed.set("0KB/s");
        
        binding.tvDownloadStatus.setText(R.string.downloading);
    }
    
    /**
     * 显示下载完成UI
     */
    private void showDownloadCompletedUI() {
        showInstallButton.set(true);
        binding.tvDownloadStatus.setText(R.string.download_completed);
        binding.btnCancel.setVisibility(View.GONE);
    }
    
    /**
     * 检查存储权限
     */
    @SuppressLint("ObsoleteSdkInt")
    private boolean checkStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    == PackageManager.PERMISSION_GRANTED;
        }
        return true;
    }
    
    /**
     * 请求存储权限
     */
    private void requestStoragePermission() {
        requestStoragePermissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }
    
    /**
     * 检查安装权限
     */
    @SuppressLint("ObsoleteSdkInt")
    private boolean checkInstallPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return getPackageManager().canRequestPackageInstalls();
        }
        return true;
    }
    
    /**
     * 请求安装权限
     */
    @SuppressLint("ObsoleteSdkInt")
    private void requestInstallPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
            intent.setData(Uri.parse("package:" + getPackageName()));
            requestInstallPermissionLauncher.launch(intent);
        }
    }
    
    /**
     * 安装APK
     */
    @SuppressLint("ObsoleteSdkInt")
    private void installApk(File apkFile) {
        if (!checkInstallPermission()) {
            requestInstallPermission();
            return;
        }
        
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            Uri apkUri;
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0及以上使用FileProvider
                apkUri = FileProvider.getUriForFile(this,
                        getPackageName() + ".fileprovider", apkFile);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                apkUri = Uri.fromFile(apkFile);
            }
            
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
            
        } catch (Exception e) {
            LogUtils.eTag(TAG, "安装APK失败", e);
            Toast.makeText(this, "安装失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    // DownloadCallback 实现
    @Override
    public void onProgress(int progress, long downloadedBytes, long totalBytes, String speed) {
        runOnUiThread(() -> {
            downloadProgress.set(progress);
            downloadSpeed.set(speed);
        });
    }
    
    @Override
    public void onCompleted(File file) {
        runOnUiThread(() -> {
            downloadedFile = file;
            showDownloadCompletedUI();
            Toast.makeText(this, getString(R.string.download_completed), Toast.LENGTH_SHORT).show();
        });
    }
    
    @Override
    public void onFailed(String error) {
        runOnUiThread(() -> {
            showInitialUI();
            Toast.makeText(this, getString(R.string.download_failed) + ": " + error, Toast.LENGTH_LONG).show();
        });
    }
    
    @Override
    public void onCancelled() {
        runOnUiThread(() -> {
            showInitialUI();
            Toast.makeText(this, "下载已取消", Toast.LENGTH_SHORT).show();
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        if (serviceBound) {
            unbindService(serviceConnection);
            serviceBound = false;
        }
    }
    
    @Override
    public void onBackPressed() {
        if (upgradeInfo.isForceUpdate() && upgradeService != null &&
            upgradeService.getDownloadStatus() == UpgradeService.DownloadStatus.DOWNLOADING) {
            Toast.makeText(this, "正在下载强制更新，无法退出", Toast.LENGTH_SHORT).show();
            return;
        }
        super.onBackPressed();
    }
}