package com.supreme.smart.sewing.database.utils;


import static com.supreme.smart.sewing.utils.CommonUtils.DatePart.SECOND;

import com.supreme.smart.sewing.database.beans.sys.SysPowerAction;
import com.supreme.smart.sewing.database.config.RealmConfig;
import com.supreme.smart.sewing.utils.CommonUtils;

import java.util.Date;

import io.realm.Realm;

public class DataActionUtils {

    public synchronized static boolean savePowerAction(final String id, boolean byBoot) {
        final Realm realm = Realm.getInstance(RealmConfig.getDefaultConfig());
        try {
            realm.beginTransaction();
            SysPowerAction element = realm.where(SysPowerAction.class).equalTo("id", id).findFirst();
            boolean isUpdate = true;
            if (element == null) {
                isUpdate = false;
                element = new SysPowerAction();
                element.setId(id);
            }

            final Date currentTime = CommonUtils.getCurrentTime();
            if (isUpdate) {
                if (element.getFirstOnTime() == null)
                    element.setFirstOnTime(currentTime);
            } else {
                element.setFirstOnTime(currentTime);
                element.setDuration(0);
            }

            if (!byBoot) {
                final int durationDiff = (int) CommonUtils.dateDiff(SECOND, element.getAcTime(), currentTime);
                final int oldDuration = element.getDuration() == null ? 0 : element.getDuration();
                final int newDuration = oldDuration + durationDiff;
                element.setDuration(newDuration);
            }

            element.setAcTime(currentTime);

            realm.copyToRealm(element);
            realm.commitTransaction();
            return true;
        } catch (Exception e) {
            realm.cancelTransaction();
            return false;
        } finally {
            realm.close();
        }

    }


    /**
     * 获取
     *
     * @param id 键
     * @return 值
     */
    public synchronized static int getPowerActionDuration(final String id) {
        try (Realm realm = Realm.getInstance(RealmConfig.getDefaultConfig())) {
            final SysPowerAction element = realm.where(SysPowerAction.class)
                    .equalTo("id", id).findFirst();
            if (element != null)
                return element.getDuration() == null ? 0 : element.getDuration();
        }
        return 0;
    }


}
