package com.supreme.smart.sewing.modbus;

import static com.supreme.smart.sewing.modbus.ModbusConstants.EXCEPTION_ILLEGAL_DATA_ADDRESS;
import static com.supreme.smart.sewing.modbus.ModbusConstants.EXCEPTION_ILLEGAL_DATA_VALUE;
import static com.supreme.smart.sewing.modbus.ModbusConstants.EXCEPTION_ILLEGAL_FUNCTION;
import static com.supreme.smart.sewing.modbus.ModbusConstants.EXCEPTION_SLAVE_DEVICE_FAILURE;
import static com.supreme.smart.sewing.modbus.ModbusConstants.FUNCTION_READ_COILS;
import static com.supreme.smart.sewing.modbus.ModbusConstants.FUNCTION_READ_DISCRETE_INPUTS;
import static com.supreme.smart.sewing.modbus.ModbusConstants.FUNCTION_READ_HOLDING_REGISTERS;

import android.content.Context;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.serial.SerialPortManager;
import com.supreme.smart.sewing.utils.CommonUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Modbus协议管理器
 * 基于SerialPortManager实现Modbus RTU协议
 * 保留原有串口通信功能
 */
public class ModbusManager {
    private static final String TAG = "ModbusManager";

    // 单例实例
    private static volatile ModbusManager instance;

    private final SerialPortManager serialPortManager;
    private int defaultTimeout = 1000; // 默认超时时间1秒

    /**
     * 私有构造函数，防止外部实例化
     */
    private ModbusManager() {
        this.serialPortManager = SerialPortManager.getInstance();
    }

    /**
     * 获取ModbusManager的单例实例
     *
     * @return ModbusManager实例
     */
    public static ModbusManager getInstance() {
        if (instance == null) {
            synchronized (ModbusManager.class) {
                if (instance == null) {
                    instance = new ModbusManager();
                }
            }
        }
        return instance;
    }


    /**
     * 初始化
     */
    public boolean init(Context context, String portPath, int baudRate) {
        return serialPortManager.initSerialPort(context, portPath, baudRate);
    }

    /**
     * 设置默认超时时间
     */
    public void setDefaultTimeout(int timeout) {
        this.defaultTimeout = timeout;
    }

    /**
     * 计算CRC16校验码
     */
    private int calculateCRC16(byte[] data) {
        int crc = 0xFFFF;
        for (byte b : data) {
            crc ^= (b & 0xFF);
            for (int i = 0; i < 8; i++) {
                if ((crc & 0x0001) != 0) {
                    crc >>= 1;
                    crc ^= 0xA001;
                } else {
                    crc >>= 1;
                }
            }
        }
        return crc;
    }

    /**
     * 构建Modbus RTU请求帧
     */
    private byte[] buildModbusFrame(byte slaveId, byte funcCode, byte[] data) {
        byte[] frame = new byte[data.length + 4]; // slaveId + funcCode + data + CRC(2字节)
        frame[0] = slaveId;
        frame[1] = funcCode;
        System.arraycopy(data, 0, frame, 2, data.length);

        // 计算CRC校验码
        byte[] frameWithoutCRC = Arrays.copyOf(frame, frame.length - 2);
        int crc = calculateCRC16(frameWithoutCRC);
        frame[frame.length - 2] = (byte) (crc & 0xFF);        // CRC低字节
        frame[frame.length - 1] = (byte) ((crc >> 8) & 0xFF); // CRC高字节

        return frame;
    }

    /**
     * 验证Modbus响应帧
     */
    private boolean validateModbusFrame(byte[] frame) {
        if (frame == null || frame.length < 4) {
            return false;
        }

        // 提取CRC
        byte[] frameWithoutCRC = Arrays.copyOf(frame, frame.length - 2);
        int receivedCRC = ((frame[frame.length - 1] & 0xFF) << 8) | (frame[frame.length - 2] & 0xFF);
        int calculatedCRC = calculateCRC16(frameWithoutCRC);

        return receivedCRC == calculatedCRC;
    }

    /**
     * 分离多个Modbus响应帧
     *
     * @param data 接收到的原始数据
     * @return 分离后的帧列表
     */
    private List<byte[]> separateFrames(byte[] data) {
        List<byte[]> frames = new ArrayList<>();
        int index = 0;

        while (index < data.length) {
            // 至少需要4个字节才能开始解析（从站地址1字节 + 功能码1字节 + 数据长度1字节 + CRC2字节）
            if (index + 4 > data.length) {
                break;
            }

            // 获取功能码
            byte funcCode = data[index + 1];

            // 计算帧长度
            int frameLength;
            if ((funcCode & 0x80) != 0) {
                // 异常响应固定为5字节
                frameLength = 5;
            } else {
                // 正常响应：从站地址(1) + 功能码(1) + 数据长度(1) + 数据(N) + CRC(2)
                int dataLength = data[index + 2] & 0xFF;
                frameLength = 3 + dataLength + 2;
            }

            // 检查是否有足够的数据
            if (index + frameLength > data.length) {
                break;
            }

            // 提取当前帧
            byte[] frame = Arrays.copyOfRange(data, index, index + frameLength);

            // 验证CRC
            if (validateModbusFrame(frame)) {
                frames.add(frame);
                index += frameLength;
            } else {
                // CRC校验失败，尝试向后移动一个字节继续查找
                index++;
            }
        }

        return frames;
    }

    /**
     * 解析多个Modbus响应
     *
     * @param response             原始响应数据
     * @param expectedFunctionCode 期望的功能码
     * @return 解析后的响应列表
     */
    private List<ModbusResponse> parseModbusResponses(byte[] response, byte expectedFunctionCode) {
        if (response == null || response.length < 4) {
            return Collections.singletonList(ModbusResponse.error("响应数据长度不足"));
        }

        // 分离可能的多个帧
        List<byte[]> frames = separateFrames(response);
        if (frames.isEmpty()) {
            return Collections.singletonList(ModbusResponse.error("无法解析响应帧"));
        }

        List<ModbusResponse> responses = new ArrayList<>();
        for (byte[] frame : frames) {
            if (!validateModbusFrame(frame)) {
                responses.add(ModbusResponse.error("CRC校验失败"));
                continue;
            }

            byte funcCode = frame[1];

            // 检查是否为异常响应
            if ((funcCode & 0x80) != 0) {
                byte exceptionCode = frame[2];
                String errorMessage = getExceptionMessage(exceptionCode);
                responses.add(ModbusResponse.exception(exceptionCode, errorMessage));
                continue;
            }

            // 检查功能码是否匹配
            if (funcCode != expectedFunctionCode) {
                responses.add(ModbusResponse.error("功能码不匹配"));
                continue;
            }

            // 提取数据部分（去除从站地址、功能码、数据长度和CRC）
            byte[] data = Arrays.copyOfRange(frame, 3, frame.length - 2);
            responses.add(ModbusResponse.success(data));
        }

        return responses;
    }

    /**
     * 获取异常码对应的错误信息
     */
    private String getExceptionMessage(byte exceptionCode) {
        switch (exceptionCode) {
            case EXCEPTION_ILLEGAL_FUNCTION:
                return "非法功能码";
            case EXCEPTION_ILLEGAL_DATA_ADDRESS:
                return "非法数据地址";
            case EXCEPTION_ILLEGAL_DATA_VALUE:
                return "非法数据值";
            case EXCEPTION_SLAVE_DEVICE_FAILURE:
                return "从站设备故障";
            default:
                return "未知异常码: " + (exceptionCode & 0xFF);
        }
    }

    /**
     * 读取线圈状态 (功能码 0x01) - 同步方法
     */
    public ModbusResponse readCoils(byte slaveId, int startAddress, int quantity) {
        if (quantity < 1 || quantity > 2000) {
            return ModbusResponse.error("线圈数量超出范围 (1-2000)");
        }

        byte[] data = new byte[4];
        data[0] = (byte) ((startAddress >> 8) & 0xFF);  // 起始地址高字节
        data[1] = (byte) (startAddress & 0xFF);         // 起始地址低字节
        data[2] = (byte) ((quantity >> 8) & 0xFF);      // 数量高字节
        data[3] = (byte) (quantity & 0xFF);             // 数量低字节

        return sendModbusRequest(slaveId, FUNCTION_READ_COILS, data);
    }

    /**
     * 读取线圈状态 (功能码 0x01) - 异步方法
     */
    public Observable<ModbusResponse> readCoilsAsync(byte slaveId, int startAddress, int quantity) {
        if (quantity < 1 || quantity > 2000) {
            return Observable.just(ModbusResponse.error("线圈数量超出范围 (1-2000)"));
        }

        byte[] data = new byte[4];
        data[0] = (byte) ((startAddress >> 8) & 0xFF);  // 起始地址高字节
        data[1] = (byte) (startAddress & 0xFF);         // 起始地址低字节
        data[2] = (byte) ((quantity >> 8) & 0xFF);      // 数量高字节
        data[3] = (byte) (quantity & 0xFF);             // 数量低字节

        return sendModbusRequestAsync(slaveId, FUNCTION_READ_COILS, data);
    }

    /**
     * 读取离散输入状态 (功能码 0x02) - 同步方法
     */
    public ModbusResponse readDiscreteInputs(byte slaveId, int startAddress, int quantity) {
        if (quantity < 1 || quantity > 2000) {
            return ModbusResponse.error("离散输入数量超出范围 (1-2000)");
        }

        byte[] data = new byte[4];
        data[0] = (byte) ((startAddress >> 8) & 0xFF);
        data[1] = (byte) (startAddress & 0xFF);
        data[2] = (byte) ((quantity >> 8) & 0xFF);
        data[3] = (byte) (quantity & 0xFF);

        return sendModbusRequest(slaveId, FUNCTION_READ_DISCRETE_INPUTS, data);
    }

    /**
     * 读取离散输入状态 (功能码 0x02) - 异步方法
     */
    public Observable<ModbusResponse> readDiscreteInputsAsync(byte slaveId, int startAddress, int quantity) {
        if (quantity < 1 || quantity > 2000) {
            return Observable.just(ModbusResponse.error("离散输入数量超出范围 (1-2000)"));
        }

        byte[] data = new byte[4];
        data[0] = (byte) ((startAddress >> 8) & 0xFF);
        data[1] = (byte) (startAddress & 0xFF);
        data[2] = (byte) ((quantity >> 8) & 0xFF);
        data[3] = (byte) (quantity & 0xFF);

        return sendModbusRequestAsync(slaveId, FUNCTION_READ_DISCRETE_INPUTS, data);
    }

    /**
     * 批量读取保持寄存器 - 异步方法
     * 一次性发送多个读取保持寄存器的命令，并获取所有响应
     *
     * @param slaveId   从站地址
     * @param registers 要读取的寄存器列表，每个元素是一个长度为2的数组，第一个元素是起始地址，第二个元素是数量
     * @return Observable<List < ModbusResponse>> 包含所有响应的Observable
     */
    public Observable<List<ModbusResponse>> batchReadHoldingRegisters(byte slaveId, int[][] registers) {
        if (registers == null || registers.length == 0) {
            return Observable.just(new ArrayList<>());
        }


        LogUtils.dTag(TAG, "批量发送Modbus请求，共 " + registers.length + " 条命令");
        int index = 0;
        // 创建命令列表
        List<byte[]> commands = new ArrayList<>();
        for (int[] register : registers) {
            int startAddress = register[0];
            int quantity = register[1];

            if (quantity < 1 || quantity > 125) {
                return Observable.error(new IllegalArgumentException("寄存器数量超出范围 (1-125): " + quantity));
            }

            byte[] data = new byte[4];
            data[0] = (byte) ((startAddress >> 8) & 0xFF);
            data[1] = (byte) (startAddress & 0xFF);
            data[2] = (byte) ((quantity >> 8) & 0xFF);
            data[3] = (byte) (quantity & 0xFF);

            byte[] frame = buildModbusFrame(slaveId, FUNCTION_READ_HOLDING_REGISTERS, data);
            commands.add(frame);
            LogUtils.dTag(TAG, "发送Modbus命令 " + (++index) + ": " + CommonUtils.convertByteArrayToString(frame));

        }

        // 使用SerialPortManager的sendCommands方法一次性发送所有命令
        return serialPortManager.sendCommands(commands, defaultTimeout)
                .map(responses -> {
                    List<ModbusResponse> modbusResponses = new ArrayList<>();

                    // 解析每个响应
                    for (int i = 0; i < responses.size(); i++) {
                        byte[] response = responses.get(i);
                        LogUtils.dTag(TAG, "接收Modbus响应 " + (i + 1) + ": " + CommonUtils.convertByteArrayToString(response));
                        modbusResponses.addAll(parseModbusResponses(response, FUNCTION_READ_HOLDING_REGISTERS));
                    }

                    return modbusResponses;
                })
                .onErrorReturn(throwable -> {
                    LogUtils.eTag(TAG, "批量Modbus请求失败: " + throwable.getMessage());
                    List<ModbusResponse> errorResponses = new ArrayList<>();
                    for (int i = 0; i < registers.length; i++) {
                        errorResponses.add(ModbusResponse.error("通信失败: " + throwable.getMessage()));
                    }
                    return errorResponses;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 发送Modbus请求并等待响应（同步方法）
     */
    private ModbusResponse sendModbusRequest(byte slaveId, byte funcCode, byte[] data) {
        List<ModbusResponse> responses = sendModbusRequest0(slaveId, funcCode, data, defaultTimeout);
        return responses.isEmpty() ? ModbusResponse.error("无响应") : responses.get(0);
    }

    /**
     * 发送Modbus请求并等待响应（同步方法，指定超时时间）
     */
    private ModbusResponse sendModbusRequest(byte slaveId, byte funcCode, byte[] data, int timeout) {
        List<ModbusResponse> responses = sendModbusRequest0(slaveId, funcCode, data, timeout);
        return responses.isEmpty() ? ModbusResponse.error("无响应") : responses.get(0);
    }

    /**
     * 发送Modbus请求并等待响应（异步方法）
     */
    private Observable<ModbusResponse> sendModbusRequestAsync(byte slaveId, byte funcCode, byte[] data) {
        return sendModbusRequestAsync(slaveId, funcCode, data, defaultTimeout);
    }

    /**
     * 发送Modbus请求并等待响应（异步方法，指定超时时间）
     */
    private Observable<ModbusResponse> sendModbusRequestAsync(byte slaveId, byte funcCode, byte[] data, int timeout) {
        return Observable.fromCallable(() ->
                        sendModbusRequest0(slaveId, funcCode, data, timeout)
                ).flatMap(responses -> {
                    if (responses.isEmpty()) {
                        return Observable.just(ModbusResponse.error("无响应"));
                    }
                    return Observable.fromIterable(responses);
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 发送Modbus请求并等待响应
     */
    private List<ModbusResponse> sendModbusRequest0(byte slaveId, byte funcCode, byte[] data, int timeout) {
        try {
            byte[] frame = buildModbusFrame(slaveId, funcCode, data);
            LogUtils.dTag(TAG, "发送Modbus请求: " + CommonUtils.convertByteArrayToString(frame));

            List<byte[]> commands = new ArrayList<>(1);
            commands.add(frame);

            List<byte[]> responses = serialPortManager.sendCommands(commands, timeout)
                    .blockingFirst();

            if (responses != null && !responses.isEmpty()) {
                byte[] response = responses.get(0);
                LogUtils.dTag(TAG, "接收Modbus响应: " + CommonUtils.convertByteArrayToString(response));
                return parseModbusResponses(response, funcCode);
            } else {
                LogUtils.eTag(TAG, "Modbus请求超时或无响应");
                return Collections.singletonList(ModbusResponse.error("请求超时或无响应"));
            }

        } catch (Exception e) {
            LogUtils.eTag(TAG, "Modbus请求失败: " + e.getMessage());
            return Collections.singletonList(ModbusResponse.error("通信失败: " + e.getMessage()));
        }
    }

    public boolean isConnected() {
        return serialPortManager.isConnected();
    }

    /**
     * 关闭连接并释放资源
     */
    public void close() {
        serialPortManager.closeSerialPort();
    }

}

