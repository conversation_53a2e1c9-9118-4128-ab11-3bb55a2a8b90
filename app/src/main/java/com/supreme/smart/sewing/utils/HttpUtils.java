package com.supreme.smart.sewing.utils;


import android.graphics.Bitmap;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.blankj.utilcode.util.LogUtils;

import org.jetbrains.annotations.NotNull;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


public final class HttpUtils {
    private static final String TAG = HttpUtils.class.getSimpleName();
    private static HttpUtils instance;
    
    private HttpUtils() {
    }
    
    public static synchronized HttpUtils getInstance() {
        if (instance == null) {
            instance = new HttpUtils();
        }
        return instance;
    }
    /**
     * RGS TOKEN
     */
    private final static String RGS_ACCESS_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hc" +
            "y54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjIiLCJodHRwOi8vc2NoZW" +
            "1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiYWRtaW4iLCJBc3BOZXQuSWRlbnRpdHk" +
            "uU2VjdXJpdHlTdGFtcCI6IjBhODMwZDhmLTgwNTktNzI4OS0zMDA3LTNhMDQwY2I2ZTVjYSIsImh0dHA6Ly9zY2hlbWFzLm1p" +
            "Y3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFkbWluIiwiaHR0cDovL3d3dy5hc3BuZXRib" +
            "2lsZXJwbGF0ZS5jb20vaWRlbnRpdHkvY2xhaW1zL3RlbmFudElkIjoiMSIsIkNQU19VU0VSTkFNRSI6ImFkbWluIiwiQ1BTX0" +
            "ZVTExOQU1FIjoiYWRtaW4iLCJDUFNfT1JHSUQiOiIyIiwic3ViIjoiMiIsImp0aSI6IjA4NTJjOGEzLTgzZmMtNGZkZS1hNDk" +
            "2LTM1OTJiNWIzODNiMCIsImlhdCI6MTY1OTY2ODUzMCwibmJmIjoxNjU5NjY4NTMwLCJleHAiOjE2NjAyNzMzMzAsImlzcyI6" +
            "IkNQUyIsImF1ZCI6IkNQUyJ9.pdyD8__oS4sw1kMMC3UY-xUZCwwC08XY5HCp9KO6otU";

    private final static String TEST_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjIiLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiYWRtaW4iLCJBc3BOZXQuSWRlbnRpdHkuU2VjdXJpdHlTdGFtcCI6IjBhODMwZDhmLTgwNTktNzI4OS0zMDA3LTNhMDQwY2I2ZTVjYSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFkbWluIiwiaHR0cDovL3d3dy5hc3BuZXRib2lsZXJwbGF0ZS5jb20vaWRlbnRpdHkvY2xhaW1zL3RlbmFudElkIjoiMSIsIkNQU19VU0VSTkFNRSI6ImFkbWluIiwiQ1BTX0ZVTExOQU1FIjoiYWRtaW4iLCJDUFNfT1JHSUQiOiIyIiwic3ViIjoiMiIsImp0aSI6IjUxYTU3NGFhLTJkMmItNDc0Ni1hZjAxLWU5NDVkZWI5ZTVhNCIsImlhdCI6MTY2MDk4MDE1NSwibmJmIjoxNjYwOTgwMTU1LCJleHAiOjE2NjE1ODQ5NTUsImlzcyI6IkNQUyIsImF1ZCI6IkNQUyJ9.be6tyG-XDobh291X0OeY10LOGswtN_BR00FlENnDyFI";


    /**
     * POST 异步方法
     */
    public static void post(String httpUrl,
                            String contextPath,
                            Callback callback,
                            String jsonString) {
         OkHttpClient client = getOkHttpClient();
        try {
             RequestBody requestBody = RequestBody.create(jsonString,
                    MediaType.parse("application/json; charset=utf-8"));
             Request request = new Request.Builder()
                    .url(httpUrl + contextPath)
                    .post(requestBody).build();
            client.newCall(request).enqueue(callback);
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
        }
    }

    /**
     * GET 异步方法
     */
    public static void get(String httpUrl,
                           String contextPath,
                           Callback callback) {
        OkHttpClient client = getOkHttpClient();
        try {
            Request request = new Request.Builder()
                    .url(httpUrl + contextPath)
                    .get().build();
            client.newCall(request).enqueue(callback);
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
        }
    }

    /**
     * POST 同步方法
     */
    public static Response postSync(String httpUrl,
                                    String contextPath,
                                    String jsonString) {
        OkHttpClient client = getOkHttpClient();
        try {
            RequestBody requestBody = RequestBody.create(jsonString,
                    MediaType.parse("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url(httpUrl + contextPath).post(requestBody).build();
            return client.newCall(request).execute();
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
        }
        return null;
    }

    /**
     * GET 同步方法
     */
    public static Response getSync(String httpUrl, String contextPath) {
        OkHttpClient client = getOkHttpClient();
        try {
            Request request = new Request.Builder().url(httpUrl + contextPath).get().build();
            return client.newCall(request).execute();
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
        }
        return null;
    }

    public static String uploadFile(String httpUrl,
                                    String contextPath,
                                    String filePath) {
        // 创建OkHttpClient实例
        OkHttpClient client = getOkHttpClient();
        // 文件路径
        File file = new File(filePath);
        // 创建请求体
        RequestBody body = RequestBody.create(file,
                MediaType.parse("application/octet-stream"));
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("biz", "File-Upload")
                .addFormDataPart("file", file.getName(), body)
                .build();
        try {
        // 创建请求
            Request request = new Request.Builder()
                .url(httpUrl + contextPath)
                .post(requestBody)
                .build();

            Response response = client.newCall(request).execute();
            assert response.body() != null;
            String responseString = response.body().string();
            LogUtils.i("responseString" + responseString);
            return responseString;
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return null;
    }


    public static void uploadFileSync(String httpUrl,
                                      String contextPath,
                                      String filePath,
                                      Callback callback) {
        // 创建OkHttpClient实例
        OkHttpClient client = getOkHttpClient();
        // 文件路径
        File file = new File(filePath);
        // 创建请求体
        RequestBody body = RequestBody.create(file,
                MediaType.parse("application/octet-stream"));
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("biz", "File-Upload")
                .addFormDataPart("table", "WcsLocation")
                .addFormDataPart("id", "1816711759563485185")
                .addFormDataPart("photoType", "MachinePhoto")
                .addFormDataPart("file", file.getName(), body)
                .build();
        try {
            // 创建请求
            Request request = new Request.Builder()
                .url(httpUrl + contextPath)
                .post(requestBody)
                .build();

            // 发送请求并获取响应
            client.newCall(request).enqueue(callback);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }


    public static void uploadBytesSync(String httpUrl,
                                       String contextPath,
                                       String userId,
                                       int imageWidth,
                                       int imageHeight,
                                       byte[] imageData,
                                       Callback callback) {
        OkHttpClient client = getOkHttpClient();

        // 创建请求体
        RequestBody body = RequestBody.create(imageData,
                MediaType.parse("application/octet-stream"));
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("userId",userId)
                .addFormDataPart("imageWidth", String.valueOf(imageWidth))
                .addFormDataPart("imageHeight", String.valueOf(imageHeight))
                .addFormDataPart("imageData", "imageData", body)  // 使用byte[]数据
                .build();

        try {
            // 创建请求
            Request request = new Request.Builder()
                .url(httpUrl + contextPath)
                .post(requestBody)
                .build();


            client.newCall(request).enqueue(callback);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }


    public static void uploadBitmapSync(String httpUrl,
                                        String contextPath,
                                        String userId,
                                        Bitmap bitmap,
                                        Callback callback) {
        OkHttpClient client = getOkHttpClient();

        // 将Bitmap转换为byte数组
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
        byte[] bitmapData = byteArrayOutputStream.toByteArray();

        // 创建请求体
        RequestBody body = RequestBody.create(bitmapData, MediaType.parse("image/jpeg"));
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("userId", userId)
                .addFormDataPart("imageData", "imageData", body)  // 使用Bitmap转换的byte[]数据
                .build();

        try {
        // 创建请求
            Request request = new Request.Builder()
                .url(httpUrl + contextPath)
                .post(requestBody)
                .build();


            client.newCall(request).enqueue(callback);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }



    public static void uploadBytesAndBitmapSync(String httpUrl,
                                       String contextPath,
                                       String userId,
                                       int imageWidth,
                                       int imageHeight,
                                       byte[] imageData,
                                       Bitmap bitmap,
                                       Callback callback) {
        OkHttpClient client = getOkHttpClient();
        // 将Bitmap转换为byte数组
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
        byte[] bitmapData = byteArrayOutputStream.toByteArray();

        // 创建请求体
        RequestBody imageDataBody = RequestBody.create(imageData,
                MediaType.parse("application/octet-stream"));
        RequestBody bitmapDataBody = RequestBody.create(bitmapData,
                MediaType.parse("image/jpeg"));

        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("userId",userId)
                .addFormDataPart("imageWidth", String.valueOf(imageWidth))
                .addFormDataPart("imageHeight", String.valueOf(imageHeight))
                .addFormDataPart("imageData", "imageData", imageDataBody)
                .addFormDataPart("bitmapData", "bitmapData", bitmapDataBody)
                .build();

        try {
        // 创建请求
            Request request = new Request.Builder()
                .url(httpUrl + contextPath)
                .post(requestBody)
                .build();


            client.newCall(request).enqueue(callback);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }


    /**
     * GET 方法（实例方法）
     */
    public void get(String url, Callback callback) {
        OkHttpClient client = getOkHttpClient();
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .get().build();
            client.newCall(request).enqueue(callback);
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
        }
    }
    
    /**
     * 下载文件方法
     */
    public Call downloadFile(String url, Callback callback) {
        OkHttpClient client = getOkHttpClient();
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .get().build();
            Call call = client.newCall(request);
            call.enqueue(callback);
            return call;
        } catch (Exception e) {
            LogUtils.eTag(TAG, Log.getStackTraceString(e));
            return null;
        }
    }

    @NotNull
    private static OkHttpClient getOkHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .addInterceptor(new HttpRetryInterceptor())
                .build();
    }


    public static void main(String[] args) {
        String httpUrl = "http://127.0.0.1:9898";
        String contextPath = "/api/smart-needle/upload";
        String filePath = "d:/worker-demo.png";
        Callback callback = new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                String errorMessage = "文件上传失败，" + e.getMessage();
                LogUtils.e(errorMessage);
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                assert response.body() != null;
                String responseBody = response.body().string();
                try {
                    JSONObject respData = JSON.parseObject(responseBody);
                    boolean success = respData.getBooleanValue("success");
                    if (success) {
                        LogUtils.i("文件上传成功,返回路径：" + respData.getString("message"));
                    } else {
                        String errorMessage = "文件上传失败：" + respData.getString("message");
                        LogUtils.e(errorMessage);
                    }
                } catch (Exception e) {
                    String errorMessage = "文件上传失败，数据解析错误：" + Log.getStackTraceString(e);
                    LogUtils.e(errorMessage);
                }
            }
        };
        uploadFileSync(httpUrl, contextPath, filePath, callback);
    }

}
