package com.supreme.smart.sewing.update;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.utils.HttpUtils;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

/**
 * 更新管理器
 * Update Manager
 */
public class UpgradeManager {
    
    private static final String TAG = "UpdateManager";
    private static UpgradeManager instance;
    private Context context;
    private ExecutorService executorService;
    private Handler mainHandler;
    
    // 更新检查回调接口
    public interface UpdateCheckCallback {
        void onUpdateAvailable(UpgradeInfo upgradeInfo);
        void onNoUpdate();
        void onError(String error);
    }
    
    private UpgradeManager(Context context) {
        this.context = context.getApplicationContext();
        this.executorService = Executors.newCachedThreadPool();
        this.mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public static synchronized UpgradeManager getInstance(Context context) {
        if (instance == null) {
            instance = new UpgradeManager(context);
        }
        return instance;
    }
    
    /**
     * 检查更新
     * @param updateUrl 更新检查URL
     * @param callback 回调接口
     */
    public void checkUpdate(String updateUrl, UpdateCheckCallback callback) {
        LogUtils.dTag(TAG, "开始检查更新: " + updateUrl);
        
        HttpUtils.getInstance().get(updateUrl, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                LogUtils.eTag(TAG, "检查更新失败", e);
                mainHandler.post(() -> callback.onError("网络连接失败: " + e.getMessage()));
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        LogUtils.dTag(TAG, "更新检查响应: " + responseBody);
                        
                        // 解析JSON响应
                        UpgradeInfo upgradeInfo = JSON.parseObject(responseBody, UpgradeInfo.class);
                        
                        if (upgradeInfo != null) {
                            // 检查是否需要更新
                            int currentVersionCode = getCurrentVersionCode();
                            if (upgradeInfo.getVersionCode() > currentVersionCode) {
                                LogUtils.dTag(TAG, "发现新版本: " + upgradeInfo.getVersionName());
                                mainHandler.post(() -> callback.onUpdateAvailable(upgradeInfo));
                            } else {
                                LogUtils.dTag(TAG, "已是最新版本");
                                mainHandler.post(callback::onNoUpdate);
                            }
                        } else {
                            mainHandler.post(() -> callback.onError("解析更新信息失败"));
                        }
                    } else {
                        mainHandler.post(() -> callback.onError("服务器响应错误: " + response.code()));
                    }
                } catch (Exception e) {
                    LogUtils.eTag(TAG, "处理更新响应失败", e);
                    mainHandler.post(() -> callback.onError("处理响应失败: " + e.getMessage()));
                }
            }
        });
    }
    
    /**
     * 显示更新对话框
     * @param upgradeInfo 更新信息
     */
    public void showUpdateDialog(UpgradeInfo upgradeInfo) {
        Intent intent = new Intent(context, UpgradeActivity.class);
        intent.putExtra("updateInfo", upgradeInfo);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
    
    /**
     * 获取当前应用版本号
     * @return 版本号
     */
    public int getCurrentVersionCode() {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            LogUtils.eTag(TAG, "获取版本号失败", e);
            return 0;
        }
    }
    
    /**
     * 获取当前应用版本名称
     * @return 版本名称
     */
    public String getCurrentVersionName() {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            LogUtils.eTag(TAG, "获取版本名称失败", e);
            return "未知";
        }
    }
    
    /**
     * 创建示例更新信息（用于测试）
     * @return 示例更新信息
     */
    public UpgradeInfo createSampleUpdateInfo() {
        UpgradeInfo upgradeInfo = new UpgradeInfo();
        upgradeInfo.setVersionName(getCurrentVersionName());
        upgradeInfo.setVersionCode(getCurrentVersionCode() + 1);
        upgradeInfo.setDownloadUrl("https://example.com/app-release.apk");
        upgradeInfo.setUpdateContent("1. 新增APP自动更新功能\n2. 优化用户界面设计\n3. 修复已知问题\n4. 提升应用性能和稳定性\n5. 增强安全性");
        upgradeInfo.setFileSize(25 * 1024 * 1024); // 25MB
        upgradeInfo.setForceUpdate(false);
        upgradeInfo.setMd5("d41d8cd98f00b204e9800998ecf8427e");
        return upgradeInfo;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}