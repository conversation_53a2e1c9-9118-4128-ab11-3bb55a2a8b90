package com.supreme.smart.sewing.voice.voice;

import android.app.Activity;
import android.content.Context;
import android.media.AudioManager;
import android.media.ToneGenerator;
import android.os.Handler;
import android.text.TextUtils;
import android.view.MotionEvent;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.voice.ability.AbilityCallback;
import com.supreme.smart.sewing.voice.ability.EdEnCnHelper;
import com.supreme.smart.sewing.voice.component.VoiceInputDialog;
import com.supreme.smart.sewing.voice.component.VoiceUsageType;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 语音识别执行器
 * 提供统一的语音识别逻辑，支持不同的使用场景
 */
public class VoiceRecognitionExecutor {

    private static final String TAG = "VoiceExecutor";

    // 优化配置常量
    private static final long DEBOUNCE_DELAY_MS = 300;        // 防抖延迟（毫秒）
    private static final long MIN_RECORDING_TIME_MS = 500;    // 最短录音时长（毫秒）
    private static final long MAX_RECORDING_TIME_MS = 60000;  // 最长录音时长（毫秒）

    // 实例变量
    private VoiceRecognitionHelper voiceRecognitionHelper;
    private VoiceRecognitionCallback currentCallback;
    private VoiceInputDialog voiceInputDialog;
    private Context context;
    private Activity activity;

    // 状态管理
    private String lastRecognitionText = "";
    private VoiceRecognitionAction currentAction = VoiceRecognitionAction.NONE;
    private VoiceUsageType currentUsageType = VoiceUsageType.VOICE_CHAT;

    // 触摸事件处理
    private float touchStartX;
    private float touchStartY;
    private boolean isRecording = false;

    // 音频数据收集（聊天场景）
    private final List<byte[]> audioDataBuffer;
    private boolean isCollectingAudio = false;

    // 实例标识
    private final String instanceId;

    // === 新增：优化功能相关变量 ===
    private long lastTouchDownTime = 0;           // 上次按下时间
    private long recordingStartTime = 0;          // 录音开始时间
    private boolean isDebouncing = false;         // 是否在防抖期间
    private final Handler mainHandler;            // 主线程Handler
    private Runnable recordingTimeoutRunnable;   // 录音超时任务
    private boolean vadEnded = false;             // VAD是否已结束

    // === 语音转文字相关变量 ===
    private boolean isConvertingVoiceToText = false;  // 是否正在转换
    private String convertRecognitionText = "";       // 转换识别结果
    private EdEnCnHelper currentConvertHelper = null; // 当前转换使用的Helper

    /**
     * 构造函数
     */
    public VoiceRecognitionExecutor() {
        this.audioDataBuffer = new ArrayList<>();
        this.instanceId = "VoiceExecutor_" + System.currentTimeMillis();
        this.mainHandler = new Handler(android.os.Looper.getMainLooper());
        LogUtils.dTag(TAG, "创建语音识别执行器实例: " + instanceId);
    }

    /**
     * 初始化语音识别执行器
     *
     * @param activity       Activity实例，用于创建对话框
     * @param callback       识别结果回调
     * @param voiceUsageType 使用场景
     */
    public void initialize(Activity activity, VoiceRecognitionCallback callback, VoiceUsageType voiceUsageType) {
        LogUtils.dTag(TAG, instanceId + " - 初始化语音识别执行器，使用场景: " + voiceUsageType);
        this.activity = activity;
        this.context = activity;
        this.currentCallback = callback;
        this.currentUsageType = voiceUsageType;
        this.audioDataBuffer.clear();

        // 创建语音输入对话框
        createVoiceInputDialog();

        // 初始化语音识别助手
        initializeVoiceRecognitionHelper();
    }

    /**
     * 创建语音输入对话框
     */
    private void createVoiceInputDialog() {
        if (activity != null) {
            LogUtils.dTag(TAG, instanceId + " - 开始创建语音输入对话框");
            voiceInputDialog = new VoiceInputDialog(activity);
            // 设置使用场景类型（转换枚举）
            voiceInputDialog.setUsageType(currentUsageType);
            LogUtils.dTag(TAG, instanceId + " - 语音输入对话框创建完成，使用场景: " + currentUsageType);
        } else {
            LogUtils.wTag(TAG, instanceId + " - Activity为空，无法创建语音输入对话框");
        }
    }

    /**
     * 初始化语音识别助手
     */
    private void initializeVoiceRecognitionHelper() {
        if (voiceRecognitionHelper == null) {
            // 创建一个代理回调类，用于处理音频数据收集
            AbilityCallback proxyCallback = new AbilityCallback() {
                @Override
                public void onAbilityBegin() {
                    LogUtils.dTag(TAG, instanceId + " - 语音识别开始");
                }

                @Override
                public void onAbilityEnd() {
                    LogUtils.dTag(TAG, instanceId + " - 语音识别结束");
                    // VAD结束，立即关闭弹窗
                    vadEnded = true;
                    hideVoiceInputDialog();

                    // 不播放声音，等待用户松开按钮时根据手势决定播放对应的声音
                    LogUtils.dTag(TAG, instanceId + " - VAD结束，等待用户操作决定发送类型");
                }

                @Override
                public void onAbilityError(int code, Throwable e) {
                    LogUtils.eTag(TAG, instanceId + " - 语音识别错误: " + code + ", " + e.getMessage());
                    if (currentCallback != null) {
                        currentCallback.onError("语音识别错误: " + code + ", " + e.getMessage());
                    }
                }

                @Override
                public void onAbilityResult(@NonNull String result) {
                    try {
                        LogUtils.dTag(TAG, instanceId + " - 原始识别结果: " + result);
                        String text = extractTextFromResult(result);

                        if (!TextUtils.isEmpty(text)) {
                            if (result.contains("pgs:")) {
                                lastRecognitionText = text;
                                updateRealTimeText(text);
                                LogUtils.dTag(TAG, instanceId + " - pgs结果: [" + text + "]");
                            } else if (result.contains("plain:")) {
                                if (text.length() > lastRecognitionText.length()) {
                                    lastRecognitionText = text;
                                    updateRealTimeText(lastRecognitionText);
                                    LogUtils.dTag(TAG, instanceId + " - plain结果，使用更长文本: [" + lastRecognitionText + "]");
                                } else if (text.equals("。") || text.equals("，") ||
                                        text.equals("？") || text.equals("！")) {
                                    lastRecognitionText += text;
                                    updateRealTimeText(lastRecognitionText);
                                    LogUtils.dTag(TAG, instanceId + " - plain结果，添加标点: [" + lastRecognitionText + "]");
                                }
                            }
                        } else {
                            LogUtils.dTag(TAG, instanceId + " - 未识别的结果格式，跳过");
                        }
                    } catch (Exception e) {
                        LogUtils.eTag(TAG, instanceId + " - 处理识别结果失败", e);
                    }
                }

                @Override
                public void onVolumeUpdate(int volume) {
                    // 音量更新 - 添加调试日志
                    LogUtils.dTag(TAG, instanceId + " - 收到音量更新: " + volume);
                    if (voiceInputDialog != null) {
                        voiceInputDialog.updateVolume(volume);
                        LogUtils.dTag(TAG, instanceId + " - 已传递音量到对话框: " + volume);
                    } else {
                        LogUtils.wTag(TAG, instanceId + " - 对话框为空，无法传递音量");
                    }
                }
            };

            voiceRecognitionHelper = new VoiceRecognitionHelper(proxyCallback);

            // 设置音频数据回调
            voiceRecognitionHelper.setAudioDataCallback(this::onRecordProgress);
        }
    }

    /**
     * 音频数据收集回调（由VoiceRecognitionHelper调用）
     */
    public void onRecordProgress(byte[] data, int sampleSize, int volume) {
        if (isCollectingAudio && data != null && sampleSize > 0) {
            byte[] audioChunk = new byte[sampleSize];
            System.arraycopy(data, 0, audioChunk, 0, sampleSize);
            audioDataBuffer.add(audioChunk);

            // 每100个块打印一次日志，避免日志过多
            if (audioDataBuffer.size() % 100 == 0) {
                LogUtils.dTag(TAG, instanceId + " - 收集音频数据: " + sampleSize + " 字节，总缓冲区: " + audioDataBuffer.size());
            }
        }
    }

    /**
     * 处理收集到的音频数据
     */
    private byte[] processCollectedAudioData() {
        if (audioDataBuffer.isEmpty()) {
            LogUtils.wTag(TAG, instanceId + " - 没有收集到音频数据");
            return null;
        }

        LogUtils.dTag(TAG, instanceId + " - 开始处理音频数据，缓冲区大小: " + audioDataBuffer.size());

        try {
            // 计算总的音频数据大小
            int totalSize = 0;
            for (byte[] data : audioDataBuffer) {
                totalSize += data.length;
            }

            LogUtils.dTag(TAG, instanceId + " - 总音频数据大小: " + totalSize + " 字节");

            // 检查音频数据是否足够
            if (totalSize < 1600) { // 至少100ms的音频数据
                LogUtils.wTag(TAG, instanceId + " - 音频数据太少: " + totalSize + " 字节");
                return null;
            }

            // 创建WAV文件数据
            byte[] wavData = createWavFile(audioDataBuffer, totalSize);
            if (wavData != null) {
                LogUtils.dTag(TAG, instanceId + " - WAV数据创建成功，大小: " + wavData.length);
                return wavData;
            } else {
                LogUtils.eTag(TAG, instanceId + " - 创建WAV数据失败");
                return null;
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 处理音频数据时发生异常", e);
            return null;
        }
    }

    /**
     * 创建WAV文件数据
     */
    private byte[] createWavFile(List<byte[]> audioDataList, int totalPcmSize) {
        try {
            // WAV文件头部大小为44字节
            int wavFileSize = 44 + totalPcmSize;
            byte[] wavData = new byte[wavFileSize];

            // 填充WAV头部
            int offset = 0;

            // RIFF标识符
            System.arraycopy("RIFF".getBytes(), 0, wavData, offset, 4);
            offset += 4;

            // 文件大小（不包括前8字节）
            writeInt32(wavData, offset, wavFileSize - 8);
            offset += 4;

            // WAVE标识符
            System.arraycopy("WAVE".getBytes(), 0, wavData, offset, 4);
            offset += 4;

            // fmt 子块
            System.arraycopy("fmt ".getBytes(), 0, wavData, offset, 4);
            offset += 4;

            // fmt子块大小
            writeInt32(wavData, offset, 16);
            offset += 4;

            // 音频格式（PCM）
            writeInt16(wavData, offset, 1);
            offset += 2;

            // 声道数
            writeInt16(wavData, offset, 1);
            offset += 2;

            // 采样率
            writeInt32(wavData, offset, 16000);
            offset += 4;

            // 字节率
            writeInt32(wavData, offset, 16000 * 2);
            offset += 4;

            // 块对齐
            writeInt16(wavData, offset, 2);
            offset += 2;

            // 位深度
            writeInt16(wavData, offset, 16);
            offset += 2;

            // data子块
            System.arraycopy("data".getBytes(), 0, wavData, offset, 4);
            offset += 4;

            // data子块大小
            writeInt32(wavData, offset, totalPcmSize);
            offset += 4;

            // 复制PCM数据
            for (byte[] data : audioDataList) {
                System.arraycopy(data, 0, wavData, offset, data.length);
                offset += data.length;
            }

            LogUtils.dTag(TAG, instanceId + " - WAV文件创建成功，总大小: " + wavFileSize + "，PCM数据: " + totalPcmSize);
            return wavData;

        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 创建WAV文件失败", e);
            return null;
        }
    }

    /**
     * 写入32位小端序整数
     */
    private void writeInt32(byte[] data, int offset, int value) {
        data[offset] = (byte) (value & 0xFF);
        data[offset + 1] = (byte) ((value >> 8) & 0xFF);
        data[offset + 2] = (byte) ((value >> 16) & 0xFF);
        data[offset + 3] = (byte) ((value >> 24) & 0xFF);
    }

    /**
     * 写入16位小端序整数
     */
    private void writeInt16(byte[] data, int offset, int value) {
        data[offset] = (byte) (value & 0xFF);
        data[offset + 1] = (byte) ((value >> 8) & 0xFF);
    }

    /**
     * 处理触摸按下事件 - 添加防抖和优化
     */
    public void handleTouchDown(MotionEvent event) {
        long currentTime = System.currentTimeMillis();

        // 1. 防抖处理：避免用户过快连续点击
        if (currentTime - lastTouchDownTime < DEBOUNCE_DELAY_MS) {
            LogUtils.wTag(TAG, instanceId + " - 防抖：忽略过快的连续点击");
            return;
        }

        // 2. 状态检查：确保不在录音中
        if (isRecording || isDebouncing) {
            LogUtils.wTag(TAG, instanceId + " - 状态冲突：当前正在录音或防抖中");
            return;
        }


        lastTouchDownTime = currentTime;
        recordingStartTime = currentTime;
        isDebouncing = true;

        // 延迟一小段时间确保操作有效性
        mainHandler.postDelayed(() -> {
            if (isDebouncing) { // 确保在延迟期间用户没有取消操作
                isDebouncing = false;
                performTouchDown(event);
            }
        }, 100); // 100ms 延迟确保操作稳定性
    }

    /**
     * 实际执行触摸按下操作
     */
    private void performTouchDown(MotionEvent event) {
        try {
            LogUtils.dTag(TAG, instanceId + " - 开始执行触摸按下操作");

            // 检查基础状态
            if (activity == null) {
                LogUtils.eTag(TAG, instanceId + " - Activity为null，无法执行操作");
                if (currentCallback != null) {
                    currentCallback.onError("Activity未初始化");
                }
                return;
            }

            if (voiceRecognitionHelper == null) {
                LogUtils.eTag(TAG, instanceId + " - VoiceRecognitionHelper为null，尝试重新初始化");
                initializeVoiceRecognitionHelper();
                if (voiceRecognitionHelper == null) {
                    LogUtils.eTag(TAG, instanceId + " - 重新初始化VoiceRecognitionHelper失败");
                    if (currentCallback != null) {
                        currentCallback.onError("语音识别助手初始化失败");
                    }
                    return;
                }
            }

            touchStartX = event.getRawX();
            touchStartY = event.getRawY();

            currentAction = VoiceRecognitionAction.SEND_TEXT;
            lastRecognitionText = "";
            audioDataBuffer.clear();
            vadEnded = false;  // 重置VAD结束状态

            playSound("start");

            // 设置按钮位置信息到对话框
            if (voiceInputDialog != null) {
                voiceInputDialog.setTriggerButtonPosition((int) touchStartX, (int) touchStartY);
            } else {
                LogUtils.wTag(TAG, instanceId + " - 语音输入对话框为null，尝试重新创建");
                createVoiceInputDialog();
            }

            showVoiceInputDialog();
            startRecording();

            // 设置录音超时保护
            scheduleRecordingTimeout();

            LogUtils.dTag(TAG, instanceId + " - 触摸按下操作执行完成");
        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 执行触摸按下操作时发生异常", e);
            if (currentCallback != null) {
                currentCallback.onError("触摸操作异常: " + e.getMessage());
            }
        }
    }

    /**
     * 处理触摸移动事件
     */
    public void handleTouchMove(MotionEvent event) {
        if (!isRecording) return;

        float deltaX = event.getRawX() - touchStartX;
        float deltaY = event.getRawY() - touchStartY;

        VoiceRecognitionAction newAction = determineAction(deltaX, deltaY);

        if (newAction != currentAction) {
            currentAction = newAction;
            updateDialogHint(currentAction);

            if (currentCallback != null) {
                currentCallback.onActionChanged(currentAction);
            }
        }
    }

    /**
     * 处理触摸抬起事件 - 添加最短录音时长检查
     */
    public void handleTouchUp() {
        LogUtils.dTag(TAG, instanceId + " - === 触摸结束，当前操作: " + currentAction + " ===");

        // 如果在防抖期间，取消防抖并直接返回
        if (isDebouncing) {
            isDebouncing = false;
            LogUtils.dTag(TAG, instanceId + " - 在防抖期间松开，取消操作");
            return;
        }

        if (!isRecording) {
            LogUtils.wTag(TAG, instanceId + " - 未在录音状态，忽略触摸结束");
            return;
        }

        // 如果VAD已经结束，根据当前操作执行相应处理
        if (vadEnded) {
            LogUtils.dTag(TAG, instanceId + " - VAD已结束，根据手势执行操作: " + currentAction);
            performTouchUp(); // 执行正常的操作处理
            return;
        }

        long currentTime = System.currentTimeMillis();
        long recordingDuration = currentTime - recordingStartTime;

        // 检查最短录音时长
        if (recordingDuration < MIN_RECORDING_TIME_MS) {
            LogUtils.wTag(TAG, instanceId + " - 录音时长过短: " + recordingDuration + "ms，最少需要: " + MIN_RECORDING_TIME_MS + "ms");

            // 显示提示并继续录音
            if (voiceInputDialog != null) {
                voiceInputDialog.showShortRecordingHint("录音时间太短，请继续说话");
            }

            // 延迟一段时间后如果用户没有继续操作，则自动结束
            mainHandler.postDelayed(() -> {
                if (isRecording) {
                    LogUtils.dTag(TAG, instanceId + " - 自动结束短时录音");
                    performTouchUp();
                }
            }, 1000);

            return;
        }

        performTouchUp();
    }

    /**
     * 实际执行触摸抬起操作
     */
    private void performTouchUp() {
        stopRecording();
        executeAction(currentAction);

        currentAction = VoiceRecognitionAction.NONE;
        isRecording = false;

        // 清理定时任务
        cancelRecordingTimeout();
    }

    /**
     * 根据手势确定操作类型
     */
    private VoiceRecognitionAction determineAction(float deltaX, float deltaY) {
        final float SWIPE_THRESHOLD = 150f;

        if (Math.abs(deltaX) < SWIPE_THRESHOLD && Math.abs(deltaY) < SWIPE_THRESHOLD) {
            return VoiceRecognitionAction.SEND_TEXT;
        }

        if (deltaX < -SWIPE_THRESHOLD) {
            return VoiceRecognitionAction.CANCEL;
        } else if (currentUsageType == VoiceUsageType.VOICE_CHAT && deltaY < -SWIPE_THRESHOLD) {
            return VoiceRecognitionAction.SEND_VOICE;
        }

        return VoiceRecognitionAction.SEND_TEXT;
    }

    /**
     * 开始录音
     */
    private void startRecording() {
        try {
            LogUtils.dTag(TAG, instanceId + " - 准备开始录音");

            if (voiceRecognitionHelper == null) {
                LogUtils.eTag(TAG, instanceId + " - VoiceRecognitionHelper为null，无法开始录音");
                if (currentCallback != null) {
                    currentCallback.onError("语音识别助手未初始化");
                }
                return;
            }

            LogUtils.dTag(TAG, instanceId + " - 调用VoiceRecognitionHelper.startRecording()");
            voiceRecognitionHelper.startRecording();
            isRecording = true;
            isCollectingAudio = (currentUsageType == VoiceUsageType.VOICE_CHAT);
            LogUtils.dTag(TAG, instanceId + " - 开始录音成功，音频收集: " + isCollectingAudio);

        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 开始录音失败", e);

            // 重置状态
            isRecording = false;
            isCollectingAudio = false;

            // 隐藏对话框
            hideVoiceInputDialog();

            if (currentCallback != null) {
                currentCallback.onError("开始录音失败: " + e.getMessage());
            }
        }
    }

    /**
     * 停止录音
     */
    private void stopRecording() {
        try {
            if (voiceRecognitionHelper != null && isRecording) {
                voiceRecognitionHelper.stopRecording();
                isCollectingAudio = false;
                LogUtils.dTag(TAG, instanceId + " - 停止录音，共收集 " + audioDataBuffer.size() + " 个音频块");
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 停止录音失败", e);
        }
    }

    /**
     * 执行相应的操作
     */
    private void executeAction(VoiceRecognitionAction action) {
        LogUtils.dTag(TAG, instanceId + " - 执行操作: " + action);

        switch (action) {
            case SEND_TEXT:
                if (!TextUtils.isEmpty(lastRecognitionText)) {
                    playSound("success");
                    LogUtils.dTag(TAG, instanceId + " - 语音转文字完成: " + lastRecognitionText);
                } else {
                    LogUtils.dTag(TAG, instanceId + " - 没有识别到语音");
                }
                break;

            case SEND_VOICE:
                // 处理音频数据并发送
                byte[] collectedAudio = processCollectedAudioData();
                if (collectedAudio != null) {
                    playSound("success");
                    LogUtils.dTag(TAG, instanceId + " - 发送语音消息，音频数据大小: " + collectedAudio.length + "，识别文字: " + lastRecognitionText);
                    if (currentCallback != null)
                        currentCallback.onAudioDataCollected(collectedAudio);
                } else {
                    LogUtils.dTag(TAG, instanceId + " - 音频数据处理失败");
                }
                break;

            case CANCEL:
                playSound("cancel");
                audioDataBuffer.clear();
                lastRecognitionText = "";
                LogUtils.dTag(TAG, instanceId + " - 录音已取消");
                break;

            default:
                LogUtils.dTag(TAG, instanceId + " - 无操作状态");
                break;
        }

        hideVoiceInputDialog();

        if (currentCallback != null) {
            currentCallback.onRecognitionComplete(lastRecognitionText, action);
        }
    }

    /**
     * 显示语音输入对话框
     */
    private void showVoiceInputDialog() {
        if (voiceInputDialog != null) {
            LogUtils.dTag(TAG, instanceId + " - 准备显示语音输入对话框");
            voiceInputDialog.show();
            voiceInputDialog.reset();

            // 确保底部提示正确显示（在reset后再次确认）
            voiceInputDialog.setUsageType(currentUsageType);

            // 启动波形动画 - 这是关键！
            voiceInputDialog.startWaveformAnimation();

            LogUtils.dTag(TAG, instanceId + " - 语音对话框已显示并重置，使用场景: " + currentUsageType + "，波形动画已启动");
        } else {
            LogUtils.wTag(TAG, instanceId + " - 语音输入对话框为空，无法显示");
        }
    }

    /**
     * 隐藏语音输入对话框
     */
    private void hideVoiceInputDialog() {
        if (voiceInputDialog != null && voiceInputDialog.isShowing()) {
            // 停止波形动画
            voiceInputDialog.stopWaveformAnimation();

            voiceInputDialog.dismiss();
            LogUtils.dTag(TAG, instanceId + " - 对话框已关闭，波形动画已停止");
        }
    }

    /**
     * 更新对话框提示
     */
    private void updateDialogHint(VoiceRecognitionAction action) {
        if (voiceInputDialog == null) return;

        switch (action) {
            case CANCEL:
                voiceInputDialog.showHint(VoiceInputDialog.HintType.CANCEL);
                voiceInputDialog.updateCancelHintText("松开取消");
                break;
            case SEND_VOICE:
                if (currentUsageType == VoiceUsageType.VOICE_CHAT) {
                    voiceInputDialog.showHint(VoiceInputDialog.HintType.SEND_VOICE);
                    voiceInputDialog.updateSendVoiceHintText("松开发送语音");
                }
                break;
            default:
                voiceInputDialog.reset();
                break;
        }
    }


    /**
     * 从识别结果中提取文本
     */
    private String extractTextFromResult(String result) {
        try {
            if (result.contains("pgs:")) {
                String[] lines = result.split("\n");
                for (int i = 0; i < lines.length; i++) {
                    if (lines[i].trim().equals("pgs:") && i + 1 < lines.length) {
                        return lines[i + 1].trim();
                    }
                }
                return "";
            }

            if (result.contains("plain:")) {
                int startIndex = result.indexOf("plain:") + 6;
                String jsonText = result.substring(startIndex).trim();

                if (jsonText.startsWith("{")) {
                    JSONObject json = new JSONObject(jsonText);
                    if (json.has("ws")) {
                        JSONArray wsArray = json.getJSONArray("ws");
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < wsArray.length(); i++) {
                            JSONObject ws = wsArray.getJSONObject(i);
                            if (ws.has("w")) {
                                sb.append(ws.getString("w"));
                            }
                        }
                        return sb.toString();
                    }
                }
                return jsonText;
            }

            return "";
        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 提取文本失败", e);
            return "";
        }
    }

    /**
     * 更新实时文本显示
     */
    private void updateRealTimeText(String text) {
        if (voiceInputDialog != null) {
            voiceInputDialog.updateRecognitionText(text);
        }

        if (currentCallback != null) {
            currentCallback.onRealTimeTextUpdate(text);
        }
    }

    /**
     * 播放声音提示
     */
    private void playSound(String type) {
        LogUtils.dTag(TAG, instanceId + " - 播放声音: " + type);
        try {
            ToneGenerator toneGenerator = new ToneGenerator(AudioManager.STREAM_NOTIFICATION, 80);

            switch (type) {
                case "start":
                    toneGenerator.startTone(ToneGenerator.TONE_PROP_BEEP, 200);
                    break;
                case "success":
                    toneGenerator.startTone(ToneGenerator.TONE_CDMA_CONFIRM, 300);
                    break;
                case "cancel":
                    toneGenerator.startTone(ToneGenerator.TONE_CDMA_EMERGENCY_RINGBACK, 400);
                    break;
            }

            LogUtils.dTag(TAG, instanceId + " - 播放" + type + "提示音");

            new Thread(() -> {
                try {
                    Thread.sleep(500);
                    toneGenerator.release();
                } catch (Exception e) {
                    LogUtils.eTag(TAG, instanceId + " - 释放ToneGenerator失败", e);
                }
            }).start();

        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 播放声音失败: " + type, e);
        }
    }

    // === 语音转文字功能 ===

    /**
     * 将音频数据转换为文字
     *
     * @param audioData 音频数据（WAV格式）
     */
    public void convertVoiceToText(byte[] audioData) {
        if (audioData == null || audioData.length == 0) {
            LogUtils.eTag(TAG, instanceId + " - 音频数据为空，无法转换");
            if (currentCallback != null) {
                currentCallback.onVoiceToTextError("音频数据为空");
            }
            return;
        }

        if (isConvertingVoiceToText) {
            LogUtils.wTag(TAG, instanceId + " - 正在转换中，请稍候");
            if (currentCallback != null) {
                currentCallback.onVoiceToTextError("正在转换中，请稍候");
            }
            return;
        }

        LogUtils.dTag(TAG, instanceId + " - 开始语音转文字，音频数据大小: " + audioData.length);
        startVoiceToTextConversion(audioData);
    }

    /**
     * 将音频文件转换为文字
     *
     * @param audioFilePath 音频文件路径
     */
    public void convertVoiceToText(String audioFilePath) {
        if (TextUtils.isEmpty(audioFilePath)) {
            LogUtils.eTag(TAG, instanceId + " - 音频文件路径为空");
            if (currentCallback != null) {
                currentCallback.onVoiceToTextError("音频文件路径为空");
            }
            return;
        }

        try {
            java.io.File audioFile = new java.io.File(audioFilePath);
            if (!audioFile.exists()) {
                LogUtils.eTag(TAG, instanceId + " - 音频文件不存在: " + audioFilePath);
                if (currentCallback != null) {
                    currentCallback.onVoiceToTextError("音频文件不存在");
                }
                return;
            }

            // 读取文件数据
            try (java.io.FileInputStream fis = new java.io.FileInputStream(audioFile)) {
                byte[] audioData = new byte[(int) audioFile.length()];
                int readSize = fis.read(audioData);
                if (readSize > 0) {
                    convertVoiceToText(audioData);
                } else {
                    LogUtils.eTag(TAG, instanceId + " - 读取音频文件失败");
                    if (currentCallback != null) {
                        currentCallback.onVoiceToTextError("读取音频文件失败");
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 读取音频文件异常", e);
            if (currentCallback != null) {
                currentCallback.onVoiceToTextError("读取音频文件异常: " + e.getMessage());
            }
        }
    }

    /**
     * 开始语音转文字转换
     */
    private void startVoiceToTextConversion(byte[] audioData) {
        isConvertingVoiceToText = true;
        convertRecognitionText = "";

        if (currentCallback != null) {
            currentCallback.onVoiceToTextStart();
        }

        // 清理之前的转换Helper（如果存在）
        cleanupConvertHelper();

        // 创建转换专用的EdEnCnHelper
        currentConvertHelper = new EdEnCnHelper(new AbilityCallback() {
            @Override
            public void onAbilityBegin() {
                LogUtils.dTag(TAG, instanceId + " - 转换语音识别开始");
            }

            @Override
            public void onAbilityResult(@NonNull String result) {
                // 处理转换结果
                String text = extractTextFromResult(result);
                if (!TextUtils.isEmpty(text)) {
                    if (result.contains("pgs:")) {
                        convertRecognitionText = text;
                        if (currentCallback != null) {
                            currentCallback.onVoiceToTextProgress(convertRecognitionText);
                        }
                    } else if (result.contains("plain:")) {
                        if (text.length() >= convertRecognitionText.length()) {
                            convertRecognitionText = text;
                        } else if (text.length() == 1 && "，。！？；：".contains(text)) {
                            convertRecognitionText += text;
                        }
                        if (currentCallback != null) {
                            currentCallback.onVoiceToTextProgress(convertRecognitionText);
                        }
                    }
                    LogUtils.dTag(TAG, instanceId + " - 转换识别结果: " + convertRecognitionText);
                }
            }

            @Override
            public void onAbilityEnd() {
                LogUtils.dTag(TAG, instanceId + " - 转换语音识别结束");
                mainHandler.post(() -> onVoiceToTextConversionComplete());
            }

            @Override
            public void onAbilityError(int code, Throwable error) {
                LogUtils.eTag(TAG, instanceId + " - 转换语音识别错误: " + code, error);
                mainHandler.post(() -> onVoiceToTextConversionError("语音识别错误: " + code));
            }

            @Override
            public void onVolumeUpdate(int volume) {
                // 转换时不需要音量更新
            }
        });

        // 在后台线程处理音频数据
        new Thread(() -> {
            try {
                convertFromAudioData(audioData, currentConvertHelper);
            } catch (Exception e) {
                LogUtils.eTag(TAG, instanceId + " - 音频数据转换异常", e);
                mainHandler.post(() -> onVoiceToTextConversionError("音频数据转换异常: " + e.getMessage()));
            }
        }).start();
    }

    /**
     * 从音频数据转换
     */
    private void convertFromAudioData(byte[] audioData, com.supreme.smart.sewing.voice.ability.EdEnCnHelper convertHelper) {
        LogUtils.dTag(TAG, instanceId + " - 使用音频数据进行转换，数据大小: " + audioData.length);

        try {
            // 提取PCM数据（跳过WAV头部）
            if (audioData.length > 44) {
                byte[] pcmData = new byte[audioData.length - 44];
                System.arraycopy(audioData, 44, pcmData, 0, pcmData.length);

                LogUtils.dTag(TAG, instanceId + " - 提取PCM数据大小: " + pcmData.length);

                // 创建输入流
                java.io.ByteArrayInputStream inputStream = new java.io.ByteArrayInputStream(pcmData);

                LogUtils.dTag(TAG, instanceId + " - 开始通过writeStream处理音频数据");
                convertHelper.writeStream(inputStream);
                LogUtils.dTag(TAG, instanceId + " - writeStream处理完成");

            } else {
                LogUtils.eTag(TAG, instanceId + " - 音频数据太小，无法转换");
                mainHandler.post(() -> onVoiceToTextConversionError("音频数据太小，无法转换"));
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, instanceId + " - 处理音频数据失败", e);
            mainHandler.post(() -> onVoiceToTextConversionError("处理音频数据失败: " + e.getMessage()));
        }
    }

    /**
     * 转换完成回调
     */
    private void onVoiceToTextConversionComplete() {
        LogUtils.dTag(TAG, instanceId + " - 语音转文字完成: " + convertRecognitionText);

        isConvertingVoiceToText = false;

        if (currentCallback != null) {
            if (!TextUtils.isEmpty(convertRecognitionText)) {
                currentCallback.onVoiceToTextComplete(convertRecognitionText);
            } else {
                currentCallback.onVoiceToTextComplete("");
            }
        }

        // 清理状态
        convertRecognitionText = "";

        // 清理转换Helper资源
        cleanupConvertHelper();

        // 延迟重新初始化语音识别助手，确保后续录音正常
        mainHandler.postDelayed(this::reinitializeVoiceRecognition, 500);
    }

    /**
     * 转换错误回调
     */
    private void onVoiceToTextConversionError(String errorMessage) {
        LogUtils.eTag(TAG, instanceId + " - 语音转文字失败: " + errorMessage);

        isConvertingVoiceToText = false;

        if (currentCallback != null) {
            currentCallback.onVoiceToTextError(errorMessage);
        }

        // 清理状态
        convertRecognitionText = "";

        // 清理转换Helper资源
        cleanupConvertHelper();

        // 延迟重新初始化语音识别助手，确保后续录音正常
        mainHandler.postDelayed(this::reinitializeVoiceRecognition, 500);
    }

    /**
     * 重新初始化语音识别
     */
    public void reinitializeVoiceRecognition() {
        LogUtils.dTag(TAG, instanceId + " - 重新初始化语音识别助手");

        new Thread(() -> {
            try {
                // 确保当前不在录音状态
                if (isRecording) {
                    LogUtils.wTag(TAG, instanceId + " - 当前正在录音，停止录音后再重新初始化");
                    if (voiceRecognitionHelper != null) {
                        voiceRecognitionHelper.stopRecording();
                    }
                    isRecording = false;
                    isCollectingAudio = false;
                }

                if (voiceRecognitionHelper != null) {
                    LogUtils.dTag(TAG, instanceId + " - 销毁现有的VoiceRecognitionHelper");
                    voiceRecognitionHelper.destroy();
                    voiceRecognitionHelper = null;
                    Thread.sleep(300); // 增加等待时间确保资源完全释放
                }

                if (context != null && activity != null) {
                    LogUtils.dTag(TAG, instanceId + " - 开始重新初始化VoiceRecognitionHelper");
                    initializeVoiceRecognitionHelper();
                    if (voiceRecognitionHelper != null) {
                        LogUtils.dTag(TAG, instanceId + " - 语音识别助手重新初始化完成");
                    } else {
                        LogUtils.eTag(TAG, instanceId + " - 语音识别助手重新初始化后仍为null");
                    }
                } else {
                    LogUtils.eTag(TAG, instanceId + " - Context或Activity为null，无法重新初始化");
                }
            } catch (Exception e) {
                LogUtils.eTag(TAG, instanceId + " - 重新初始化语音识别助手失败", e);
                voiceRecognitionHelper = null; // 确保失败时清空引用
            }
        }).start();
    }

    /**
     * 清理资源 - 添加新的清理项
     */
    public void cleanup() {
        LogUtils.dTag(TAG, instanceId + " - 开始清理资源");

        // 停止所有定时任务
        isDebouncing = false;
        cancelRecordingTimeout();

        // 清理状态
        isRecording = false;
        currentAction = VoiceRecognitionAction.NONE;
        lastTouchDownTime = 0;
        recordingStartTime = 0;
        vadEnded = false;  // 重置VAD结束状态

        // 清理语音转文字状态
        isConvertingVoiceToText = false;
        convertRecognitionText = "";

        // 清理转换Helper资源
        cleanupConvertHelper();

        // 原有清理逻辑
        if (voiceRecognitionHelper != null) {
            voiceRecognitionHelper.destroy();
            voiceRecognitionHelper = null;
        }

        if (voiceInputDialog != null && voiceInputDialog.isShowing()) {
            voiceInputDialog.dismiss();
            voiceInputDialog = null;
        }

        currentCallback = null;
        context = null;
        activity = null;
        audioDataBuffer.clear();

        LogUtils.dTag(TAG, instanceId + " - 资源清理完成");
    }

    /**
     * 清理转换Helper资源
     */
    private void cleanupConvertHelper() {
        if (currentConvertHelper != null) {
            try {
                LogUtils.dTag(TAG, instanceId + " - 开始清理转换Helper资源");
                currentConvertHelper.destroy();
                currentConvertHelper = null;
                LogUtils.dTag(TAG, instanceId + " - 转换Helper资源清理完成");
            } catch (Exception e) {
                LogUtils.eTag(TAG, instanceId + " - 清理转换Helper资源时出错", e);
                currentConvertHelper = null; // 确保引用被清空
            }
        }
    }

    // === 新增：优化功能辅助方法 ===


    /**
     * 设置录音超时保护
     */
    private void scheduleRecordingTimeout() {
        if (recordingTimeoutRunnable != null) {
            cancelRecordingTimeout();
        }

        recordingTimeoutRunnable = () -> {
            if (isRecording) {
                LogUtils.wTag(TAG, instanceId + " - 录音超时，自动停止");
                if (voiceInputDialog != null) {
                    voiceInputDialog.showTimeoutHint("录音时间已达上限，自动结束");
                }

                // 延迟一秒后自动结束
                mainHandler.postDelayed(() -> {
                    if (isRecording) {
                        performTouchUp();
                    }
                }, 1000);
            }
        };

        mainHandler.postDelayed(recordingTimeoutRunnable, MAX_RECORDING_TIME_MS);
    }

    /**
     * 取消录音超时保护
     */
    private void cancelRecordingTimeout() {
        if (recordingTimeoutRunnable != null) {
            mainHandler.removeCallbacks(recordingTimeoutRunnable);
            recordingTimeoutRunnable = null;
        }
    }

} 
