package com.supreme.smart.sewing.update;

import androidx.annotation.NonNull;

import java.io.Serializable;
import java.util.Locale;

/**
 * 更新信息数据模型
 * Update information data model
 */
public class UpgradeInfo implements Serializable {
    
    private String versionName;        // 版本名称
    private int versionCode;           // 版本号
    private String downloadUrl;        // 下载链接
    private String updateContent;      // 更新内容
    private long fileSize;            // 文件大小（字节）
    private boolean forceUpdate;      // 是否强制更新
    private String md5;               // 文件MD5校验值
    
    public UpgradeInfo() {
    }
    
    public UpgradeInfo(String versionName, int versionCode, String downloadUrl,
                       String updateContent, long fileSize, boolean forceUpdate, String md5) {
        this.versionName = versionName;
        this.versionCode = versionCode;
        this.downloadUrl = downloadUrl;
        this.updateContent = updateContent;
        this.fileSize = fileSize;
        this.forceUpdate = forceUpdate;
        this.md5 = md5;
    }
    
    public String getVersionName() {
        return versionName;
    }
    
    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }
    
    public int getVersionCode() {
        return versionCode;
    }
    
    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }
    
    public String getDownloadUrl() {
        return downloadUrl;
    }
    
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }
    
    public String getUpdateContent() {
        return updateContent;
    }
    
    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    public boolean isForceUpdate() {
        return forceUpdate;
    }
    
    public void setForceUpdate(boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }
    
    public String getMd5() {
        return md5;
    }
    
    public void setMd5(String md5) {
        this.md5 = md5;
    }
    
    /**
     * 格式化文件大小
     * @return 格式化后的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + "B";
        } else if (fileSize < 1024 * 1024) {
            return String.format(Locale.getDefault(),"%.1fKB", fileSize / 1024.0);
        } else {
            return String.format(Locale.getDefault(),"%.1fMB", fileSize / (1024.0 * 1024.0));
        }
    }
    
    @NonNull
    @Override
    public String toString() {
        return "UpdateInfo{" +
                "versionName='" + versionName + '\'' +
                ", versionCode=" + versionCode +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", updateContent='" + updateContent + '\'' +
                ", fileSize=" + fileSize +
                ", forceUpdate=" + forceUpdate +
                ", md5='" + md5 + '\'' +
                '}';
    }
}