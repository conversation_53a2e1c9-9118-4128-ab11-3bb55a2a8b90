package com.supreme.smart.sewing.face;

import static com.supreme.smart.sewing.constants.CommonConstant.SYSTEM_SERVER_URL;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.camera.core.CameraSelector;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.google.mlkit.vision.face.Face;
import com.king.camera.scan.AnalyzeResult;
import com.king.camera.scan.CameraScan;
import com.king.camera.scan.config.CameraConfig;
import com.king.mlkit.vision.face.FaceCameraScanActivity;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.utils.ArcSoftFaceUtils;
import com.supreme.smart.sewing.utils.HttpUtils;
import com.supreme.smart.sewing.utils.LocaleHelper;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * 人脸检测
 */
public class FaceDetectionActivity extends FaceCameraScanActivity {
    private static final String TAG = "FaceDetectionActivity";

    private static final String HTTP_URL = SYSTEM_SERVER_URL;
    private static final String CONTEXT_PATH_REGISTER = "/api/face/register";
    private static final String CONTEXT_PATH_RECOGNIZE = "/api/face/recognize";


    /**
     * 是否进行人脸识别
     */
    private final AtomicBoolean faceRecognize = new AtomicBoolean(true);

    /**
     * 用于确保相机预览尺寸只更新一次
     */
    private final AtomicBoolean cameraSizeUpdated = new AtomicBoolean(false);

    /**
     * 用于显示人脸框的覆盖视图
     */
    private FaceOverlayView overlayView;
    
    /**
     * 用于处理人脸框隐藏的Handler和Runnable
     */
    private final Handler faceHandler = new Handler(Looper.getMainLooper());
    private final Runnable clearFaceRunnable = new Runnable() {
        @Override
        public void run() {
            if (overlayView != null) {
                // 使用带动画的清除方法
                overlayView.clearFacesWithAnimation();
            }
        }
    };
    private static final long FACE_CLEAR_DELAY = 150; // 150ms延时清除人脸框
    private static final long MIN_UPDATE_INTERVAL = 20; // 约30fps
    private long lastUpdateTime = 0;
    private final Handler handler = new Handler(Looper.getMainLooper());

    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase)));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        LogUtils.iTag(TAG, "FaceDetectionActivity启动开始");
        long startTime = System.currentTimeMillis();


        // 添加覆盖视图
        overlayView = new FaceOverlayView(this);
        addContentView(overlayView, new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));

        // 在初始化时设置摄像头方向
        getCameraScan().setCameraConfig(new CameraConfig() {
            @NonNull
            @Override
            public CameraSelector options(@NonNull CameraSelector.Builder builder) {
                return builder.requireLensFacing(CameraScan.LENS_FACING_FRONT).build();
            }
        });
        
        long endTime = System.currentTimeMillis();
        LogUtils.iTag(TAG, "FaceDetectionActivity启动完成，耗时: " + (endTime - startTime) + "ms");

    }

    @Override
    public int getLayoutId() {
        return R.layout.face_detection;
    }
    
    @Override
    public void initCameraScan(@NonNull CameraScan<List<Face>> cameraScan) {
        super.initCameraScan(cameraScan);
        // 优化相机配置以提高帧率和性能
        cameraScan.setAnalyzeImage(true);
        
        // 预热相机，减少首次启动延迟
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (cameraScan != null) {
                LogUtils.iTag(TAG, "相机预热完成");
            }
        }, 100);
    }

    @Override
    public void onScanResultCallback(@NonNull AnalyzeResult<List<Face>> result) {
        // 限制更新频率，避免过于频繁的重绘
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUpdateTime < MIN_UPDATE_INTERVAL) {
            return;
        }
        lastUpdateTime = currentTime;
        
        getCameraScan().setAnalyzeImage(false);
        try {
            // 更新相机预览尺寸
            if (!cameraSizeUpdated.get()) {
                int width = result.getImageWidth();
                int height = result.getImageHeight();
                overlayView.setCameraSize(width, height);
                cameraSizeUpdated.set(true);
            }

            List<Face> faces = result.getResult();
            
            // 始终更新人脸框，让动画处理平滑过渡
            overlayView.updateFaces(faces);
            
            if (!faces.isEmpty()) {
                // 取消之前的清除任务
                faceHandler.removeCallbacks(clearFaceRunnable);
                
                // 设置新的延时清除任务
                faceHandler.postDelayed(clearFaceRunnable, FACE_CLEAR_DELAY);

                // 增加人脸识别调用控制
                if (shouldPerformFaceRecognition(currentTime)) {
                    doScanResultCallback(result);
                }

                // 执行人脸识别或注册
              /*  if (result.getBitmap() != null && faceRecognize.get()) {
                    doScanResultCallback(result);
                }*/
            }
        } finally {
            getCameraScan().setAnalyzeImage(true);
        }
    }

    // 增加人脸识别的最小间隔
    private static final long MIN_FACE_RECOGNITION_INTERVAL = 1000; // 1秒间隔
    private long lastFaceRecognitionTime = 0;

    // 连续识别计数器
    private int consecutiveRecognitionCount = 0;
    private static final int MAX_CONSECUTIVE_RECOGNITIONS = 3;

    private boolean shouldPerformFaceRecognition(long currentTime) {
        // 检查时间间隔
        if (currentTime - lastFaceRecognitionTime < MIN_FACE_RECOGNITION_INTERVAL) {
            return false;
        }

        // 检查连续识别次数
        if (consecutiveRecognitionCount >= MAX_CONSECUTIVE_RECOGNITIONS) {
            // 达到最大连续识别次数，暂停一段时间
            if (currentTime - lastFaceRecognitionTime < MIN_FACE_RECOGNITION_INTERVAL * 3) {
                return false;
            }
            consecutiveRecognitionCount = 0; // 重置计数器
        }

        lastFaceRecognitionTime = currentTime;
        consecutiveRecognitionCount++;
        return true;
    }


    private void doScanResultCallback(@NonNull AnalyzeResult<List<Face>> result) {
        if (!result.getResult().isEmpty() && result.getBitmap() != null) {
            final String USER_ID = "123456788";
            Bitmap cameraBitmap = result.getBitmap();
            int width = cameraBitmap.getWidth();
            int height = cameraBitmap.getHeight();
            byte[] nv21Data = ArcSoftFaceUtils.bitmapToNV21(cameraBitmap);
            // 统一回调处理
            Callback uploadCallback = new Callback() {

                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    getCameraScan().setAnalyzeImage(true);
                    overlayView.setRectColor(Color.YELLOW);
                    LogUtils.eTag(TAG, "文件上传失败: " + Log.getStackTraceString(e));
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    getCameraScan().setAnalyzeImage(true);
                    try (ResponseBody body = response.body()) {
                        String responseString = body != null ? body.string() : "" ;
                        LogUtils.iTag(TAG, "响应内容: " + responseString);
                        if (checkResponse(responseString)){
                          overlayView.setRectColor(Color.GREEN);
                          if (!faceRecognize.get()){
                              // 人脸注册成功后，更新提示信息
                              TextView tvHint = findViewById(R.id.tvHint);
                              tvHint.setText(R.string.face_register_completed);
                              tvHint.setTextColor(Color.GREEN);
                              ToastUtils.showLong("人脸注册成功");

                              handler.postDelayed(() -> {
                                  // 3秒后恢复初始状态
                                  tvHint.setText(R.string.face_hint);
                                  tvHint.setTextColor(Color.WHITE);
                              }, 3000);
                          }
                        } else {
                          overlayView.setRectColor(Color.YELLOW);
                        }

                        if (!faceRecognize.get()) faceRecognize.set(true);
                    }
                }

                private boolean checkResponse(String responseString) {
                    if (StringUtils.isEmpty(responseString) )
                        return false;

                    try {
                        JSONObject respData = JSON.parseObject(responseString);
                        return  respData.getBooleanValue("success");
                    } catch (Exception e) {
                        String errorMessage = "文件上传失败，数据解析错误：" + Log.getStackTraceString(e);
                        LogUtils.e(errorMessage);
                    }
                    return false;
                }
            };

            if (faceRecognize.get()) {
                // 人脸识别流程
              //  LogUtils.iTag(TAG,"人脸识别");
                HttpUtils.uploadBytesSync(HTTP_URL, CONTEXT_PATH_RECOGNIZE,
                        USER_ID, width, height, nv21Data, uploadCallback);
            } else {
                // 人脸注册流程
               LogUtils.iTag(TAG,"人脸注册");
                // 显示开始注册提示信息
                TextView tvHint = findViewById(R.id.tvHint);
                tvHint.setText(getString(R.string.face_register_start));
                tvHint.setTextColor(Color.WHITE);
               HttpUtils.uploadBytesAndBitmapSync(HTTP_URL, CONTEXT_PATH_REGISTER,
                        USER_ID, width, height, nv21Data, cameraBitmap, uploadCallback);
            }
        }
    }


    /**
     * 人脸注册按钮点击事件
     */
    public void onRegisterFaceClick(View view) {
        faceRecognize.set(false);
    }

    /**
     * 关闭按钮点击事件
     */
    public void onCloseClick(View view) {
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理定时器，避免内存泄漏
        if (faceHandler != null && clearFaceRunnable != null) {
            faceHandler.removeCallbacks(clearFaceRunnable);
        }
        // 清理覆盖视图的动画
        if (overlayView != null) {
            overlayView.clearFacesImmediately();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 暂停时清理定时器
        if (faceHandler != null && clearFaceRunnable != null) {
            faceHandler.removeCallbacks(clearFaceRunnable);
        }
    }
}