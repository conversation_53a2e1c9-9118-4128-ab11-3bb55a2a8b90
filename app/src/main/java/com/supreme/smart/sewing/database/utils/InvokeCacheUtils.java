package com.supreme.smart.sewing.database.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.supreme.smart.sewing.database.beans.cache.InvokeFailureCache;
import com.supreme.smart.sewing.database.config.RealmConfig;

import io.realm.Realm;
import io.realm.RealmResults;

public class InvokeCacheUtils {
    public synchronized static void save(String contextUrl, String contextPath, JSONObject param) {
        save(contextUrl, contextPath, param == null ? "" : param.toString());
    }

    /**
     * 保存
     *
     * @param contextUrl  URL
     * @param contextPath 上下文路径
     * @param param       参数
     * @return 是否成功
     */
    public synchronized static void save(String contextUrl, String contextPath, String param) {
        Realm realm = Realm.getInstance(RealmConfig.getCacheConfig());
        try {
            realm.beginTransaction();
            InvokeFailureCache element = new InvokeFailureCache();
            element.setContextUrl(contextUrl);
            element.setContextPath(contextPath);
            element.setParam(param);
            realm.copyToRealm(element);
            realm.commitTransaction();

        } catch (Exception e) {
            realm.cancelTransaction();
            e.printStackTrace();
        } finally {
            realm.close();
        }
    }


    /**
     * 获取
     *
     * @param id 键
     * @return 值
     */
    public synchronized static InvokeFailureCache get(String id) {
        try (Realm realm = Realm.getInstance(RealmConfig.getCacheConfig())) {
            return realm.where(InvokeFailureCache.class)
                    .equalTo("id", id).findFirst();
        }
    }

    public synchronized static JSONArray getAll() {
        JSONArray result = new JSONArray();
        try (Realm realm = Realm.getInstance(RealmConfig.getCacheConfig())) {
            RealmResults<InvokeFailureCache> elements = realm.where(InvokeFailureCache.class).findAll();
            for (InvokeFailureCache element : elements) {
                JSONObject item = new JSONObject();
                item.put("id", element.getId());
                item.put("contextUrl", element.getContextUrl());
                item.put("contextPath", element.getContextPath());
                item.put("param", element.getParam());
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 删除
     *
     * @param id 键
     * @return 是否成功
     */
    public synchronized static void delete(String id) {
        final Realm realm = Realm.getInstance(RealmConfig.getCacheConfig());
        try {
            realm.beginTransaction();
            realm.where(InvokeFailureCache.class).equalTo("id", id).findAll().deleteAllFromRealm();
            realm.commitTransaction();
        } catch (Exception e) {
            realm.cancelTransaction();
            e.printStackTrace();
        } finally {
            realm.close();
        }
    }

}
