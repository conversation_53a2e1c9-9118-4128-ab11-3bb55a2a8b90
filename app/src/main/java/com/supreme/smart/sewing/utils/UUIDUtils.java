package com.supreme.smart.sewing.utils;

import java.util.Date;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 */
public class UUIDUtils {
    private static final int MY_IP_ADDRESS = 1076410497;

    /**
     * 是否使用雪花算法
     */
    private static final boolean USE_SNOWFLAKE_ID = true;

    private static final Snowflake snowflake = IdUtil.getSnowflake(7, 7);

    /**
     * 产生一个32位的UUID
     *
     * @return
     */

    public static String getId() {
        if (USE_SNOWFLAKE_ID) {
            try {
                return snowflake.nextIdStr();
            } catch (Exception e) {
                return format(getIP()) +
                        format(getJVM()) + format(getHiTime()) +
                        format(getLoTime()) + format(getCount());
            }
        } else {
            return format(getIP()) +
                    format(getJVM()) + format(getHiTime()) +
                    format(getLoTime()) + format(getCount());
        }
    }

    private static final int IP;

    static {
        int ipadd;
        try {
            ipadd = MY_IP_ADDRESS; // toInt(InetAddress.getLocalHost().getAddress());
        } catch (Exception e) {
            ipadd = 0;
        }
        IP = ipadd;
    }

    private static volatile short counter = (short) 0;

    private static final int JVM = (int) (System.currentTimeMillis() >>> 8);

    private final static String format(int intval) {
        String formatted = Integer.toHexString(intval);
        StringBuilder buf = new StringBuilder("00000000");
        buf.replace(8 - formatted.length(), 8, formatted);
        return buf.toString();
    }

    private final static String format(short shortval) {
        String formatted = Integer.toHexString(shortval);
        StringBuilder buf = new StringBuilder("0000");
        buf.replace(4 - formatted.length(), 4, formatted);
        return buf.toString();
    }

    private final static int getJVM() {
        return JVM;
    }

    private final static short getCount() {
        synchronized (UUIDUtils.class) {
            if (counter < 0)
                counter = 0;
            return counter++;
        }
    }

    /**
     * Unique in a local network
     */
    private final static int getIP() {
        return IP;
    }

    /**
     * Unique down to millisecond
     */
    private final static short getHiTime() {
        return (short) (System.currentTimeMillis() >>> 32);
    }

    private final static int getLoTime() {
        return (int) System.currentTimeMillis();
    }

    @SuppressWarnings("unused")
    private final static int toInt(byte[] bytes) {
        int result = 0;
        for (int i = 0; i < 4; i++) {
            result = (result << 8) - Byte.MIN_VALUE + (int) bytes[i];
        }
        return result;
    }

    public static String getCreateBy() {
        return "8a8ab0b246dc81120146dc8181950052";
    }

    public static String getCreateName() {
        return "admin";
    }

    public static Date getCreateTime() {
        return CommonUtils.getCurrentTime();
    }
    public static Date getCreateDate() {
        return CommonUtils.getCurrentDate();
    }

    public static String getUpdateBy() {
        return "8a8ab0b246dc81120146dc8181950052";
    }

    public static String getUpdateName() {
        return "admin";
    }

    public static Date getUpdateTime() {
        return CommonUtils.getCurrentTime();
    }

}
