package com.supreme.smart.sewing.database.beans.sys;

import java.io.Serializable;
import java.util.Date;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class SysPowerAction extends RealmObject implements Serializable {
    /**
     * 主键 (日期格式)
     */
    @PrimaryKey
    private String id;
    /**
     * 当天首次开机时间
     */
    private Date firstOnTime;
    /**
     * 开机时长
     */
    private Integer duration;
    /**
     * 最近一次时钟
     */
    private Date acTime;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getFirstOnTime() {
        return firstOnTime;
    }

    public void setFirstOnTime(Date firstOnTime) {
        this.firstOnTime = firstOnTime;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Date getAcTime() {
        return acTime;
    }

    public void setAcTime(Date acTime) {
        this.acTime = acTime;
    }
}
