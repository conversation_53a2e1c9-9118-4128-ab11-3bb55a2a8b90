package com.supreme.smart.sewing.ocr;

import android.text.method.ScrollingMovementMethod;
import android.widget.TextView;
import android.widget.Toast;

import com.google.mlkit.vision.text.Text;
import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions;
import com.king.app.dialog.AppDialog;
import com.king.app.dialog.AppDialogConfig;
import com.king.camera.scan.AnalyzeResult;
import com.king.camera.scan.CameraScan;
import com.king.camera.scan.analyze.Analyzer;
import com.king.mlkit.vision.text.TextCameraScanActivity;
import com.king.mlkit.vision.text.analyze.TextRecognitionAnalyzer;
import com.supreme.smart.sewing.R;

/** 
 * 文字识别示例 
 * <AUTHOR> href="mailto:<EMAIL>">Jenly</a> 
 */ 
public class TextRecognitionActivity extends TextCameraScanActivity {
    
    @Override
    public void onScanResultCallback(AnalyzeResult<Text> result) {
        CameraScan<Text> cameraScan = getCameraScan();
        if (result.getResult().getText().isEmpty()) {
            // 识别失败也恢复分析
            cameraScan.setAnalyzeImage(true);
            return;
        }

        cameraScan.setAnalyzeImage(false);

        AppDialogConfig dialog = new AppDialogConfig(this, R.layout.text_result_dialog);
        TextView textView = dialog.getView(R.id.tvDialogContent);
        textView.setMovementMethod(ScrollingMovementMethod.getInstance());
        
        dialog.setContent(result.getResult().getText())
              .setOnClickConfirm(v -> {
                  AppDialog.INSTANCE.dismissDialog();
                  cameraScan.setAnalyzeImage(true);
              })
              .setOnClickCancel(v -> {
                  AppDialog.INSTANCE.dismissDialog();
                  finish();
              });
              
        AppDialog.INSTANCE.showDialog(dialog, false);
    }
    
    @Override
    public Analyzer<Text> createAnalyzer() {
        return new TextRecognitionAnalyzer(new ChineseTextRecognizerOptions.Builder().build());
    }
}