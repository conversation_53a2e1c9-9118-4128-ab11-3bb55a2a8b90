package com.supreme.smart.sewing.voice.adapter;

import java.util.Date;
import java.util.Locale;

/**
 * 聊天消息数据模型
 */
public class ChatMessage {
    public static final int TYPE_TEXT = 0;
    public static final int TYPE_VOICE = 1;
    
    private String id;
    private String content;
    private int type; // 0: 文本, 1: 语音
    private String audioFilePath; // 语音文件路径
    private byte[] audioData; // 语音数据（内存模式）
    private long duration; // 语音时长（毫秒）
    private Date timestamp;
    private boolean isPlaying; // 是否正在播放
    
    // 语音转文字相关
    private String convertedText; // 语音转换后的文字
    private boolean showConvertedText; // 是否显示转换后的文字
    private boolean isConverting; // 是否正在转换中
    
    public ChatMessage() {
        this.id = String.valueOf(System.currentTimeMillis());
        this.timestamp = new Date();
        this.isPlaying = false;
    }
    
    public ChatMessage(String content, int type) {
        this();
        this.content = content;
        this.type = type;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public int getType() {
        return type;
    }
    
    public void setType(int type) {
        this.type = type;
    }
    
    public String getAudioFilePath() {
        return audioFilePath;
    }
    
    public void setAudioFilePath(String audioFilePath) {
        this.audioFilePath = audioFilePath;
    }
    
    public byte[] getAudioData() {
        return audioData;
    }
    
    public void setAudioData(byte[] audioData) {
        this.audioData = audioData;
    }
    
    public long getDuration() {
        return duration;
    }
    
    public void setDuration(long duration) {
        this.duration = duration;
    }
    
    public Date getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }
    
    public boolean isPlaying() {
        return isPlaying;
    }
    
    public void setPlaying(boolean playing) {
        isPlaying = playing;
    }
    
    /**
     * 获取格式化的时间戳
     */
    public String getFormattedTime() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("HH:mm", Locale.US);
        return sdf.format(timestamp);
    }
    
    /**
     * 获取语音时长的格式化字符串
     */
    public String getFormattedDuration() {
        if (duration <= 0) return "0''";
        long seconds = duration / 1000;
        return seconds + "''";
    }
    
    /**
     * 是否为文本消息
     */
    public boolean isTextMessage() {
        return type == TYPE_TEXT;
    }
    
    /**
     * 是否为语音消息
     */
    public boolean isVoiceMessage() {
        return type == TYPE_VOICE;
    }
    
    // 语音转文字相关方法
    
    /**
     * 获取转换后的文字
     */
    public String getConvertedText() {
        return convertedText;
    }
    
    /**
     * 设置转换后的文字
     */
    public void setConvertedText(String convertedText) {
        this.convertedText = convertedText;
    }
    
    /**
     * 是否显示转换后的文字
     */
    public boolean isShowConvertedText() {
        return showConvertedText;
    }
    
    /**
     * 设置是否显示转换后的文字
     */
    public void setShowConvertedText(boolean showConvertedText) {
        this.showConvertedText = showConvertedText;
    }
    
    /**
     * 是否正在转换中
     */
    public boolean isConverting() {
        return isConverting;
    }
    
    /**
     * 设置是否正在转换中
     */
    public void setConverting(boolean converting) {
        isConverting = converting;
    }
    
    /**
     * 是否有转换后的文字
     */
    public boolean hasConvertedText() {
        return convertedText != null && !convertedText.trim().isEmpty();
    }
}
