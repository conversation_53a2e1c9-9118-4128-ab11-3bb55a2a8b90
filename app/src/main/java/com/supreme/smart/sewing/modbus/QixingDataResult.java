package com.supreme.smart.sewing.modbus;

import com.supreme.smart.sewing.utils.CommonUtils;

import java.util.Arrays;

/**
 * 琦星电控数据结果类
 * 用于存储并行读取的多个Modbus数据
 */
public class QixingDataResult {
    // 针距数据
    private byte[] needleGapData;
    private String needleGapError;

    // 计数器数据（针数、计件数、剪线次数、抬压脚次数）
    private byte[] counterData;
    private String counterError;

    // 状态数据（设备状态、错误码）
    private byte[] statusData;
    private String statusError;

    // 速度数据（最高转速）
    private byte[] speedData;
    private String speedError;

    // 发送和接收的命令数量跟踪
    private int sentCommandCount = 0;
    private int receivedCommandCount = 0;

    // Getters and Setters
    public byte[] getNeedleGapData() {
        return needleGapData;
    }


    public void setNeedleGapData(byte[] needleGapData) {
        this.needleGapData = needleGapData;
    }

    /**
     * 获取针距值
     * 针距数据是一个16位的无符号整数
     *
     * @return 针距值
     */
    public int getNeedleGapValue() {
        if (needleGapData != null) {
            return CommonUtils.byteArrayToUnsigned16(needleGapData, true);
        }
        return 0;
    }

    public String getNeedleGapError() {
        return needleGapError;
    }

    public void setNeedleGapError(String needleGapError) {
        this.needleGapError = needleGapError;
    }

    public byte[] getCounterData() {
        return counterData;
    }

    public void setCounterData(byte[] counterData) {
        this.counterData = counterData;
    }

    /**
     * 取得开机后的针数
     * 针数数据是一个32位的无符号整数
     *
     * @return 计数器值数组
     */
    public Long getNeedleCountValue() {
        if (counterData != null && counterData.length >= 4) {
            byte[] bytes = Arrays.copyOfRange(counterData, 0, 4);
            return CommonUtils.byteArrayToUnsigned32(bytes, true);
        }

        return null;
    }

    /**
     * 取得累计计件数
     * 计件数数据是一个16位的无符号整数
     *
     * @return 计件数 (范围：0-9999)
     */
    public Long getPieceCountValue() {
        if (counterData != null && counterData.length >= 6) {
            byte[] bytes = Arrays.copyOfRange(counterData, 4, 6);
            return (long) CommonUtils.byteArrayToUnsigned16(bytes, true);
        }
        return null;
    }

    /**
     * 取得累计剪线次数
     * 剪线次数数据是一个16位的无符号整数
     *
     * @return 剪线次数 (范围：0-65535)
     */
    public Long getCutCountValue() {
        if (counterData != null && counterData.length >= 8) {
            byte[] bytes = Arrays.copyOfRange(counterData, 6, 8);
            return (long) CommonUtils.byteArrayToUnsigned16(bytes, true);
        }
        return null;
    }

    /**
     * 取得累计抬压脚次数
     * 抬压脚次数数据是一个16位的无符号整数
     *
     * @return 抬压脚次数 (范围：0-65535)
     */
    public Long getRaiseCountValue() {
        if (counterData != null && counterData.length >= 10) {
            byte[] bytes = Arrays.copyOfRange(counterData, 8, 10);
            return (long) CommonUtils.byteArrayToUnsigned16(bytes, true);
        }
        return null;
    }

    public String getCounterError() {
        return counterError;
    }

    public void setCounterError(String counterError) {
        this.counterError = counterError;
    }

    public byte[] getStatusData() {
        return statusData;
    }

    public void setStatusData(byte[] statusData) {
        this.statusData = statusData;
    }

    /**
     * 取得设备状态
     * 设备状态数据是一个8位的无符号整数
     *
     * @return 设备状态
     */
    public int getDeviceStatusValue() {
        if (statusData != null && statusData.length >= 4) {
            byte[] bytes = Arrays.copyOfRange(statusData, 0, 2);
            return  CommonUtils.byteArrayToUnsigned16(bytes, true);
        }
        return 0;
    }

    /**
     * 取得错误码
     * 错误码数据是一个8位的无符号整数
     *
     * @return 错误码
     */
    public int getErrorCodeValue() {
        if (statusData!= null && statusData.length >= 4) {
            byte[] bytes = Arrays.copyOfRange(statusData, 2, 4);
            return  CommonUtils.byteArrayToUnsigned16(bytes, true);
        }
        return 0;
    }


    public String getStatusError() {
        return statusError;
    }

    public void setStatusError(String statusError) {
        this.statusError = statusError;
    }

    public byte[] getSpeedData() {
        return speedData;
    }

    public void setSpeedData(byte[] speedData) {
        this.speedData = speedData;
    }

    /**
     * 取得最高转速
     * 最高转速数据是一个16位的无符号整数
     *
     * @return 最高转速
     */
    public int getMaxSpeedValue() {
        if (speedData != null && speedData.length >= 2) {
            return CommonUtils.byteArrayToUnsigned16(speedData, true);
        }
        return 0;
    }


    public String getSpeedError() {
        return speedError;
    }

    public void setSpeedError(String speedError) {
        this.speedError = speedError;
    }

    /**
     * 检查是否所有数据都读取成功
     */
    public boolean isAllSuccess() {
        return needleGapData != null && counterData != null && statusData != null && speedData != null ;
    }

    /**
     * 设置发送的命令数量
     */
    public void setSentCommandCount(int sentCommandCount) {
        this.sentCommandCount = sentCommandCount;
    }

    /**
     * 获取发送的命令数量
     */
    public int getSentCommandCount() {
        return sentCommandCount;
    }

    /**
     * 设置接收的成功响应数量
     * 只有当Modbus响应的isSuccess为true时才计入
     */
    public void setReceivedCommandCount(int receivedCommandCount) {
        this.receivedCommandCount = receivedCommandCount;
    }

    /**
     * 获取接收的成功响应数量
     */
    public int getReceivedCommandCount() {
        return receivedCommandCount;
    }

    /**
     * 检查发送的命令数和收到的成功响应数是否一致
     * 只有当Modbus响应的isSuccess为true时才计入receivedCommandCount成功响应数
     */
    public boolean isSentAndReceivedConsistent() {
        return sentCommandCount > 0 && sentCommandCount == receivedCommandCount;
    }

    /**
     * 从另一个QixingDataResult对象复制所有数据
     * 用于智能轮询中保持历史数据
     *
     * @param other 要复制的源对象
     */
    public void copyFrom(QixingDataResult other) {
        if (other == null) {
            return;
        }

        // 复制针距数据
        this.needleGapData = other.needleGapData != null ? Arrays.copyOf(other.needleGapData, other.needleGapData.length) : null;
        this.needleGapError = other.needleGapError;

        // 复制计数器数据
        this.counterData = other.counterData != null ? Arrays.copyOf(other.counterData, other.counterData.length) : null;
        this.counterError = other.counterError;

        // 复制状态数据
        this.statusData = other.statusData != null ? Arrays.copyOf(other.statusData, other.statusData.length) : null;
        this.statusError = other.statusError;

        // 复制速度数据
        this.speedData = other.speedData != null ? Arrays.copyOf(other.speedData, other.speedData.length) : null;
        this.speedError = other.speedError;

        // 复制命令计数
        this.sentCommandCount = other.sentCommandCount;
        this.receivedCommandCount = other.receivedCommandCount;
    }
}