package com.supreme.smart.sewing.push;

import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_CLIENT_ID;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_PASSWORD;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_SERVER_HOST;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_SERVER_PORT;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_TOPIC_PREFIX;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_USERNAME;

import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.Utils;
import com.hivemq.client.mqtt.MqttClient;
import com.hivemq.client.mqtt.mqtt3.Mqtt3AsyncClient;
import com.hivemq.client.mqtt.mqtt3.message.connect.connack.Mqtt3ConnAck;
import com.hivemq.client.mqtt.mqtt3.message.publish.Mqtt3Publish;
import com.supreme.smart.sewing.push.PowerOptimizationConfig.MqttConfig;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * MQTT连接管理器
 * 负责MQTT连接、重连、消息订阅和发布
 */
public class MqttConnectionManager {

    private static final String TAG = "MqttConnectionManager";

    // 连接状态
    public enum ConnectionState {
        DISCONNECTED, CONNECTING, CONNECTED
    }

    private ConnectionState connectionState = ConnectionState.DISCONNECTED;
    private Mqtt3AsyncClient mqttClient;

    // 电源优化配置
    private PowerOptimizationConfig powerConfig;
    
    // 回调接口
    public interface ConnectionCallback {
        void onConnected();
        void onDisconnected();
        void onConnectionFailed(Throwable throwable);
        void onMessageReceived(String topic, String message);
    }
    
    private ConnectionCallback callback;
    
    public MqttConnectionManager() {
        // 构造函数
    }
    
    /**
     * 设置连接回调
     */
    public void setConnectionCallback(ConnectionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 初始化MQTT客户端
     */
    public void initialize() {
        if (connectionState != ConnectionState.DISCONNECTED) {
            return;
        }

        try {
            // 初始化电源优化配置
            powerConfig = PowerOptimizationConfig.getInstance(Utils.getApp());
            MqttConfig mqttConfig = powerConfig.getMqttConfig();
            mqttClient = MqttClient.builder()
                    .useMqttVersion3()
                    .identifier(MQTT_CLIENT_ID)
                    .serverHost(MQTT_SERVER_HOST)
                    .serverPort(MQTT_SERVER_PORT)
                    .automaticReconnect()
                        .initialDelay(mqttConfig.reconnectInitialDelay, TimeUnit.MILLISECONDS)
                        .maxDelay(mqttConfig.reconnectMaxDelay, TimeUnit.MILLISECONDS)
                        .applyAutomaticReconnect()
                    .transportConfig()
                        .socketConnectTimeout(mqttConfig.socketConnectTimeout, TimeUnit.MILLISECONDS)
                        .mqttConnectTimeout(mqttConfig.mqttConnectTimeout, TimeUnit.MILLISECONDS)
                        .applyTransportConfig()
                    .buildAsync();

            connect();
        } catch (Exception e) {
            LogUtils.eTag(TAG, "初始化MQTT客户端失败", e);
            if (callback != null) {
                callback.onConnectionFailed(e);
            }
        }
    }
    
    /**
     * 连接MQTT服务器
     */
    private void connect() {
        if (connectionState == ConnectionState.CONNECTING || connectionState == ConnectionState.CONNECTED) {
            return;
        }
        
        if (!NetworkUtils.isConnected()) {
            LogUtils.wTag(TAG, "网络未连接，HiveMQ客户端会自动重连");
            return;
        }
        
        connectionState = ConnectionState.CONNECTING;

        // 获取当前应该使用的心跳间隔（根据电源状态动态调整）
        int keepAliveInterval = powerConfig != null ? powerConfig.getCurrentKeepAliveInterval() : 60;
        LogUtils.dTag(TAG, "开始连接MQTT服务器，心跳间隔: " + keepAliveInterval + "秒");

        CompletableFuture<Mqtt3ConnAck> connectFuture = mqttClient.connectWith()
                .keepAlive(keepAliveInterval)
                .cleanSession(true)
                .simpleAuth()
                .username(MQTT_USERNAME)
                .password(MQTT_PASSWORD.getBytes(StandardCharsets.UTF_8))
                .applySimpleAuth()
                .send();
        
        connectFuture.whenComplete((connAck, throwable) -> {
            if (throwable == null) {
                onMqttConnected();
            } else {
                onMqttConnectionFailed(throwable);
            }
        });
    }
    
    /**
     * MQTT连接成功回调
     */
    private void onMqttConnected() {
        LogUtils.dTag(TAG, "MQTT连接成功");
        connectionState = ConnectionState.CONNECTED;
        
        // 订阅主题
        subscribeToTopics();
        
        if (callback != null) {
            callback.onConnected();
        }
    }
    
    /**
     * MQTT连接失败回调
     */
    private void onMqttConnectionFailed(Throwable throwable) {
        LogUtils.eTag(TAG, "MQTT连接失败", throwable);
        connectionState = ConnectionState.DISCONNECTED;
        
        if (callback != null) {
            callback.onConnectionFailed(throwable);
        }
    }
    
    /**
     * 订阅MQTT主题
     */
    private void subscribeToTopics() {
        String deviceId = DeviceUtils.getAndroidID();
        
        // 订阅设备专用主题
        String deviceTopic = MQTT_TOPIC_PREFIX + "device/" + deviceId;
        subscribeToTopic(deviceTopic);
        
        // 订阅广播主题
        String broadcastTopic = MQTT_TOPIC_PREFIX + "broadcast";
        subscribeToTopic(broadcastTopic);
        
        // 订阅机器警报主题
        String alertTopic = MQTT_TOPIC_PREFIX + "alert/" + deviceId;
        subscribeToTopic(alertTopic);
    }
    
    /**
     * 订阅单个主题
     */
    private void subscribeToTopic(String topic) {
        mqttClient.subscribeWith()
                .topicFilter(topic)
                .callback(this::onMessageReceived)
                .send()
                .whenComplete((subAck, throwable) -> {
                    if (throwable == null) {
                        LogUtils.dTag(TAG, "订阅主题[" + topic + "]成功");
                    } else {
                        LogUtils.eTag(TAG, "订阅主题[" + topic + "]失败", throwable);
                    }
                });
    }
    
    /**
     * 收到MQTT消息回调
     */
    private void onMessageReceived(Mqtt3Publish publish) {
        try {
            String topic = publish.getTopic().toString();
            String message = new String(publish.getPayloadAsBytes(), StandardCharsets.UTF_8);

            LogUtils.dTag(TAG, "收到MQTT消息 - 主题: " + topic + ", 内容: " + message);

            if (callback != null) {
                callback.onMessageReceived(topic, message);
            }
            
        } catch (Exception e) {
            LogUtils.eTag(TAG, "处理MQTT消息失败", e);
        }
    }
    
    /**
     * 手动重新连接
     */
    public void reconnect() {
        LogUtils.dTag(TAG, "手动重连MQTT");
        disconnect();
        // 延迟重新初始化
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(this::initialize, 1000);
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        LogUtils.dTag(TAG, "断开MQTT连接");
        connectionState = ConnectionState.DISCONNECTED;
        
        if (mqttClient != null) {
            mqttClient.disconnect().whenComplete((unused, throwable) -> {
                if (throwable == null) {
                    LogUtils.dTag(TAG, "MQTT断开连接成功");
                } else {
                    LogUtils.eTag(TAG, "MQTT断开连接失败", throwable);
                }
                
                if (callback != null) {
                    callback.onDisconnected();
                }
            });
        }
    }
    
    /**
     * 获取连接状态
     */
    public boolean isConnected() {
        return connectionState == ConnectionState.CONNECTED;
    }
    
    /**
     * 获取当前连接状态
     */
    public ConnectionState getConnectionState() {
        return connectionState;
    }
    
    /**
     * 发布消息
     */
    public void publishMessage(String topic, String message) {
        if (connectionState != ConnectionState.CONNECTED) {
            LogUtils.wTag(TAG, "MQTT未连接，无法发布消息");
            return;
        }
        
        mqttClient.publishWith()
                .topic(topic)
                .payload(message.getBytes(StandardCharsets.UTF_8))
                .send()
                .whenComplete((publishResult, throwable) -> {
                    if (throwable == null) {
                        LogUtils.dTag(TAG, "消息发布成功: " + topic);
                    } else {
                        LogUtils.eTag(TAG, "消息发布失败: " + topic, throwable);
                    }
                });
    }
    
    /**
     * 释放资源
     */
    public void release() {
        disconnect();
        mqttClient = null;
        callback = null;
    }
}
