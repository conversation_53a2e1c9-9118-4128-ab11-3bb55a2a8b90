package com.supreme.smart.sewing.modbus;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能轮询配置类
 * 用于配置不同数据类型的轮询间隔，支持灵活扩展
 * 
 * <AUTHOR>
 * @since 2024
 */
public class PollingConfig {
    
    /**
     * 数据类型枚举
     */
    public enum DataType {
        NEEDLE_GAP("针距数据", 131, 1),
        COUNTER("计数器数据", 2102, 5),
        STATUS("状态数据", 2130, 2),
        SPEED("速度数据", 1, 1);
        
        private final String description;
        private final int startAddress;
        private final int quantity;
        
        DataType(String description, int startAddress, int quantity) {
            this.description = description;
            this.startAddress = startAddress;
            this.quantity = quantity;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getStartAddress() {
            return startAddress;
        }
        
        public int getQuantity() {
            return quantity;
        }
    }
    
    private final Map<DataType, Long> intervalMap;
    
    /**
     * 私有构造函数，使用Builder模式创建
     */
    private PollingConfig(Map<DataType, Long> intervalMap) {
        this.intervalMap = new HashMap<>(intervalMap);
    }
    
    /**
     * 获取指定数据类型的轮询间隔
     * 
     * @param dataType 数据类型
     * @return 轮询间隔（毫秒），如果未配置则返回null
     */
    public Long getInterval(DataType dataType) {
        return intervalMap.get(dataType);
    }
    
    /**
     * 获取所有配置的数据类型
     * 
     * @return 数据类型数组
     */
    public DataType[] getConfiguredDataTypes() {
        return intervalMap.keySet().toArray(new DataType[0]);
    }
    
    /**
     * 获取所有轮询间隔值
     * 
     * @return 间隔值数组
     */
    public long[] getAllIntervals() {
        return intervalMap.values().stream().mapToLong(Long::longValue).toArray();
    }
    
    /**
     * 检查是否包含指定数据类型的配置
     * 
     * @param dataType 数据类型
     * @return 是否包含
     */
    public boolean contains(DataType dataType) {
        return intervalMap.containsKey(dataType);
    }
    
    /**
     * 获取配置的数据类型数量
     * 
     * @return 数量
     */
    public int size() {
        return intervalMap.size();
    }
    
    /**
     * 创建默认配置
     * 针距数据：5秒，计数器数据：1秒，状态数据：0.5秒，速度数据：0.2秒
     * 
     * @return 默认配置
     */
    public static PollingConfig createDefault() {
        return new Builder()
                .setInterval(DataType.NEEDLE_GAP, 5000)
                .setInterval(DataType.COUNTER, 1000)
                .setInterval(DataType.STATUS, 500)
                .setInterval(DataType.SPEED, 200)
                .build();
    }
    
    /**
     * 创建快速配置（所有数据类型使用相同间隔）
     * 
     * @param intervalMs 轮询间隔（毫秒）
     * @return 配置对象
     */
    public static PollingConfig createFast(long intervalMs) {
        return new Builder()
                .setInterval(DataType.NEEDLE_GAP, intervalMs)
                .setInterval(DataType.COUNTER, intervalMs)
                .setInterval(DataType.STATUS, intervalMs)
                .setInterval(DataType.SPEED, intervalMs)
                .build();
    }
    
    /**
     * 创建只读取基础数据的配置（针距和状态）
     * 
     * @param needleGapIntervalMs 针距数据间隔
     * @param statusIntervalMs 状态数据间隔
     * @return 配置对象
     */
    public static PollingConfig createBasic(long needleGapIntervalMs, long statusIntervalMs) {
        return new Builder()
                .setInterval(DataType.NEEDLE_GAP, needleGapIntervalMs)
                .setInterval(DataType.STATUS, statusIntervalMs)
                .build();
    }
    
    /**
     * Builder模式用于构建PollingConfig
     */
    public static class Builder {
        private final Map<DataType, Long> intervalMap = new HashMap<>();
        
        /**
         * 设置指定数据类型的轮询间隔
         * 
         * @param dataType 数据类型
         * @param intervalMs 轮询间隔（毫秒）
         * @return Builder对象
         */
        public Builder setInterval(DataType dataType, long intervalMs) {
            if (intervalMs <= 0) {
                throw new IllegalArgumentException("轮询间隔必须大于0: " + intervalMs);
            }
            intervalMap.put(dataType, intervalMs);
            return this;
        }
        
        /**
         * 移除指定数据类型的配置
         * 
         * @param dataType 数据类型
         * @return Builder对象
         */
        public Builder removeInterval(DataType dataType) {
            intervalMap.remove(dataType);
            return this;
        }
        
        /**
         * 构建PollingConfig对象
         * 
         * @return PollingConfig对象
         * @throws IllegalStateException 如果没有配置任何数据类型
         */
        public PollingConfig build() {
            if (intervalMap.isEmpty()) {
                throw new IllegalStateException("至少需要配置一种数据类型的轮询间隔");
            }
            return new PollingConfig(intervalMap);
        }
    }
    
    /**
     * 计算基础轮询间隔（所有配置间隔的最大公约数）
     * 
     * @return 基础轮询间隔（毫秒）
     */
    public long calculateBaseInterval() {
        if (intervalMap.isEmpty()) {
            return 1000; // 默认1秒
        }
        
        // 获取所有间隔值
        Long[] intervalArray = intervalMap.values().toArray(new Long[0]);
        
        if (intervalArray.length == 1) {
            return intervalArray[0];
        }
        
        // 计算所有间隔的最大公约数
        long result = intervalArray[0];
        for (int i = 1; i < intervalArray.length; i++) {
            result = gcd(result, intervalArray[i]);
        }
        
        return result;
    }
    
    /**
     * 计算两个数的最大公约数
     * 
     * @param a 第一个数
     * @param b 第二个数
     * @return 最大公约数
     */
    private long gcd(long a, long b) {
        while (b != 0) {
            long temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }
    
    @NonNull
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("PollingConfig{");
        boolean first = true;
        for (Map.Entry<DataType, Long> entry : intervalMap.entrySet()) {
            if (!first) {
                sb.append(", ");
            }
            sb.append(entry.getKey().getDescription()).append("=").append(entry.getValue()).append("ms");
            first = false;
        }
        sb.append("}");
        return sb.toString();
    }
}