package com.supreme.smart.sewing.lang;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.databinding.DataBindingUtil;

import com.supreme.smart.sewing.MainActivity;
import com.supreme.smart.sewing.R;

import androidx.appcompat.app.AppCompatActivity;

import com.supreme.smart.sewing.databinding.LanguageSettingsBinding;
import com.supreme.smart.sewing.utils.LocaleHelper;

/**
 * 语言设置页面
 * Language Settings Activity
 */
public class LanguageSettingsActivity extends AppCompatActivity {
    
    private LanguageSettingsBinding binding;
    
    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase)));
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.language_settings);
        
        // 初始化DataBinding变量
        binding.setSelectedLanguage("");
        binding.setIsChinese(false);
        binding.setIsEnglish(false);
        binding.setIsVietnamese(false);
        binding.setIsIndonesian(false);
        
        setupListeners();
        loadCurrentLanguage();
    }
    
    private void setupListeners() {
        // 设置返回按钮
        binding.btnBack.setOnClickListener(v -> finish());
        
        // 设置确认按钮
        binding.btnConfirm.setOnClickListener(this::onConfirmClicked);
        
        // 设置RadioButton点击事件
        binding.rbChinese.setOnClickListener(v -> selectLanguage(LocaleHelper.LANGUAGE_CHINESE));
        binding.rbEnglish.setOnClickListener(v -> selectLanguage(LocaleHelper.LANGUAGE_ENGLISH));
        binding.rbVietnamese.setOnClickListener(v -> selectLanguage(LocaleHelper.LANGUAGE_VIETNAMESE));
        binding.rbIndonesian.setOnClickListener(v -> selectLanguage(LocaleHelper.LANGUAGE_INDONESIAN));
    }
    
    private void loadCurrentLanguage() {
        String currentLanguage = LocaleHelper.getLanguage(this);
        selectLanguage(currentLanguage);
    }
    
    private void selectLanguage(String language) {
        binding.setSelectedLanguage(language);
        
        // 重置所有选择状态
        binding.setIsChinese(false);
        binding.setIsEnglish(false);
        binding.setIsVietnamese(false);
        binding.setIsIndonesian(false);
        
        // 设置选中的语言
        switch (language) {
            case LocaleHelper.LANGUAGE_ENGLISH:
                binding.setIsEnglish(true);
                break;
            case LocaleHelper.LANGUAGE_VIETNAMESE:
                binding.setIsVietnamese(true);
                break;
            case LocaleHelper.LANGUAGE_INDONESIAN:
                binding.setIsIndonesian(true);
                break;
            case LocaleHelper.LANGUAGE_CHINESE:
            default:
                binding.setIsChinese(true);
                break;
        }
    }
    
    private void onConfirmClicked(View view) {
        String selectedLanguage = binding.getSelectedLanguage();
        String currentLanguage = LocaleHelper.getLanguage(this);
        
        if (!selectedLanguage.equals(currentLanguage)) {
            // 保存新的语言设置
            LocaleHelper.setLocale(this, selectedLanguage);
            
            // 重启应用以应用新的语言设置
            restartApp();
        } else {
            // 语言没有改变，直接返回
            finish();
        }
    }
    
    private void restartApp() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        finish();
        
        // 可选：添加过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }
}