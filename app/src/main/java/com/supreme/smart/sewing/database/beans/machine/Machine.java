package com.supreme.smart.sewing.database.beans.machine;


import com.supreme.smart.sewing.utils.UUIDUtils;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

/**
 * 设备信息
 */
public class Machine extends RealmObject implements java.io.Serializable {

    /**
     * 主键
     */
    @PrimaryKey
    private String id;

    /**
     * 机器编号
     */
    private String machineNo;

    /**
     * 机器名称
     */
    private String machineName;

    /**
     * 自定义编号
     */
    private String customNo;

    /**
     * 机器类型
     */
    private String machineType;

    /**
     * 机型系列
     */
    private String machineSeries;

    /**
     * 机器版本
     */
    private String machineVersion;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;

    }

    public String getMachineNo() {
        return machineNo;
    }

    public void setMachineNo(String machineNo) {
        this.machineNo = machineNo;
    }

    public String getMachineName() {
        return machineName;
    }

    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }

    public String getMachineType() {
        return machineType;
    }

    public void setMachineType(String machineType) {
        this.machineType = machineType;
    }

    public String getMachineSeries() {
        return machineSeries;
    }

    public void setMachineSeries(String machineSeries) {
        this.machineSeries = machineSeries;
    }

    public String getMachineVersion() {
        return machineVersion;
    }

    public void setMachineVersion(String machineVersion) {
        this.machineVersion = machineVersion;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }
}
