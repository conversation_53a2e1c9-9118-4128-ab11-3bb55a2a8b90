package com.supreme.smart.sewing.voice.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.supreme.smart.sewing.R;

import java.util.List;

/**
 * 聊天消息适配器
 */
public class ChatMessageAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    
    private final Context context;
    private final List<ChatMessage> messages;
    private OnVoiceMessageClickListener voiceClickListener;
    private OnVoiceMessageLongClickListener voiceLongClickListener;
    
    public interface OnVoiceMessageClickListener {
        void onVoiceMessageClick(ChatMessage message);
    }
    
    public interface OnVoiceMessageLongClickListener {
        void onVoiceMessageLongClick(ChatMessage message);
    }
    
    public ChatMessageAdapter(Context context, List<ChatMessage> messages) {
        this.context = context;
        this.messages = messages;
    }
    
    public void setOnVoiceMessageClickListener(OnVoiceMessageClickListener listener) {
        this.voiceClickListener = listener;
    }
    
    public void setOnVoiceMessageLongClickListener(OnVoiceMessageLongClickListener listener) {
        this.voiceLongClickListener = listener;
    }
    
    @Override
    public int getItemViewType(int position) {
        return messages.get(position).getType();
    }
    
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(context);
        
        if (viewType == ChatMessage.TYPE_TEXT) {
            View view = inflater.inflate(R.layout.item_chat_text, parent, false);
            return new TextMessageViewHolder(view);
        } else {
            View view = inflater.inflate(R.layout.item_chat_voice, parent, false);
            return new VoiceMessageViewHolder(view);
        }
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ChatMessage message = messages.get(position);
        boolean shouldShowTime = shouldShowTime(position);
        
        if (holder instanceof TextMessageViewHolder) {
            ((TextMessageViewHolder) holder).bind(message, shouldShowTime);
        } else if (holder instanceof VoiceMessageViewHolder) {
            ((VoiceMessageViewHolder) holder).bind(message, shouldShowTime);
        }
    }
    
    /**
     * 判断是否应该显示时间
     * 微信风格：第一条消息显示时间，相邻消息间隔超过5分钟显示时间
     */
    private boolean shouldShowTime(int position) {
        // 第一条消息总是显示时间
        if (position == 0) {
            return true;
        }
        
        // 获取当前消息和前一条消息
        ChatMessage currentMessage = messages.get(position);
        ChatMessage previousMessage = messages.get(position - 1);
        
        // 计算时间间隔（毫秒）
        long timeDiff = currentMessage.getTimestamp().getTime() - previousMessage.getTimestamp().getTime();
        
        // 超过5分钟（300000毫秒）显示时间
        return timeDiff > 5 * 60 * 1000;
    }
    
    @Override
    public int getItemCount() {
        return messages.size();
    }
    
    /**
     * 添加新消息
     */
    public void addMessage(ChatMessage message) {
        messages.add(message);
        notifyItemInserted(messages.size() - 1);
    }
    
    /**
     * 更新消息播放状态
     */
    public void updatePlayingState(String messageId, boolean isPlaying) {
        for (int i = 0; i < messages.size(); i++) {
            ChatMessage message = messages.get(i);
            if (message.getId().equals(messageId)) {
                message.setPlaying(isPlaying);
                notifyItemChanged(i);
                break;
            }
        }
    }
    
    /**
     * 停止所有语音播放状态
     */
    public void stopAllPlaying() {
        for (int i = 0; i < messages.size(); i++) {
            ChatMessage message = messages.get(i);
            if (message.isPlaying()) {
                message.setPlaying(false);
                notifyItemChanged(i);
            }
        }
    }
    
    /**
     * 文本消息ViewHolder
     */
    static class TextMessageViewHolder extends RecyclerView.ViewHolder {
        TextView tvContent;
        TextView tvTime;
        
        public TextMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            tvContent = itemView.findViewById(R.id.tv_content);
            tvTime = itemView.findViewById(R.id.tv_time);
        }
        
        public void bind(ChatMessage message, boolean showTime) {
            tvContent.setText(message.getContent());
            
            if (showTime) {
                tvTime.setVisibility(View.VISIBLE);
                tvTime.setText(message.getFormattedTime());
            } else {
                tvTime.setVisibility(View.GONE);
            }
        }
    }
    
    /**
     * 语音消息ViewHolder
     */
    class VoiceMessageViewHolder extends RecyclerView.ViewHolder {
        ImageView ivVoiceIcon;
        TextView tvDuration;
        TextView tvTime;
        View voiceContainer;
        
        // 转换文字相关UI
        View convertedTextContainer;
        TextView tvConvertStatus;
        TextView tvConvertedText;

        public VoiceMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            ivVoiceIcon = itemView.findViewById(R.id.iv_voice_icon);
            tvDuration = itemView.findViewById(R.id.tv_duration);
            tvTime = itemView.findViewById(R.id.tv_time);
            voiceContainer = itemView.findViewById(R.id.voice_container);
            
            // 转换文字相关UI
            convertedTextContainer = itemView.findViewById(R.id.converted_text_container);
            tvConvertStatus = itemView.findViewById(R.id.tv_convert_status);
            tvConvertedText = itemView.findViewById(R.id.tv_converted_text);

            // 设置点击事件
            voiceContainer.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && voiceClickListener != null) {
                    ChatMessage message = messages.get(position);
                    voiceClickListener.onVoiceMessageClick(message);
                }
            });
            
            // 设置长按事件
            voiceContainer.setOnLongClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && voiceLongClickListener != null) {
                    ChatMessage message = messages.get(position);
                    voiceLongClickListener.onVoiceMessageLongClick(message);
                }
                return true;
            });
        }
        
        public void bind(ChatMessage message, boolean showTime) {
            tvDuration.setText(message.getFormattedDuration());
            
            if (showTime) {
                tvTime.setVisibility(View.VISIBLE);
                tvTime.setText(message.getFormattedTime());
            } else {
                tvTime.setVisibility(View.GONE);
            }
            
            // 根据播放状态更新图标
            if (message.isPlaying()) {
                ivVoiceIcon.setImageResource(R.drawable.ic_voice_playing);
            } else {
                ivVoiceIcon.setImageResource(R.drawable.ic_voice);
            }
            
            // 处理转换文字显示
            if (message.isConverting()) {
                // 正在转换中
                convertedTextContainer.setVisibility(View.VISIBLE);
                tvConvertStatus.setVisibility(View.VISIBLE);
                tvConvertStatus.setText("转换中...");
                tvConvertedText.setVisibility(View.GONE);
            } else if (message.isShowConvertedText() && message.hasConvertedText()) {
                // 显示转换后的文字
                convertedTextContainer.setVisibility(View.VISIBLE);
                tvConvertStatus.setVisibility(View.GONE);
                tvConvertedText.setVisibility(View.VISIBLE);
                tvConvertedText.setText(message.getConvertedText());
            } else {
                // 隐藏转换文字区域
                convertedTextContainer.setVisibility(View.GONE);
            }
        }
    }
}
