package com.supreme.smart.sewing.constants;

import com.blankj.utilcode.util.DeviceUtils;

public class CommonConstant {

    /**
     * 服务器地址
     */
    public static final String SYSTEM_SERVER_URL = "http://************:9898"; // 请替换为实际服务器地址

    /**
     * MQTT地址
     */
    public static final String MQTT_SERVER_HOST = "************"; // 替换为你的MQTT服务器

    /**
     * MQTT端口
     */
    public static final int MQTT_SERVER_PORT = 1883;

    /**
     * MQTT客户端ID
     */
    public static String MQTT_CLIENT_ID = "SmartSewingSystem_" + DeviceUtils.getAndroidID();

    /**
     * MQTT用户名
     */
    public static final String MQTT_USERNAME = "admin"; // 替换为你的用户名

    /**
     * MQTT密码
     */
    public static final String MQTT_PASSWORD = "password"; // 替换为你的密码

    /**
     * MQTT主题前缀
     */
    public static final String MQTT_TOPIC_PREFIX = "/SmartSewingSystem/";

    // ==================== MQTT配置常量 begin====================
    /**
     * MQTT 心跳间隔（毫秒） --- 平板
     */
    public static final int MQTT_TABLET_KEEP_ALIVE_INTERVAL = 60 * 1000;

    /**
     * MQTT重连初始延迟（毫秒） --- 平板
     */
    public static final int MQTT_TABLET_RECONNECT_INITIAL_DELAY = 2 * 1000;

    /**
     * MQTT重连最大延迟（毫秒） --- 平板
     */
    public static final int MQTT_TABLET_RECONNECT_MAX_DELAY = 10 * 1000;

    /**
     * MQTT Socket连接超时（毫秒）--- 平板
     */
    public static final int MQTT_TABLET_SOCKET_CONNECT_TIMEOUT = 10 * 1000;

    /**
     * MQTT协议连接超时（毫秒）--- 平板
     */
    public static final int MQTT_TABLET_MQTT_CONNECT_TIMEOUT = 15 * 1000;

    //============================= 手机 ===============================

    /**
     * MQTT 心跳间隔（毫秒） --- 手机
     */
    public static final int MQTT_PHONE_KEEP_ALIVE_INTERVAL = 60 * 1000;

    /**
     * MQTT重连初始延迟（毫秒）--- 手机
     */
    public static final int MQTT_PHONE_RECONNECT_INITIAL_DELAY = 2 * 1000;

    /**
     * MQTT重连最大延迟（毫秒）--- 手机
     */
    public static final int MQTT_PHONE_RECONNECT_MAX_DELAY = 5 * 1000;

    /**
     * MQTT Socket连接超时（毫秒）--- 手机
     */
    public static final int MQTT_PHONE_SOCKET_CONNECT_TIMEOUT = 2 * 1000;

    /**
     * MQTT协议连接超时（毫秒）
     */
    public static final int MQTT_PHONE_MQTT_CONNECT_TIMEOUT = 2 * 1000;

    // ==================== 服务配置常量 begin====================

    /**
     * 服务线程池大小 --- 平板
     */
    public static final int SERVICE_TABLET_THREAD_POOL_SIZE = 4;

    /**
     * 服务Handler延迟时间（毫秒） --- 平板
     */
    public static final long SERVICE_TABLET_HANDLER_DELAY_MS = (long) (0.5 * 1000);

    /**
     * 服务网络重试延迟（毫秒） --- 平板
     */
    public static final long SERVICE_TABLET_NETWORK_RETRY_DELAY_MS = 1000;

    /**
     * 服务通知更新间隔（毫秒） --- 平板
     */
    public static final long SERVICE_TABLET_NOTIFICATION_UPDATE_INTERVAL_MS = 10 * 1000;

    /**
     * 服务是否使用固定线程池 --- 平板
     */
    public static final boolean SERVICE_TABLET_USE_FIXED_THREAD_POOL = true;

    /**
     * 服务线程池大小 --- 手机
     */
    public static final int SERVICE_PHONE_THREAD_POOL_SIZE = 2;

    /**
     * 服务Handler延迟时间（毫秒） --- 手机
     */
    public static final long SERVICE_PHONE_HANDLER_DELAY_MS = 2 * 1000;

    /**
     * 服务网络重试延迟（毫秒） --- 手机
     */
    public static final long SERVICE_PHONE_NETWORK_RETRY_DELAY_MS = 5 * 1000;

    /**
     * 服务通知更新间隔（毫秒） --- 手机
     */
    public static final long SERVICE_PHONE_NOTIFICATION_UPDATE_INTERVAL_MS = 30 * 1000;

    /**
     * 服务是否使用固定线程池 --- 手机
     */
    public static final boolean SERVICE_PHONE_USE_FIXED_THREAD_POOL = true;

    // ==================== 网络配置常量 begin====================

    /**
     * 网络状态防抖时间（毫秒） --- 平板
     */
    public static final long NETWORK_TABLET_STATE_DEBOUNCE_MS = 1000;

    /**
     * 网络最大重试次数 --- 平板
     */
    public static final int NETWORK_TABLET_MAX_RETRY_ATTEMPTS = 10;

    /**
     * 网络是否启用智能重试 --- 平板
     */
    public static final boolean NETWORK_TABLET_ENABLE_SMART_RETRY = true;

    /**
     * 网络状态防抖时间（毫秒） --- 手机
     */
    public static final long NETWORK_PHONE_STATE_DEBOUNCE_MS = 5 * 1000;

    /**
     * 网络最大重试次数 --- 手机
     */
    public static final int NETWORK_PHONE_MAX_RETRY_ATTEMPTS = 5;

    /**
     * 网络是否启用智能重试 --- 手机
     */
    public static final boolean NETWORK_PHONE_ENABLE_SMART_RETRY = true;

}
