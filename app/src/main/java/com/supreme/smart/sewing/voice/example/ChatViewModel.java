package com.supreme.smart.sewing.voice.example;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableBoolean;

import com.supreme.smart.sewing.BR;

/**
 * ChatActivity的ViewModel
 * 用于管理聊天界面的数据和状态
 */
public class ChatViewModel extends BaseObservable {

    // 可观察字段
    public final ObservableField<String> currentMessage = new ObservableField<>("");
    public final ObservableBoolean isVoiceMode = new ObservableBoolean(false);
    public final ObservableBoolean isSendButtonEnabled = new ObservableBoolean(false);
    public final ObservableField<String> voiceButtonText = new ObservableField<>("按住 说话");
    
    // 状态字段
    private boolean isRecording = false;
    private boolean isPlaying = false;

    public ChatViewModel() {
        // 初始化默认值
        currentMessage.set("");
        isVoiceMode.set(false);
        isSendButtonEnabled.set(false);
    }

    /**
     * 设置当前消息内容
     */
    public void setCurrentMessage(String message) {
        currentMessage.set(message);
        // 根据消息内容更新发送按钮状态
        boolean hasContent = message != null && !message.trim().isEmpty();
        isSendButtonEnabled.set(hasContent && !isVoiceMode.get());
    }

    /**
     * 获取当前消息内容
     */
    public String getCurrentMessage() {
        return currentMessage.get();
    }

    /**
     * 切换输入模式（键盘/语音）
     */
    public void toggleInputMode() {
        boolean newVoiceMode = !isVoiceMode.get();
        isVoiceMode.set(newVoiceMode);
        
        // 语音模式下禁用发送按钮
        if (newVoiceMode) {
            isSendButtonEnabled.set(false);
        } else {
            // 键盘模式下根据消息内容决定发送按钮状态
            String message = getCurrentMessage();
            boolean hasContent = message != null && !message.trim().isEmpty();
            isSendButtonEnabled.set(hasContent);
        }
        
        notifyPropertyChanged(BR._all);
    }

    /**
     * 清空当前消息
     */
    public void clearMessage() {
        setCurrentMessage("");
    }

    /**
     * 设置录音状态
     */
    @Bindable
    public boolean isRecording() {
        return isRecording;
    }

    public void setRecording(boolean recording) {
        if (this.isRecording != recording) {
            this.isRecording = recording;
            notifyPropertyChanged(BR.recording);
            
            // 更新语音按钮文本
            if (recording) {
                voiceButtonText.set("松开 结束");
            } else {
                voiceButtonText.set("按住 说话");
            }
        }
    }

    /**
     * 设置播放状态
     */
    @Bindable
    public boolean isPlaying() {
        return isPlaying;
    }

    public void setPlaying(boolean playing) {
        if (this.isPlaying != playing) {
            this.isPlaying = playing;
            notifyPropertyChanged(BR.playing);
        }
    }

    /**
     * 获取语音模式状态
     */
    @Bindable
    public boolean isVoiceMode() {
        return isVoiceMode.get();
    }

    /**
     * 获取发送按钮启用状态
     */
    @Bindable
    public boolean isSendButtonEnabled() {
        return isSendButtonEnabled.get();
    }

    /**
     * 获取语音按钮文本
     */
    @Bindable
    public String getVoiceButtonText() {
        return voiceButtonText.get();
    }
} 