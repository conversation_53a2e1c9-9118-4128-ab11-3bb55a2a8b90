package com.supreme.smart.sewing.database.beans.lang;



import com.supreme.smart.sewing.utils.UUIDUtils;

import java.util.Date;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

/**
 * 语言条目
 */

public class MultiLangItem extends RealmObject implements java.io.Serializable {
    /**
     * id
     */
    @PrimaryKey
    private String id;
    /**
     * 创建人ID
     */
    private String createBy;
    /**
     * 创建人姓名
     */
    private String createName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人ID
     */
    private String updateBy;
    /**
     * 修改人姓名
     */
    private String updateName;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 序号
     */
    private Integer seq;
    /**
     * 代码
     */
    private String code;
    /**
     * locale
     */
    private String locale;
    /**
     * 名称
     */
    private String name;
    /**
     * 使用中
     */
    private Byte using;

    public MultiLangItem() {
        this.id = UUIDUtils.getId();
        this.createBy = UUIDUtils.getCreateBy();
        this.createName = UUIDUtils.getCreateName();
        this.createTime = UUIDUtils.getCreateTime();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getUsing() {
        return using;
    }

    public void setUsing(Byte using) {
        this.using = using;
    }
}
