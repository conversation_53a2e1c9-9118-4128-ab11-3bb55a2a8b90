package com.supreme.smart.sewing.finger;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.databinding.FingerprintBinding;
import com.supreme.smart.sewing.finger.FingerprintAuthManager.FingerprintAuthCallback;
import com.supreme.smart.sewing.utils.LocaleHelper;

import java.util.concurrent.Executor;

/**
 * 指纹识别 - 简化版
 * 指纹验证通过后直接与服务器交互
 */
public class FingerprintActivity extends AppCompatActivity {
    private static final String TAG = "FingerprintActivity";


    // 操作模式
    public enum Mode {
        BIND,   // 绑定指纹
        LOGIN   // 指纹登录
    }

    private FingerprintBinding binding;
    private BiometricPrompt biometricPrompt;
    private Mode currentMode = Mode.LOGIN;
    private String userId; // 用户ID
    private FingerprintAuthManager authManager;

    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase)));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);

        // 使用DataBinding初始化布局
        binding = DataBindingUtil.setContentView(this, R.layout.fingerprint);

        // 获取传入的参数
        //  currentMode = (Mode) getIntent().getSerializableExtra("mode");
        //   if (currentMode == null) currentMode = Mode.BIND;

        userId = "e9ca23d68d884d4ebb19d07889727dae"; //getIntent().getStringExtra("userId");

        // 初始化认证管理器
        authManager = FingerprintAuthManager.INSTANCE;

        // 初始化默认值
        binding.setStatusText("准备指纹认证...");
        binding.setIsAuthenticateEnabled(false);


        initBiometric();
        checkBiometricSupport();
    }


    private void initBiometric() {
        Executor executor = ContextCompat.getMainExecutor(this);

        biometricPrompt = new BiometricPrompt(this, executor, new BiometricPrompt.AuthenticationCallback() {
            @Override
            public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                super.onAuthenticationError(errorCode, errString);
            }

            @Override
            public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                super.onAuthenticationSucceeded(result);
                LogUtils.iTag(TAG, "指纹认证成功");

                if (currentMode == Mode.BIND) {
                    // 绑定模式：调用服务器绑定接口
                    bindFingerprint();
                } else {
                    // 登录模式：调用服务器登录接口
                    loginFingerprint();
                }
            }

            @Override
            public void onAuthenticationFailed() {
                super.onAuthenticationFailed();
                LogUtils.wTag(TAG, "指纹认证失败");
                updateStatus("指纹认证失败，请重试", false);
            }
        });

    }

    private void checkBiometricSupport() {
        // 首先检查设备兼容性
        String compatibilityError = authManager.checkDeviceCompatibility();
        if (compatibilityError != null) {
            updateStatus("设备不支持: " + compatibilityError, false);
            Toast.makeText(this, compatibilityError, Toast.LENGTH_LONG).show();
            return;
        }


        BiometricManager biometricManager = BiometricManager.from(this);
        switch (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            case BiometricManager.BIOMETRIC_SUCCESS:
                binding.setIsAuthenticateEnabled(true);
                if (currentMode == Mode.BIND) {
                    updateStatus("准备绑定指纹到您的账号", true);
                } else {
                    updateStatus("可以使用指纹登录", true);
                }
                break;

            case BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE:

            case BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED:
                updateStatus("设备不支持指纹识别", false);
                break;

            case BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE:
                updateStatus("指纹硬件暂时不可用", false);
                break;

            case BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED:
                updateStatus("请先在系统设置中录入指纹", false);
                break;

            case BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED:
                updateStatus("需要安全更新", false);
                break;

            case BiometricManager.BIOMETRIC_STATUS_UNKNOWN:
                updateStatus("指纹状态未知", false);
                break;

            default:
                updateStatus("指纹状态检查失败", false);
                break;
        }
    }

    private void startAuthentication() {
        // 再次检查设备兼容性
        String compatibilityError = authManager.checkDeviceCompatibility();
        if (compatibilityError != null) {
            updateStatus("设备不支持: " + compatibilityError, false);
            Toast.makeText(this, compatibilityError, Toast.LENGTH_LONG).show();
            return;
        }


        updateStatus( currentMode == Mode.BIND?"指纹绑定":"指纹登录", true);

        // 根据模式设置不同的提示信息
        String title = currentMode == Mode.BIND ? "绑定指纹" : "指纹登录";
        String subtitle = currentMode == Mode.BIND ?
                "请验证指纹以绑定您的账号" : "请验证指纹以登录";

        BiometricPrompt.PromptInfo promptInfo = new BiometricPrompt.PromptInfo.Builder()
                .setTitle(title)
                .setSubtitle(subtitle)
                .setDescription("使用指纹进行身份验证")
                .setNegativeButtonText("取消")
                .build();
        // 启动指纹认证
        biometricPrompt.authenticate(promptInfo);
    }

    /**
     * 绑定指纹到账号
     */
    private void bindFingerprint() {
        updateStatus("正在绑定指纹...", true);

        authManager.bindFingerprint(userId, new FingerprintAuthCallback() {
            @Override
            public void onSuccess(String message) {
                updateStatus(message, true);
                runOnUiThread(() -> Toast.makeText(FingerprintActivity.this, message, Toast.LENGTH_SHORT).show());
            }

            @Override
            public void onError(String error) {
                updateStatus(error, false);
                runOnUiThread(() -> Toast.makeText(FingerprintActivity.this, error, Toast.LENGTH_SHORT).show());
            }
        });
    }

    /**
     * 使用指纹登录
     */
    private void loginFingerprint() {
        updateStatus("正在验证指纹登录...", true);

        authManager.loginFingerprint(new FingerprintAuthCallback() {

            @Override
            public void onSuccess(String message) {
                updateStatus(message, true);
                runOnUiThread(() -> Toast.makeText(FingerprintActivity.this, message, Toast.LENGTH_SHORT).show());
            }

            @Override
            public void onError(String error) {
                updateStatus(error, false);
                runOnUiThread(() -> Toast.makeText(FingerprintActivity.this, error, Toast.LENGTH_SHORT).show());
            }
        });
    }

    private void unbindFingerprint() {
        updateStatus("正在解绑指纹...", true);
        authManager.unbindFingerprint(new FingerprintAuthCallback() {
            @Override
            public void onSuccess(String message) {
                updateStatus(message, true);
                runOnUiThread(() -> Toast.makeText(FingerprintActivity.this, message, Toast.LENGTH_SHORT).show());
            }

            @Override
            public void onError(String error) {
                updateStatus(error, false);
                runOnUiThread(() -> Toast.makeText(FingerprintActivity.this, error, Toast.LENGTH_SHORT).show());
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (binding != null) {
            binding.unbind();
        }
    }

    private void updateStatus(String message, boolean isSuccess) {
        // 使用DataBinding更新状态文本和按钮状态
        binding.setStatusText(message);

        // 设置状态文本颜色
        binding.tvStatus.setTextColor(ContextCompat.getColor(this,
                isSuccess ? android.R.color.holo_green_dark : android.R.color.holo_red_dark));
    }

    public void doBind(View view) {
        currentMode = Mode.BIND;
        startAuthentication();
    }

    public void doUnbind(View view) {
        unbindFingerprint();
    }


    public void doLogin(View view) {
        currentMode = Mode.LOGIN;
        startAuthentication();
    }

    public void doClose(View view) {
        finish();
    }


}