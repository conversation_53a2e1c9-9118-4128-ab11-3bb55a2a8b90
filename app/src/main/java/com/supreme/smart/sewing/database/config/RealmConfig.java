package com.supreme.smart.sewing.database.config;

import android.os.Environment;

import com.supreme.smart.sewing.database.module.DefaultModule;
import com.supreme.smart.sewing.database.module.CacheModule;

import java.io.File;

import io.realm.RealmConfiguration;

public final class RealmConfig {

    private static final String DB_PATH = Environment.getDataDirectory().getPath();// + "/save_db/";

    private static final File DB_DIRECTORY = new File(DB_PATH);
    /**
     * 每次修改Realm的表结构后，都要升级版本号
     */
    private static final long currentSchemaVersion = 10;

    public static RealmConfiguration getDefaultConfig() {
        return new RealmConfiguration.Builder()
                .name("smart-sewing-system-db.realm")
                .schemaVersion(currentSchemaVersion)
                .modules(new DefaultModule())
                .deleteRealmIfMigrationNeeded()//大规模修改表结构时使用
//                .migration((realm, oldVersion, newVersion) -> {// 部分修改表结构时使用
//                    LogUtils.iTag("migration", "oldVersion = " + oldVersion + " , newVersion = " + newVersion);
//                    RealmObjectSchema schema;
//
//                    schema = realm.getSchema().get(WcsMaterialOptRec.class.getSimpleName());
//                    if (schema != null) {
//                        if (!schema.hasField("weekDay")) {
//                            schema.addField("weekDay", Integer.class);
//                        }
//
//                        if (!schema.hasField("stdWorkTime")) {
//                            schema.addField("stdWorkTime", Integer.class);
//                        }
//
//                        //新增更新时间字段
//                        if (!schema.hasField("updateTime")) {
//                            schema.addField("updateTime", Date.class);
//                        }
//                    }
//
//
//                })
                .build();
    }


    public static RealmConfiguration getCacheConfig() {
        return new RealmConfiguration.Builder()
                .name("smart-sewing-cached-db.realm")
                .schemaVersion(currentSchemaVersion)
                .modules(new CacheModule())
               // .deleteRealmIfMigrationNeeded()//大规模修改表结构时使用
                .build();
    }


    private static void checkAndCreateDirectory(File directory) {
        if (!(directory.exists() && directory.isDirectory())) {
            directory.mkdirs();
        }
    }

}
