package com.supreme.smart.sewing.database.utils;


import com.supreme.smart.sewing.database.beans.cache.LatestValues;
import com.supreme.smart.sewing.database.config.RealmConfig;
import com.supreme.smart.sewing.utils.CommonUtils;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;

import io.realm.Realm;

public class DataCacheUtils {
    /**
     * 保存
     *
     * @param key   键
     * @param value 值
     * @return 是否成功
     */
    public synchronized static boolean save(final String key, final String value) {
        return save(key, value, 0);
    }

    public synchronized static boolean save(final String key, final String value, final int timeoutSecond) {
        boolean bUpdate = true;
          Realm realm = Realm.getInstance(RealmConfig.getCacheConfig());
        try {
            realm.beginTransaction();
            LatestValues element = realm.where(LatestValues.class).equalTo("key", key).findFirst();
            if (element == null) {
                bUpdate = false;
                element = new LatestValues();
                element.setKey(key);
            }
            element.setValue(value);
            element.setTimeoutSecond(timeoutSecond);
            if (bUpdate) {
                realm.copyToRealmOrUpdate(element);
            } else {
                realm.copyToRealm(element);
            }
            realm.commitTransaction();
            return true;
        } catch (Exception e) {
            realm.cancelTransaction();
            return false;
        } finally {
            realm.close();
        }
    }

    /**
     * 获取
     *
     * @param key 键
     * @return 值
     */
    public synchronized static String get(final String key) {
        try (Realm realm = Realm.getInstance(RealmConfig.getCacheConfig())) {
              LatestValues element = realm.where(LatestValues.class)
                    .equalTo("key", key).findFirst();
            if (element != null && StringUtils.isNotEmpty(element.getValue())) {
                  int timeoutSecond = element.getTimeoutSecond() == null ? 0 : element.getTimeoutSecond();
                if (timeoutSecond <= 0) {
                    return element.getValue();
                } else {
                      Date createTime = element.getCreateTime();
                      Date currentTime = CommonUtils.getCurrentTime();
                    if (CommonUtils.dateDiff(CommonUtils.DatePart.SECOND, createTime, currentTime) > timeoutSecond) {
                        // 超时的数据，删除，并返回空置
                        try {
                            realm.beginTransaction();
                            realm.where(LatestValues.class).equalTo("key", key).findAll().deleteAllFromRealm();
                            realm.commitTransaction();
                        } catch (Exception e) {
                            realm.cancelTransaction();
                        }
                        return null;
                    } else {
                        return element.getValue();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 删除
     *
     * @param key 键
     * @return 是否成功
     */
    public synchronized static boolean delete(final String key) {
          Realm realm = Realm.getInstance(RealmConfig.getCacheConfig());
        try {
            realm.beginTransaction();
            realm.where(LatestValues.class).equalTo("key", key).findAll().deleteAllFromRealm();
            realm.commitTransaction();
            return true;
        } catch (Exception e) {
            realm.cancelTransaction();
            return false;
        } finally {
            realm.close();
        }
    }

    /**
     * 根据键的前缀删除
     *
     * @param keyPrefix 键前缀
     * @return 是否成功
     */
    public synchronized static boolean deleteByKeyPrefix(final String keyPrefix) {
          Realm realm = Realm.getInstance(RealmConfig.getCacheConfig());
        try {
            realm.beginTransaction();
            realm.where(LatestValues.class).like("key", keyPrefix + "*").findAll().deleteAllFromRealm();
            realm.commitTransaction();
            return true;
        } catch (Exception e) {
            realm.cancelTransaction();
            return false;
        } finally {
            realm.close();
        }
    }


    public synchronized static String findKeyByValue(final String value) {
        try (Realm realm = Realm.getInstance(RealmConfig.getCacheConfig())) {
              LatestValues element = realm.where(LatestValues.class)
                    .equalTo("value", value).findFirst();
            if (element != null && StringUtils.isNotEmpty(element.getKey())) {
                return element.getKey();
            }
        }
        return null;
    }


}
