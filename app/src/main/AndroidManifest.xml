<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 硬件特性声明：USB主机模式支持 - 允许应用作为USB主机连接USB设备 -->
    <uses-feature android:name="android.hardware.usb.host" />

    <!-- 通知权限 -->
    <!-- 发送通知权限 - Android 13+必需，用于显示推送通知 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 音频相关权限 -->
    <!-- 录音权限 - 允许应用录制音频，用于语音识别功能 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- USB权限 -->
    <!-- USB设备访问权限 - 允许应用访问USB设备（如串口设备、传感器等） -->
    <uses-permission android:name="android.permission.USB_PERMISSION" />

    <!-- 指纹识别权限 -->
    <!-- 生物识别权限 - Android 9.0+推荐使用，支持指纹、面部识别等 -->
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <!-- 指纹识别权限 - Android 6.0-8.1使用，现已被USE_BIOMETRIC替代 -->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <!-- 生物识别硬件特性声明 -->
    <!-- 指纹硬件特性 - 声明设备需要指纹传感器（非必需，兼容无指纹设备） -->
    <uses-feature
        android:name="android.hardware.fingerprint"
        android:required="false" />
    <!-- 生物识别硬件特性 - 声明设备支持生物识别（非必需） -->
    <uses-feature
        android:name="android.hardware.biometrics"
        android:required="false" />

    <!-- 摄像头硬件特性 - 声明设备需要摄像头（非必需，兼容无摄像头设备） -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <!-- 摄像头和网络权限 -->
    <!-- 摄像头权限 - 允许应用使用摄像头进行拍照、扫码、人脸识别等 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 网络访问权限 - 允许应用访问互联网，用于数据传输、API调用等 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 震动权限 - 允许应用控制设备震动，用于反馈提示 -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <!-- 闪光灯权限 - 允许应用控制摄像头闪光灯 -->
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <!-- 系统窗口权限 -->
    <!-- 系统级弹窗权限 - 允许应用在其他应用上方显示窗口 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <!-- 系统覆盖窗口权限 - 允许应用创建系统级覆盖窗口 -->
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />

    <!-- 系统状态权限 -->
    <!-- 读取手机状态权限 - 允许应用读取设备信息、IMEI等 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!-- 读取基本手机状态权限 - Android 10+的受限版本，只能读取基本信息 -->
    <uses-permission android:name="android.permission.READ_BASIC_PHONE_STATE" />

    <!-- 存储权限 -->
    <!-- 写入外部存储权限 - 允许应用写入SD卡等外部存储（Android 10以下） -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 读取外部存储权限 - 允许应用读取SD卡等外部存储文件 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!-- 管理外部存储权限 - Android 11+的完整文件访问权限，需要用户手动授权 -->
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <!-- 写入媒体存储权限 - 系统级权限，允许写入媒体文件到共享存储 -->
    <uses-permission
        android:name="android.permission.WRITE_MEDIA_STORAGE"
        tools:ignore="ProtectedPermissions" />

    <!-- 联系人权限 -->
    <!-- 读取联系人权限 - 允许应用读取设备联系人信息 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <!-- 写入联系人权限 - 允许应用修改或添加联系人信息 -->
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />

    <!-- USB访问权限 -->
    <!-- USB配件模式权限 - 允许应用作为USB配件与其他USB主机通信 -->
    <uses-permission android:name="android.hardware.usb.accessory" />

    <!-- 任务管理权限 -->
    <!-- 重新排序任务权限 - 允许应用重新排列系统任务栈中的Activity -->
    <uses-permission android:name="android.permission.REORDER_TASKS" />

    <!-- 蓝牙权限（传统） -->
    <!-- 蓝牙基础权限 - 允许应用使用蓝牙功能（Android 12以下） -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <!-- 蓝牙管理权限 - 允许应用管理蓝牙设置、配对等（Android 12以下） -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <!-- Android 12+ 新蓝牙权限 -->
    <!-- 蓝牙连接权限 - Android 12+必需，用于连接已配对的蓝牙设备 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <!-- 蓝牙扫描权限 - Android 12+必需，用于扫描附近的蓝牙设备 -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <!-- Android 13+ 细分媒体权限 -->
    <!-- 注意：为了减少权限申请，已移除图片、视频、音频权限 -->
    <!-- 如需访问媒体文件，可使用 MANAGE_EXTERNAL_STORAGE 权限或分区存储 -->
    <!-- <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> -->
    <!-- <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> -->
    <!-- <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> -->

    <!-- 网络和位置权限 -->
    <!-- 访问WiFi状态权限 - 允许应用查看WiFi连接状态和信息 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 粗略位置权限 - 允许应用获取大概位置（基于网络定位） -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- 精确位置权限 - 允许应用获取精确位置（GPS定位） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- 访问网络状态权限 - 允许应用检查网络连接状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 改变WiFi状态权限 - 允许应用开启/关闭WiFi，连接WiFi网络 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!-- 改变WiFi组播状态权限 - 允许应用接收WiFi组播数据包 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />

    <!-- 系统服务权限 -->
    <!-- 开机启动权限 - 允许应用在设备启动完成后自动运行 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!-- 唤醒锁权限 - 允许应用防止设备进入睡眠状态 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 禁用键盘锁权限 - 允许应用禁用设备锁屏 -->
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <!-- 前台服务权限 - 允许应用运行前台服务 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- 前台数据同步服务权限 - Android 14+必需，用于数据同步类型的前台服务 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <!-- NFC和其他系统权限 -->
    <!-- NFC权限 - 允许应用使用近场通信功能 -->
    <uses-permission android:name="android.permission.NFC" />
    <!-- 读取同步设置权限 - 允许应用读取账户同步设置 -->
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <!-- 写入同步设置权限 - 允许应用修改账户同步设置 -->
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <!-- 设置壁纸权限 - 允许应用设置系统壁纸 -->
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <!-- 读取订阅源权限 - 允许应用读取RSS等订阅内容（已废弃） -->
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
    <!-- 写入订阅源权限 - 允许应用写入RSS等订阅内容（已废弃） -->
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" />
    <!-- 使用凭据权限 - 允许应用使用账户管理器中的凭据 -->
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />

    <!-- 包查询权限 -->
    <!-- 查询所有包权限 - Android 11+必需，允许应用查询设备上安装的其他应用 -->
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <!-- APP更新相关权限 -->
    <!-- 请求安装包权限 - Android 8.0+必需，允许应用安装APK文件 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <!-- 静默下载权限 - 允许应用在后台下载文件而不显示通知 -->
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />

    <!-- 文件系统权限 -->
    <!-- 挂载/卸载文件系统权限 - 允许应用挂载和卸载可移动存储 -->
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <!-- 读取特权手机状态权限 - 系统级权限，允许读取详细的设备信息 -->
    <uses-permission
        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <!-- 读取手机号码权限 - 允许应用读取设备的电话号码 -->
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />

    <!-- 电源管理权限 -->
    <!-- 请求忽略电池优化权限 - 允许应用请求加入电池优化白名单 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- 应用程序配置 -->
    <!-- 允许应用数据备份，启用硬件加速提升图形性能，启用大内存堆适用于内存密集型应用 -->
    <!-- 请求传统外部存储访问方式，支持从右到左的语言布局，目标API级别31 -->
    <application
        android:name=".application.SmartSewingApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SmartSewingSystem"
        tools:targetApi="31">

        <!-- 讯飞语音SDK配置 - 用于语音识别和语音合成功能 -->
        <!-- 讯飞应用ID -->
        <meta-data
            android:name="com.iflytek.aikit.core.appid"
            android:value="@string/appId" />
        <!-- 讯飞API密钥 -->
        <meta-data
            android:name="com.iflytek.aikit.core.apikey"
            android:value="@string/apiKey" />
        <!-- 讯飞API密钥 -->
        <meta-data
            android:name="com.iflytek.aikit.core.apisecret"
            android:value="@string/apiSecret" />

        <!-- 主Activity - 应用程序入口 -->
        <!-- 配置变更时不重新创建Activity，允许其他应用启动此Activity -->
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true">

            <!-- 主启动器意图过滤器 -->
            <intent-filter>
                <!-- 主要动作 -->
                <action android:name="android.intent.action.MAIN" />
                <!-- 默认类别 -->
                <category android:name="android.intent.category.DEFAULT" />
                <!-- 主屏幕类别 -->
                <category android:name="android.intent.category.HOME" />
                <!-- 启动器类别 -->
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- USB设备连接意图过滤器 -->
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>

            <!-- USB设备过滤器配置 -->
            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/device_filter" />
        </activity>

        <!-- 条码扫描Activity -->
        <activity
            android:name=".barcode.BarcodeScanningActivity"
            android:label="条码扫码" />
        <!-- 二维码扫描Activity -->
        <activity
            android:name=".barcode.QRCodeScanningActivity"
            android:label="二维码扫码" />
        <!-- 人脸识别Activity -->
        <activity
            android:name=".face.FaceDetectionActivity"
            android:label="人脸识别" />
        <!-- 指纹识别Activity -->
        <!-- 不允许其他应用启动 -->
        <activity
            android:name=".finger.FingerprintActivity"
            android:exported="false"
            android:label="指纹识别" />
        <!-- 文字识别Activity -->
        <activity
            android:name=".ocr.TextRecognitionActivity"
            android:label="文字识别" />
        <!-- 串口通信Activity -->
        <activity
            android:name=".serial.SerialActivity"
            android:label="标准串口" />
        <!-- Modbus通信Activity -->
        <activity
            android:name=".modbus.ModbusActivity"
            android:label="标准串口-Modbus" />
        <!-- 语言设置Activity -->
        <!-- 不允许其他应用启动 -->
        <activity
            android:name=".lang.LanguageSettingsActivity"
            android:exported="false"
            android:label="@string/language_settings" />

        <!-- 语音聊天Activity -->
        <!-- 不允许其他应用启动，软键盘弹出时调整窗口大小 -->
        <activity
            android:name=".voice.example.ChatActivity"
            android:exported="false"
            android:label="@string/text_chat"
            android:windowSoftInputMode="adjustResize" />

        <!-- 语音输入Activity -->
        <!-- 不允许其他应用启动，软键盘弹出时调整窗口大小 -->
        <activity
            android:name=".voice.example.VoiceInputActivity"
            android:exported="false"
            android:label="@string/text_command"
            android:windowSoftInputMode="adjustResize" />
        
        <!-- APP更新Activity - 显示更新对话框 -->
        <!-- 不允许其他应用启动，使用对话框主题 -->
        <activity
            android:name=".update.UpgradeActivity"
            android:exported="false"
            android:label="应用更新"
            android:theme="@style/Theme.MaterialComponents.DayNight.Dialog" />

        <!-- APP更新下载服务 - 后台下载APK文件 -->
        <!-- 不允许其他应用绑定，前台服务类型：数据同步 -->
        <service
            android:name=".update.UpgradeService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- MQTT推送服务 - 保持长连接接收推送消息 -->
        <!-- 不允许其他应用绑定，前台服务类型：数据同步 -->
        <service
            android:name=".push.MqttPushService"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            android:stopWithTask="false" />

        <!-- 文件提供者 - 用于安全地共享APK文件给系统安装器 -->
        <!-- 不允许其他应用直接访问，允许授予URI权限 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <!-- 文件路径配置 -->
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 推送服务广播接收器 - 监听系统事件自动重启推送服务 -->
        <receiver
            android:name=".push.PushBootReceiver"
            android:exported="false"
            android:enabled="true">
            <intent-filter android:priority="1000">
                <!-- 开机完成广播 -->
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <!-- 应用更新完成广播 -->
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <!-- 网络状态变化广播 -->
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"
                    tools:ignore="BatteryLife" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>


        <!-- 禁用 Firebase Analytics -->
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="false" />

        <!-- 禁用 Firebase Crashlytics -->
        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />
    </application>

</manifest>
