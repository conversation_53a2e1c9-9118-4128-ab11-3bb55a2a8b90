plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id 'kotlin-kapt'
}

apply plugin: 'realm-android'

android {
    namespace 'com.supreme.smart.sewing'
    compileSdk 35

    defaultConfig {
        applicationId "com.supreme.smart.sewing"
        minSdk 30
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            //设置支持的 SO 库架构（开发者可以根据需要，选择一个或多个平台的so）
            abiFilters 'arm64-v8a', 'x86_64' // , 'arm64-v8a', 'x86', 'x86_64'
        }
        
        // 启用16KB页面大小支持
        externalNativeBuild {
            cmake {
                arguments "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }


    viewBinding {
        enable = true
    }

    dataBinding {
        enable = true
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'org/apache/http/version.properties'
        exclude 'org/apache/http/client/version.properties'
        exclude 'META-INF/io.netty.versions.properties'
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'lib/commons-logging-1.1.3.jar'
    }

    // 16KB页面大小兼容性配置
    lint {
        checkReleaseBuilds false
        abortOnError false
        // 启用16KB页面大小检查
        enable 'Aligned16KB'
        warning 'Aligned16KB'
    }

    // Gradle属性配置
    android.buildFeatures.buildConfig true
}

dependencies {
    // 系统库
    implementation libs.appcompat
    implementation libs.material
    implementation libs.core.ktx
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    // 标准串口
    implementation libs.yserialport
    // 标准串口工具库
    implementation libs.yutils
    // usb串口
    implementation libs.usb.serial.for1.android
    // RxJava依赖
    implementation libs.rxjava
    // RxAndroid依赖
    implementation libs.rxandroid
    // mlkit公共库
    implementation libs.mlkit.common
    // mlkit条码识别
    implementation libs.mlkit.barcode.scanning
    // mlkit人脸检测
    implementation libs.mlkit.face.detection
    // mlkit文字识别
    implementation libs.mlkit.text.recognition
    // 指纹识别依赖
    implementation libs.biometric
    // dialog
    implementation libs.app.dialog
    // okhttp
    implementation libs.okhttp
    // hutool
    implementation libs.hutool.all
    // utilcodex
    implementation libs.utilcodex
    // fastjson
    implementation libs.fastjson
    // commons.lang3
    implementation libs.commons.lang3
    // HIVEMQ MQTT Client
    implementation libs.hivemq.mqtt.client
    // andserver http server
    implementation libs.andserver
    // andserver http server processor
    kapt libs.andserver.processor
    // eventbus
    implementation libs.eventbus
    // glide
    implementation libs.glide
    // MPAndroidChart
    implementation libs.mpandroidchart
    // 鲁班压缩
    implementation libs.luban
    // 崩溃捕获
    implementation libs.xcrash.android.lib
    // phoenix
    implementation libs.process.phoenix
    // guava-retrying
    implementation libs.guava.retrying
    //------------- 分词器 ---------------
    // hanlp
    implementation libs.hanlp
    // TTS 文本转语音
    implementation libs.speech
    // 本地的依赖
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
}