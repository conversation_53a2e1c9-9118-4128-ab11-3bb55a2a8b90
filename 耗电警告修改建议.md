基于对代码的分析，我发现了几个导致前台服务耗电过快的主要问题。以下是我的电源优化建议：
🔋 前台服务电源优化建议
1. 线程池优化问题
   问题：使用了 Executors.newCachedThreadPool()
   这种线程池会无限制创建线程，在高并发情况下会消耗大量资源
   空闲线程会保持60秒才回收
   建议：
   改用固定大小的线程池：Executors.newFixedThreadPool(2-4)
   或使用单线程池：Executors.newSingleThreadExecutor()
   设置合理的线程存活时间
2. 网络监听过于频繁
   问题：网络状态监听器会频繁触发回调
   每次网络能力变化都会触发回调
   网络恢复时有2秒延迟重连，但仍可能频繁触发
   建议：
   增加网络状态变化的防抖机制（debounce）
   限制重连尝试频率，避免短时间内多次重连
   考虑使用指数退避算法
3. MQTT心跳机制优化
   问题：当前keepAlive设置为60秒
   对于移动设备来说可能过于频繁
   在网络不稳定时会增加电量消耗
   建议：
   将keepAlive调整为120-300秒
   根据应用场景调整，非实时性要求高的可以设置更长
   考虑在应用后台时使用更长的心跳间隔
4. Handler延迟任务累积
   问题：多处使用Handler.postDelayed()
   PushBootReceiver中5秒延迟检查
   服务启动时1秒延迟
   网络恢复时2秒延迟重连
   建议：
   在服务销毁时清理所有pending的Handler任务
   使用WeakReference避免内存泄漏
   合并相似的延迟任务
5. 前台通知优化
   问题：通知更新可能过于频繁
   每次网络状态变化都更新通知
   没有限制通知更新频率
   建议：
   限制通知更新频率（如最多每30秒更新一次）
   只在关键状态变化时更新通知
   使用静默通知，避免声音和震动
6. 服务重启策略优化
   问题：使用START_STICKY可能导致频繁重启
   系统资源紧张时会频繁杀死和重启服务
   广播接收器会在多种情况下重启服务
   建议：
   考虑使用START_REDELIVER_INTENT
   增加服务重启的时间间隔限制
   在低电量模式下降低服务活跃度
7. 电池优化适配
   建议：
   检测设备是否处于省电模式，相应调整服务行为
   在Doze模式下使用更长的心跳间隔
   适配不同厂商的电池优化策略
8. 资源释放优化
   问题：可能存在资源未及时释放
   ExecutorService需要正确关闭
   NetworkCallback需要及时注销
   建议：
   在onDestroy()中确保所有资源都被释放
   使用try-with-resources或finally块确保资源释放
   定期检查和清理无用的连接和监听器
9. 智能休眠策略
   建议：
   在应用长时间未使用时，降低服务活跃度
   夜间时段使用更长的心跳间隔
   根据用户使用模式调整服务行为
10. 监控和诊断
    建议：
    添加电量消耗监控日志
    记录服务重启次数和原因
    监控网络连接状态变化频率
    这些优化措施可以显著降低前台服务的电量消耗，同时保持推送功能的可靠性。建议按优先级逐步实施这些优化。